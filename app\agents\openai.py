from typing import Optional
from langchain_openai import ChatOpenAI
from app.llm_factory import LLMFactory
import logging
import os
from pathlib import Path

from app.agents.base import BaseAgent
from app.config import AgentConfig

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    ConfigurationException, ExternalServiceException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()

# Set layer context for agent operations
set_layer_context("business_logic")


class OpenAIAgent(BaseAgent):
    """OpenAI对话流程控制Agent，继承自BaseAgent"""

    @log_and_reraise(logging.getLogger(__name__), "OpenAI agent initialization")
    def __init__(self, config: AgentConfig):
        """初始化OpenAI Agent with unified error handling

        Args:
            config: 包含所有配置参数的AgentConfig对象
        """
        set_operation_context("openai_agent_init")

        with error_boundary("agent configuration", LayerType.BUSINESS_LOGIC):
            if not config:
                raise ValidationException(
                    message="Agent configuration is required",
                    field_name="config",
                    details="Configuration cannot be None",
                    suggested_action="Provide a valid AgentConfig object"
                )

            super().__init__(config)
            self.report_year = None
            self.company_name = None

        with error_boundary("LLM initialization", LayerType.BUSINESS_LOGIC):
            try:
                self.llm = LLMFactory.create_llm(
                    model_name=self.config.model_name,
                    temperature=self.config.temperature,
                    api_key=self.config.api_key,
                    api_base=self.config.api_base
                )
                logging.info(f"Initializing OpenAIAgent for workspace {self.config.workspace_id}")
                self.workspace_id = self.config.workspace_id

            except Exception as e:
                raise ExternalServiceException(
                    message="Failed to initialize LLM for OpenAI agent",
                    service_name="LLM_Factory",
                    details=f"LLM initialization error: {str(e)}",
                    original_exception=e,
                    context={
                        'model_name': self.config.model_name,
                        'api_base': self.config.api_base,
                        'workspace_id': self.config.workspace_id
                    },
                    suggested_action="Check LLM configuration and API connectivity"
                )

    @log_and_reraise(logging.getLogger(__name__), "message processing")
    def process_message(self, message: str,
                        current_company: Optional[str] = None,
                        current_year: Optional[str] = None,
                        current_template: Optional[str] = None) -> dict:
        """实现基类要求的消息处理方法 with unified error handling"""
        set_operation_context("openai_message_processing")

        with error_boundary("message validation", LayerType.BUSINESS_LOGIC):
            if not message:
                raise ValidationException(
                    message="Empty message provided",
                    field_name="message",
                    details="Message cannot be empty or None",
                    suggested_action="Provide a valid message string"
                )

            logging.debug(f"开始处理用户消息: {message}")
            logging.debug(f"当前状态 - 公司: {current_company}, 年份: {current_year}, 模板: {current_template}")

            self.company_name = current_company
            self.report_year = current_year

        with error_boundary("four elements handling", LayerType.BUSINESS_LOGIC):
            try:
                processed_message = self._handle_four_elements(message)

                result = {
                    "company": current_company,
                    "year": current_year,
                    "template": current_template,
                    "message": processed_message,
                    "status": "success"
                }

                logging.debug(f"process_message消息处理完成, 结果: {result}")
                return result

            except Exception as e:
                raise ValidationException(
                    message="Four elements validation failed",
                    field_name="four_elements",
                    details=f"Validation error: {str(e)}",
                    original_exception=e,
                    context={
                        'company': current_company,
                        'year': current_year,
                        'template': current_template
                    },
                    suggested_action="Check company name, year, and workspace setup"
                )

    @log_and_reraise(logging.getLogger(__name__), "four elements validation")
    def _handle_four_elements(self, message: str) -> str:
        """处理四要素验证核心逻辑 with unified error handling"""
        set_operation_context("four_elements_validation")
        logging.debug(f"开始验证四要素, 消息内容: {message}")

        with error_boundary("workspace path validation", LayerType.BUSINESS_LOGIC):
            if not self.workspace_id or not self.company_name or not self.report_year:
                raise ValidationException(
                    message="Missing required parameters for workspace validation",
                    field_name="workspace_parameters",
                    details="workspace_id, company_name, and report_year are required",
                    context={
                        'workspace_id': self.workspace_id,
                        'company_name': self.company_name,
                        'report_year': self.report_year
                    },
                    suggested_action="Ensure all workspace parameters are set"
                )

            workspace_path = f"workspaces/{self.workspace_id}/{self.company_name}/{self.report_year}"
            logging.debug(f"检查工作区路径: {workspace_path}")

            if not os.path.exists(workspace_path):
                error_msg = f"目录 {workspace_path} 不存在，请先确认公司名称和年份"
                logging.warning(error_msg)
                return error_msg

        with error_boundary("file validation", LayerType.BUSINESS_LOGIC):
            try:
                valid_files = [f for f in os.listdir(workspace_path)
                               if f.lower().endswith(('.xls', '.xlsx', '.pdf'))]

                if not valid_files:
                    error_msg = f"检测到以下目录缺少资料文件：\n{workspace_path}\n请上传PDF/Excel文件或输入'无需资料'确认继续"
                    logging.warning(error_msg)
                    return error_msg

                success_msg = f"✅ 四要素验证通过\n已找到资料文件：{', '.join(valid_files)}"
                logging.info(success_msg)
                return success_msg

            except (OSError, PermissionError) as e:
                raise ValidationException(
                    message="Failed to access workspace directory",
                    field_name="workspace_access",
                    details=f"Directory access error: {str(e)}",
                    original_exception=e,
                    context={'workspace_path': workspace_path},
                    suggested_action="Check directory permissions and existence"
                )
