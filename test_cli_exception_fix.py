#!/usr/bin/env python3
"""
Test script to verify the CLIException KeyError fix and related exception classes.

This test specifically targets the scenario that was causing KeyError: 'suggested_action'
and ensures that all exception classes with hard-coded suggested actions work correctly.
"""

import sys
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.exceptions import (
    CLIException, AuthenticationException, UIException,
    RAGWorkflowException, ReportWorkflowException
)
from crawler.exceptions import AccountRestrictedException
from app.logging_config import setup_logging

# Setup logging
setup_logging()

def test_cli_exception_scenarios():
    """Test CLIException with various argument patterns"""
    print("🧪 Testing CLIException scenarios...")
    
    try:
        # Test 1: Basic instantiation (should use default suggested action)
        exc1 = CLIException("Test CLI error")
        assert exc1.message == "Test CLI error"
        assert exc1.error_code == "CLI_001"
        assert exc1.suggested_action == "Please check command syntax and arguments"
        print("   ✅ Basic instantiation works")
        
        # Test 2: Custom suggested action
        exc2 = CLIException(
            "Test CLI error with custom action",
            suggested_action="Custom action advice"
        )
        assert exc2.message == "Test CLI error with custom action"
        assert exc2.suggested_action == "Custom action advice"
        print("   ✅ Custom suggested action works")
        
        # Test 3: The problematic scenario from engine.py
        exc3 = CLIException(
            message="Command execution failed",
            details="Execution error: test error",
            context={'command': 'test_command'},
            suggested_action="Check command arguments and system state"
        )
        assert exc3.message == "Command execution failed"
        assert exc3.details == "Execution error: test error"
        assert exc3.context['command'] == 'test_command'
        assert exc3.suggested_action == "Check command arguments and system state"
        print("   ✅ Engine.py scenario works")
        
        # Test 4: With additional kwargs but no custom suggested action
        exc4 = CLIException(
            "Test with kwargs",
            details="Additional details",
            context={'key': 'value'}
        )
        assert exc4.message == "Test with kwargs"
        assert exc4.details == "Additional details"
        assert exc4.context['key'] == 'value'
        assert exc4.suggested_action == "Please check command syntax and arguments"
        print("   ✅ Additional kwargs without custom suggested action works")
        
        return True
        
    except Exception as e:
        print(f"   ❌ CLIException test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_authentication_exception_scenarios():
    """Test AuthenticationException with various argument patterns"""
    print("\n🧪 Testing AuthenticationException scenarios...")
    
    try:
        # Test 1: Basic instantiation (should use default message and suggested action)
        exc1 = AuthenticationException()
        assert exc1.message == "Authentication failed"
        assert exc1.error_code == "AUTH_001"
        assert exc1.suggested_action == "Please check credentials and try again"
        print("   ✅ Basic instantiation works")
        
        # Test 2: Custom message
        exc2 = AuthenticationException("Custom auth error")
        assert exc2.message == "Custom auth error"
        assert exc2.suggested_action == "Please check credentials and try again"
        print("   ✅ Custom message works")
        
        # Test 3: Custom suggested action
        exc3 = AuthenticationException(
            "Auth failed",
            suggested_action="Contact administrator"
        )
        assert exc3.message == "Auth failed"
        assert exc3.suggested_action == "Contact administrator"
        print("   ✅ Custom suggested action works")
        
        # Test 4: With additional kwargs
        exc4 = AuthenticationException(
            "Auth error with context",
            details="Invalid token",
            context={'user_id': 'test_user'},
            suggested_action="Refresh your token"
        )
        assert exc4.message == "Auth error with context"
        assert exc4.details == "Invalid token"
        assert exc4.context['user_id'] == 'test_user'
        assert exc4.suggested_action == "Refresh your token"
        print("   ✅ Full kwargs scenario works")
        
        return True
        
    except Exception as e:
        print(f"   ❌ AuthenticationException test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_exception_scenarios():
    """Test UIException with various argument patterns"""
    print("\n🧪 Testing UIException scenarios...")
    
    try:
        # Test 1: Basic instantiation (should use default suggested action)
        exc1 = UIException("UI error occurred")
        assert exc1.message == "UI error occurred"
        assert exc1.error_code == "UI_001"
        assert exc1.suggested_action == "Please refresh the page and try again"
        print("   ✅ Basic instantiation works")
        
        # Test 2: Custom suggested action
        exc2 = UIException(
            "UI component failed",
            suggested_action="Clear browser cache and retry"
        )
        assert exc2.message == "UI component failed"
        assert exc2.suggested_action == "Clear browser cache and retry"
        print("   ✅ Custom suggested action works")
        
        # Test 3: With additional kwargs
        exc3 = UIException(
            "UI rendering error",
            details="Component not found",
            context={'component': 'navbar'},
            suggested_action="Check component configuration"
        )
        assert exc3.message == "UI rendering error"
        assert exc3.details == "Component not found"
        assert exc3.context['component'] == 'navbar'
        assert exc3.suggested_action == "Check component configuration"
        print("   ✅ Full kwargs scenario works")
        
        return True
        
    except Exception as e:
        print(f"   ❌ UIException test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_engine_integration_scenario():
    """Test the exact scenario from app/cli/engine.py that was causing the KeyError"""
    print("\n🧪 Testing engine.py integration scenario...")
    
    try:
        # This is the exact pattern from engine.py line 95-101
        exc = CLIException(
            message="Command execution failed",
            details=f"Execution error: test error",
            original_exception=Exception("test error"),
            context={'command': 'test_command'},
            suggested_action="Check command arguments and system state"
        )
        
        assert exc.message == "Command execution failed"
        assert exc.details == "Execution error: test error"
        assert exc.context['command'] == 'test_command'
        assert exc.suggested_action == "Check command arguments and system state"
        assert exc.original_exception is not None
        print("   ✅ Engine.py integration scenario works perfectly")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Engine integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_exceptions():
    """Test workflow exception classes with explicit parameter handling"""
    print("\n🧪 Testing workflow exception scenarios...")

    try:
        # Test 1: RAGWorkflowException with defaults
        exc1 = RAGWorkflowException("RAG processing failed")
        assert exc1.message == "RAG processing failed"
        assert exc1.error_code == "RAG_001"
        assert exc1.context['workflow_name'] == "RAGWorkflow"
        print("   ✅ RAGWorkflowException basic instantiation works")

        # Test 2: RAGWorkflowException with custom error_code
        exc2 = RAGWorkflowException(
            "RAG custom error",
            step="document_processing",
            error_code="CUSTOM_RAG_001"
        )
        assert exc2.message == "RAG custom error"
        assert exc2.error_code == "CUSTOM_RAG_001"
        assert exc2.context['step'] == "document_processing"
        print("   ✅ RAGWorkflowException custom error_code works")

        # Test 3: ReportWorkflowException with defaults
        exc3 = ReportWorkflowException("Report generation failed")
        assert exc3.message == "Report generation failed"
        assert exc3.error_code == "RPT_001"
        assert exc3.context['workflow_name'] == "ReportWorkflow"
        print("   ✅ ReportWorkflowException basic instantiation works")

        # Test 4: ReportWorkflowException with custom parameters
        exc4 = ReportWorkflowException(
            "Report custom error",
            step="data_aggregation",
            error_code="CUSTOM_RPT_001",
            suggested_action="Check data sources"
        )
        assert exc4.message == "Report custom error"
        assert exc4.error_code == "CUSTOM_RPT_001"
        assert exc4.context['step'] == "data_aggregation"
        assert exc4.suggested_action == "Check data sources"
        print("   ✅ ReportWorkflowException custom parameters work")

        return True

    except Exception as e:
        print(f"   ❌ Workflow exceptions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crawler_exceptions():
    """Test crawler exception classes with explicit parameter handling"""
    print("\n🧪 Testing crawler exception scenarios...")

    try:
        # Test 1: AccountRestrictedException with defaults
        exc1 = AccountRestrictedException()
        assert exc1.message == "Account access restricted"
        assert exc1.error_code == "CRAWLER_ACCOUNT_001"
        assert exc1.context['restriction_type'] == "unknown"
        assert exc1.suggested_action == "Contact administrator for account review"
        print("   ✅ AccountRestrictedException basic instantiation works")

        # Test 2: AccountRestrictedException with custom parameters
        exc2 = AccountRestrictedException(
            message="Account temporarily suspended",
            restriction_type="rate_limit",
            error_code="CUSTOM_ACCOUNT_001",
            suggested_action="Wait 24 hours before retrying"
        )
        assert exc2.message == "Account temporarily suspended"
        assert exc2.error_code == "CUSTOM_ACCOUNT_001"
        assert exc2.context['restriction_type'] == "rate_limit"
        assert exc2.suggested_action == "Wait 24 hours before retrying"
        print("   ✅ AccountRestrictedException custom parameters work")

        return True

    except Exception as e:
        print(f"   ❌ Crawler exceptions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """Test that existing code patterns still work"""
    print("\n🧪 Testing backward compatibility...")

    try:
        # Test patterns that should continue to work

        # Simple CLIException
        exc1 = CLIException("Simple error")
        assert exc1.suggested_action == "Please check command syntax and arguments"

        # AuthenticationException with defaults
        exc2 = AuthenticationException()
        assert exc2.message == "Authentication failed"
        assert exc2.suggested_action == "Please check credentials and try again"

        # UIException with basic message
        exc3 = UIException("UI problem")
        assert exc3.suggested_action == "Please refresh the page and try again"

        # Workflow exceptions with basic usage
        exc4 = RAGWorkflowException("RAG error")
        assert exc4.error_code == "RAG_001"

        exc5 = ReportWorkflowException("Report error")
        assert exc5.error_code == "RPT_001"

        # Crawler exception with basic usage
        exc6 = AccountRestrictedException()
        assert exc6.error_code == "CRAWLER_ACCOUNT_001"

        print("   ✅ All backward compatibility tests passed")
        return True

    except Exception as e:
        print(f"   ❌ Backward compatibility test failed: {e}")
        return False

def main():
    """Run all CLIException fix tests"""
    print("🚀 Testing CLIException KeyError Fix")
    print("=" * 50)
    
    tests = [
        ("CLIException Scenarios", test_cli_exception_scenarios),
        ("AuthenticationException Scenarios", test_authentication_exception_scenarios),
        ("UIException Scenarios", test_ui_exception_scenarios),
        ("Engine Integration Scenario", test_engine_integration_scenario),
        ("Workflow Exception Scenarios", test_workflow_exceptions),
        ("Crawler Exception Scenarios", test_crawler_exceptions),
        ("Backward Compatibility", test_backward_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All CLIException fixes working correctly!")
        print("✅ CLIException KeyError issue resolved")
        print("✅ AuthenticationException explicit parameter handling works")
        print("✅ UIException explicit parameter handling works")
        print("✅ Engine.py integration scenario works")
        print("✅ Backward compatibility maintained")
        return 0
    else:
        print("\n❌ Some CLIException fixes need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())
