import os
from pathlib import Path
import re
import logging
from typing import Any, Dict, List, Tuple
from app.utils.reform import MarkdownTableR<PERSON>ormer
from app.utils.reheader import MarkdownTableHeaderAdjuster

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    FileProcessingException, DataProcessingException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

@log_and_reraise(logger, "filename parsing")
def parse_filename(filename: str) -> Tuple[str, str, str, int]:
    """
    解析文件名，返回(前部, sheet_name, cell_coordinate, start_row) with unified error handling
    """
    set_operation_context("filename_parsing")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if not filename:
            raise ValidationException(
                message="Empty filename provided",
                field_name="filename",
                details="Filename cannot be empty or None",
                suggested_action="Provide a valid filename string"
            )

        if not isinstance(filename, str):
            raise ValidationException(
                message="Filename must be a string",
                field_name="filename",
                details=f"Expected string, got {type(filename)}",
                context={'input_type': type(filename).__name__},
                suggested_action="Provide a valid filename string"
            )

    logger.debug(f"Entering parse_filename: filename={filename}")

    # Parse filename pattern
    with error_boundary("filename pattern matching", LayerType.UTILITY):
        try:
            # 匹配格式：前部_Cell坐标_Sheet Name.md
            pattern = r"^(.*?)_([A-Za-z]+\d+[A-Za-z]+\d*)_(.*?)\.md$"
            match = re.match(pattern, filename)
            if not match:
                logger.debug("No match found for filename pattern")
                return None

            prefix = match.group(1)
            cell = match.group(2)
            sheet_name = match.group(3)

        except re.error as e:
            raise DataProcessingException(
                message="Failed to parse filename with regex",
                details=f"Regex error: {str(e)}",
                original_exception=e,
                context={'filename': filename, 'pattern': pattern},
                suggested_action="Check filename format and regex pattern"
            )

    # Extract row number
    with error_boundary("row number extraction", LayerType.UTILITY):
        try:
            # 提取起始行号
            row_match = re.search(r"(\d+)", cell)
            start_row = int(row_match.group(1)) if row_match else 0

            # 记录解析结果
            logger.debug(f"Parsed filename: prefix={prefix}, sheet_name={sheet_name}, cell={cell}, start_row={start_row}")

            # 检查解析结果是否有效
            if not prefix or not cell or not sheet_name:
                logger.warning(f"Invalid parse result for filename: {filename}")
                return None

            return (prefix, sheet_name, cell, start_row)

        except (ValueError, AttributeError) as e:
            raise DataProcessingException(
                message="Failed to extract row number from cell coordinate",
                details=f"Row extraction error: {str(e)}",
                original_exception=e,
                context={'filename': filename, 'cell': cell},
                suggested_action="Check cell coordinate format in filename"
            )

@log_and_reraise(logger, "file grouping")
def group_files(file_list: List[str]) -> Dict[str, List[str]]:
    """
    扫描目录并按照规则分组文件 with unified error handling
    """
    set_operation_context("file_grouping")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if file_list is None:
            raise ValidationException(
                message="File list cannot be None",
                field_name="file_list",
                details="File list parameter is None",
                suggested_action="Provide a valid list of filenames"
            )

        if not isinstance(file_list, list):
            raise ValidationException(
                message="File list must be a list",
                field_name="file_list",
                details=f"Expected list, got {type(file_list)}",
                context={'input_type': type(file_list).__name__},
                suggested_action="Provide a valid list of filenames"
            )

    logger.debug(f"Entering group_files with {len(file_list)} files")

    # Process files
    with error_boundary("file processing", LayerType.UTILITY):
        try:
            groups = {}

            for filename in file_list:
                try:
                    parsed = parse_filename(filename)
                    if not parsed:
                        logger.debug(f"Skipping file: {filename} - could not parse")
                        continue

                    prefix, sheet_name, cell, _ = parsed

                    # 提取起始列和结束列（如A1H3中的A和H）
                    col_match = re.match(r"^([A-Za-z]+)(\d+)([A-Za-z]+)(\d*)$", cell)
                    if not col_match:
                        logger.debug(f"Skipping file: {filename} - invalid cell format")
                        continue

                    start_col = col_match.group(1)
                    start_row = col_match.group(2)
                    end_col = col_match.group(3)
                    end_row = col_match.group(4)

                    # 组合分组键：前部 + sheet_name + 起始列 + 结束列
                    group_key = (prefix, sheet_name, start_col, end_col)

                    if group_key not in groups:
                        groups[group_key] = []

                    groups[group_key].append((filename, cell, start_row, end_row))
                    logger.debug(f"Added file {filename} to group {group_key}")

                except Exception as e:
                    logger.warning(f"Error processing file {filename}: {str(e)}")
                    continue

            # 过滤掉数量小于2的分组
            filtered_groups = {k: v for k, v in groups.items() if len(v) >= 2}
            logger.debug(f"Found {len(filtered_groups)} valid groups after filtering (min 2 files per group)")

            # 转换为要求的输出格式：{前部: [文件列表]}
            result = {}
            for (prefix, _, s, e), files in filtered_groups.items():
                key = "".join((prefix, s, e))
                if key not in result:
                    result[key] = []

                sorted_files = sort_files(files)
                logger.debug(f"Sorted {len(sorted_files)} files for group {key}")

                # Check if all files in group have row span > 5
                all_large_spans = True
                for _, start_row, end_row in sorted_files:
                    if (end_row - start_row) < 5:
                        all_large_spans = False
                        break

                if all_large_spans:
                    logger.debug(f"Skipping group {key} - all files have large spans (>5 rows)")
                    continue

                prev_end_row = None
                for filename, start_row, end_row in sorted_files:
                    if prev_end_row is not None:
                        distance = start_row - prev_end_row
                        if distance > 3:
                            logger.debug(f"Skipping file {filename} in group {key} - large row gap ({distance} rows)")
                            continue

                    result[key].append(filename)
                    prev_end_row = end_row
                    logger.debug(f"Added file {filename} to final group {key}")

            # 过滤掉数量小于2的分组
            final_result = {k: v for k, v in result.items() if len(v) >= 2}
            logger.debug(f"Final grouping result: {len(final_result)} groups with at least 2 files each")
            return final_result

        except Exception as e:
            raise DataProcessingException(
                message="Failed to process file grouping",
                details=f"File grouping error: {str(e)}",
                original_exception=e,
                context={'file_count': len(file_list)},
                suggested_action="Check file naming patterns and format"
            )

@log_and_reraise(logger, "file sorting")
def sort_files(file_list: List[Tuple[str, str, str, str]]) -> List[Tuple[str, int, int]]:
    """
    按照起始行号排序文件，并返回文件名、起始行和结束行 with unified error handling
    """
    set_operation_context("file_sorting")
    
    with error_boundary("input validation", LayerType.UTILITY):
        if file_list is None:
            raise ValidationException(
                message="File list cannot be None",
                field_name="file_list",
                details="File list parameter is None",
                suggested_action="Provide a valid list of file tuples"
            )
        
        if not isinstance(file_list, list):
            raise ValidationException(
                message="File list must be a list",
                field_name="file_list",
                details=f"Expected list, got {type(file_list)}",
                context={'input_type': type(file_list).__name__},
                suggested_action="Provide a valid list of file tuples"
            )
    
    with error_boundary("file sorting operation", LayerType.UTILITY):
        try:
            logger.debug(f"Sorting {len(file_list)} files by start row")
            
            # Validate each item in the list
            for i, item in enumerate(file_list):
                if not isinstance(item, tuple) or len(item) != 4:
                    raise ValidationException(
                        message="Invalid file tuple format",
                        field_name=f"file_list[{i}]",
                        details=f"Expected tuple of length 4, got {type(item)} with length {len(item) if hasattr(item, '__len__') else 'unknown'}",
                        context={'index': i, 'item': str(item)},
                        suggested_action="Ensure all items are tuples with 4 elements: (filename, cell, start_row, end_row)"
                    )
                
                # Validate start_row and end_row can be converted to integers
                try:
                    int(item[2])
                    int(item[3])
                except (ValueError, TypeError) as e:
                    raise ValidationException(
                        message="Invalid row numbers in file tuple",
                        field_name=f"file_list[{i}]",
                        details=f"Cannot convert row numbers to integers: {str(e)}",
                        context={'index': i, 'item': str(item)},
                        suggested_action="Ensure start_row and end_row are valid integers"
                    )
            
            result = [(item[0], int(item[2]), int(item[3])) for item in sorted(file_list, key=lambda x: int(x[2]))]
            logger.debug(f"Successfully sorted {len(result)} files")
            return result
            
        except (ValueError, IndexError) as e:
            raise DataProcessingException(
                message="Failed to sort files due to invalid data format",
                details=f"Error parsing file data: {str(e)}",
                original_exception=e,
                context={'file_list_length': len(file_list)},
                suggested_action="Check file tuple format and ensure all elements are valid"
            )
        except Exception as e:
            raise DataProcessingException(
                message="Unexpected error during file sorting",
                details=str(e),
                original_exception=e,
                context={'file_list_length': len(file_list)},
                suggested_action="Check file list format and data integrity"
            )

@log_and_reraise(logger, "file merging")
def merge_files(file1: str, file2: str, output_file: str, delete: bool = False) -> str:
    """
    合并两个文件，精确处理分割行和内容拼接 with unified error handling
    delete: 是否在合并后删除原文件
    """
    set_operation_context("file_merging")
    
    with error_boundary("input validation", LayerType.UTILITY):
        if not file1 or not isinstance(file1, str):
            raise ValidationException(
                message="First file path must be a non-empty string",
                field_name="file1",
                details=f"Invalid file1 type: {type(file1)}",
                context={'file1': str(file1)},
                suggested_action="Provide a valid file path string"
            )
        
        if not file2 or not isinstance(file2, str):
            raise ValidationException(
                message="Second file path must be a non-empty string",
                field_name="file2",
                details=f"Invalid file2 type: {type(file2)}",
                context={'file2': str(file2)},
                suggested_action="Provide a valid file path string"
            )
        
        if not output_file or not isinstance(output_file, str):
            raise ValidationException(
                message="Output file path must be a non-empty string",
                field_name="output_file",
                details=f"Invalid output_file type: {type(output_file)}",
                context={'output_file': str(output_file)},
                suggested_action="Provide a valid output file path string"
            )
        
        if not isinstance(delete, bool):
            raise ValidationException(
                message="Delete parameter must be a boolean",
                field_name="delete",
                details=f"Expected bool, got {type(delete)}",
                context={'delete_type': type(delete).__name__},
                suggested_action="Provide a boolean value for delete parameter"
            )
    
    logger.debug(f"Merging files: {file1} and {file2} into {output_file}")
    
    with error_boundary("file access validation", LayerType.UTILITY):
        # Validate file existence
        if not os.path.exists(file1):
            raise FileProcessingException(
                message="First source file not found",
                file_path=file1,
                context={'file_path': file1, 'operation': 'file_access'},
                suggested_action="Check if the first source file exists"
            )

        if not os.path.exists(file2):
            raise FileProcessingException(
                message="Second source file not found",
                file_path=file2,
                context={'file_path': file2, 'operation': 'file_access'},
                suggested_action="Check if the second source file exists"
            )
        
        # Validate file readability
        if not os.access(file1, os.R_OK):
            raise FileProcessingException(
                message="Cannot read first source file",
                file_path=file1,
                operation="file_access",
                context={'file_path': file1},
                suggested_action="Check file permissions"
            )
        
        if not os.access(file2, os.R_OK):
            raise FileProcessingException(
                message="Cannot read second source file",
                file_path=file2,
                operation="file_access",
                context={'file_path': file2},
                suggested_action="Check file permissions"
            )
    
    with error_boundary("file reading", LayerType.UTILITY):
        try:
            # 读取第一个文件内容并清理
            with open(file1, 'r', encoding='utf-8') as f:
                content1 = f.read().strip()
            logger.debug(f"Read {len(content1)} characters from {file1}")
            
            if not content1:
                raise DataProcessingException(
                    message="First file is empty",
                    data_type="file_content",
                    context={'file_path': file1},
                    suggested_action="Check if the first file contains content"
                )
            
            # 读取第二个文件内容
            with open(file2, 'r', encoding='utf-8') as f:
                content2 = f.read()
            logger.debug(f"Read {len(content2)} characters from {file2}")
            
            if not content2:
                logger.warning(f"Second file {file2} is empty - will merge empty content")
                
        except UnicodeDecodeError as e:
            raise FileProcessingException(
                message="Cannot read file due to encoding issues",
                file_path=file1 if 'content1' not in locals() else file2,
                details=str(e),
                original_exception=e,
                context={'operation': 'read'},
                suggested_action="Ensure files are saved in UTF-8 encoding"
            )
        except Exception as e:
            raise FileProcessingException(
                message="Failed to read source files",
                file_path=file1 if 'content1' not in locals() else file2,
                details=str(e),
                original_exception=e,
                context={'operation': 'read'},
                suggested_action="Check file permissions and accessibility"
            )
    
    with error_boundary("content processing", LayerType.UTILITY):
        try:
            # 查找第二个文件中的表格分割行
            split_lines = content2.split('\n')
            content_start = 0
            separator_found = False
            
            for i, line in enumerate(split_lines):
                # 匹配任意列数的Markdown表格分割行，如 |---|, |---|---| 或 | --- | --- | --- |
                if re.match(r'^\|([\s-]+\|)+$', line.strip()) and line.find('-') > -1:
                    content_start = i + 1
                    separator_found = True
                    logger.debug(f"Found table separator at line {i}")
                    break
            
            if not separator_found:
                logger.debug("No table separator found in second file - using full content")
            
            # 提取有效内容并清理
            valid_content = '\n'.join(split_lines[content_start:]).strip()
            logger.debug(f"Extracted {len(valid_content)} characters of valid content from second file")
            
            # 合并内容，确保无缝衔接
            merged_content = content1
            if valid_content:
                merged_content += '\n' + valid_content
            logger.debug(f"Created merged content of {len(merged_content)} characters")
            
        except Exception as e:
            raise DataProcessingException(
                message="Failed to process file content",
                data_type="content_processing",
                details=str(e),
                original_exception=e,
                suggested_action="Check file content format and structure"
            )
    
    with error_boundary("file writing", LayerType.UTILITY):
        try:
            # 确保 output directory exists
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            
            # 写入输出文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(merged_content)
            logger.info(f"Successfully wrote merged content to {output_file}")
            
        except Exception as e:
            raise FileProcessingException(
                message="Failed to write output file",
                file_path=output_file,
                operation="write",
                details=str(e),
                original_exception=e,
                suggested_action="Check directory permissions and disk space"
            )
    
    # 如果需要删除原文件
    if delete:
        with error_boundary("file deletion", LayerType.UTILITY):
            try:
                if os.path.exists(file1):
                    os.remove(file1)
                    logger.debug(f"Deleted original file: {file1}")
                else:
                    logger.warning(f"Original file not found for deletion: {file1}")
                
                if os.path.exists(file2):
                    os.remove(file2)
                    logger.debug(f"Deleted original file: {file2}")
                else:
                    logger.warning(f"Original file not found for deletion: {file2}")
                    
                logger.info(f"Deleted original files: {file1} and {file2}")
                
            except OSError as e:
                raise FileProcessingException(
                    message="Failed to delete original files",
                    file_path=f"{file1}, {file2}",
                    operation="delete",
                    details=str(e),
                    original_exception=e,
                    suggested_action="Check file permissions and ensure files are not in use"
                )
    
    return output_file

@handle_layer_boundary(LayerType.UTILITY, "pipe operation")
def pipe(directory: str, output_dir: str, delete: bool = False, no_reheader: bool = False, no_reform: bool = False):
    """
    管道操作：改进后的分组合并处理，集成reheader和reform功能 with unified error handling
    基于merge_files函数实现合并逻辑，然后依次执行reheader和reform处理
    delete: 是否在合并后删除原文件
    """
    set_operation_context("pipe_operation")
    
    with error_boundary("input validation", LayerType.UTILITY):
        if not directory or not isinstance(directory, str):
            raise ValidationException(
                message="Directory path must be a non-empty string",
                field_name="directory",
                details=f"Invalid directory type: {type(directory)}",
                context={'directory': str(directory)},
                suggested_action="Provide a valid directory path string"
            )
        
        if not output_dir or not isinstance(output_dir, str):
            raise ValidationException(
                message="Output directory path must be a non-empty string",
                field_name="output_dir",
                details=f"Invalid output_dir type: {type(output_dir)}",
                context={'output_dir': str(output_dir)},
                suggested_action="Provide a valid output directory path string"
            )
        
        if not isinstance(delete, bool):
            raise ValidationException(
                message="Delete parameter must be a boolean",
                field_name="delete",
                details=f"Expected bool, got {type(delete)}",
                context={'delete_type': type(delete).__name__},
                suggested_action="Provide a boolean value for delete parameter"
            )
        
        if not isinstance(no_reheader, bool):
            raise ValidationException(
                message="no_reheader parameter must be a boolean",
                field_name="no_reheader",
                details=f"Expected bool, got {type(no_reheader)}",
                context={'no_reheader_type': type(no_reheader).__name__},
                suggested_action="Provide a boolean value for no_reheader parameter"
            )
        
        if not isinstance(no_reform, bool):
            raise ValidationException(
                message="no_reform parameter must be a boolean",
                field_name="no_reform",
                details=f"Expected bool, got {type(no_reform)}",
                context={'no_reform_type': type(no_reform).__name__},
                suggested_action="Provide a boolean value for no_reform parameter"
            )
    
    with error_boundary("directory setup", LayerType.UTILITY):
        # Validate source directory
        if not os.path.exists(directory):
            raise FileProcessingException(
                message="Source directory does not exist",
                file_path=directory,
                context={'directory': directory, 'operation': 'directory_access'},
                suggested_action="Check the source directory path and ensure it exists"
            )
        
        if not os.path.isdir(directory):
            raise ValidationException(
                message="Source path is not a directory",
                field_name="directory",
                details=f"Path exists but is not a directory: {directory}",
                context={'directory': directory},
                suggested_action="Provide a valid directory path"
            )
        
        # Ensure output directory exists
        try:
            if not os.path.exists(output_dir):
                logger.debug(f"Creating output directory: {output_dir}")
                os.makedirs(output_dir, exist_ok=True)
        except OSError as e:
            raise FileProcessingException(
                message="Failed to create output directory",
                file_path=output_dir,
                operation="directory_creation",
                details=str(e),
                original_exception=e,
                suggested_action="Check directory permissions and disk space"
            )
    
    logger.info(f"Starting pipe operation in directory: {directory}")
    
    with error_boundary("file listing", LayerType.UTILITY):
        try:
            file_list = os.listdir(directory)
            logger.debug(f"Found {len(file_list)} files in directory")
            
            if not file_list:
                logger.warning("No files found in source directory")
                return
                
        except OSError as e:
            raise FileProcessingException(
                message="Failed to list directory contents",
                file_path=directory,
                details=str(e),
                original_exception=e,
                context={'operation': 'directory_listing'},
                suggested_action="Check directory permissions and accessibility"
            )
    
    # Group and merge files
    with error_boundary("group processing", LayerType.UTILITY):
        try:
            logger.debug("Starting group and merge process")
            groups = group_files(file_list)
            logger.debug(f"Found {len(groups)} groups to merge")
            
            if groups:
                group_merge(directory, output_dir, delete, groups)
                logger.debug("Group merge completed")
            else:
                logger.info("No valid groups found for merging")
                
        except Exception as e:
            raise DataProcessingException(
                message="Failed during group processing",
                details=str(e),
                original_exception=e,
                context={'directory': directory, 'output_dir': output_dir},
                suggested_action="Check file formats and directory structure"
            )
    
    # Run reheader if not disabled
    with error_boundary("reheader processing", LayerType.UTILITY):
        if not no_reheader:
            try:
                logger.debug("Running MarkdownTableHeaderAdjuster")
                adjuster = MarkdownTableHeaderAdjuster()
                adjuster.debug_output = False
                adjuster.process_directory(directory, None, delete)
                logger.debug("MarkdownTableHeaderAdjuster completed")
            except Exception as e:
                raise DataProcessingException(
                    message="Failed during table header adjustment",
                    details=str(e),
                    original_exception=e,
                    context={'directory': directory},
                    suggested_action="Check table header format and data types"
                )
        else:
            logger.debug("Skipping MarkdownTableHeaderAdjuster as requested")

    # Run reform if not disabled
    with error_boundary("reform processing", LayerType.UTILITY):
        if not no_reform:
            try:
                logger.debug("Running MarkdownTableReformer")
                reformer = MarkdownTableReformer()
                reformer.process_directory(directory, None, delete)
                logger.debug("MarkdownTableReformer completed")
            except Exception as e:
                raise DataProcessingException(
                    message="Failed during table reformatting",
                    details=str(e),
                    original_exception=e,
                    context={'directory': directory},
                    suggested_action="Check table structure and formatting"
                )
        else:
            logger.debug("Skipping MarkdownTableReformer as requested")
    
    # Process markdown files to remove Sheet1 headers
    with error_boundary("title cleaning", LayerType.UTILITY):
        try:
            clean_titles(directory)
        except Exception as e:
            raise DataProcessingException(
                message="Failed during title cleaning",
                details=str(e),
                original_exception=e,
                context={'directory': directory},
                suggested_action="Check markdown file content and permissions"
            )
    
    logger.info("Pipe operation completed successfully")

@log_and_reraise(logger, "title cleaning")
def clean_titles(directory: str) -> List[str]:
    """
    清理markdown文件中的标题，移除Sheet1等默认标题 with unified error handling
    """
    set_operation_context("title_cleaning")
    
    with error_boundary("input validation", LayerType.UTILITY):
        if not directory or not isinstance(directory, str):
            raise ValidationException(
                message="Directory path must be a non-empty string",
                field_name="directory",
                details=f"Invalid directory type: {type(directory)}",
                context={'directory': str(directory)},
                suggested_action="Provide a valid directory path string"
            )
    
    with error_boundary("directory validation", LayerType.UTILITY):
        if not os.path.exists(directory):
            raise FileProcessingException(
                message="Directory does not exist",
                file_path=directory,
                operation="directory_access",
                context={'directory': directory},
                suggested_action="Check the directory path and ensure it exists"
            )
        
        if not os.path.isdir(directory):
            raise ValidationException(
                message="Path is not a directory",
                field_name="directory",
                details=f"Path exists but is not a directory: {directory}",
                context={'directory': directory},
                suggested_action="Provide a valid directory path"
            )
    
    logger.debug("Processing markdown files to remove Sheet1 headers")
    
    with error_boundary("file processing", LayerType.UTILITY):
        try:
            files = []
            directory_contents = os.listdir(directory)
            markdown_files = [f for f in directory_contents if f.endswith('.md')]
            
            if not markdown_files:
                logger.warning("No markdown files found in directory")
                return files
            
            logger.debug(f"Found {len(markdown_files)} markdown files to process")
            
            for filename in markdown_files:
                with error_boundary(f"file processing: {filename}", LayerType.UTILITY):
                    filepath = os.path.join(directory, filename)
                    
                    # Validate file accessibility
                    if not os.path.exists(filepath):
                        raise FileProcessingException(
                            message="File not found",
                            file_path=filepath,
                            operation="file_access",
                            context={'filename': filename},
                            suggested_action="Check if file still exists"
                        )
                    
                    if not os.access(filepath, os.R_OK):
                        raise FileProcessingException(
                            message="Cannot read file",
                            file_path=filepath,
                            operation="file_access",
                            context={'filename': filename},
                            suggested_action="Check file permissions"
                        )
                    
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                        
                        if not lines:
                            logger.warning(f"Empty file: {filename}")
                            continue
                            
                    except UnicodeDecodeError as e:
                        raise FileProcessingException(
                            message="Cannot read file due to encoding issues",
                            file_path=filepath,
                            details=str(e),
                            original_exception=e,
                            context={'filename': filename, 'operation': 'read'},
                            suggested_action="Ensure file is saved in UTF-8 encoding"
                        )
                    except Exception as e:
                        raise FileProcessingException(
                            message="Failed to read file",
                            file_path=filepath,
                            details=str(e),
                            original_exception=e,
                            context={'filename': filename, 'operation': 'read'},
                            suggested_action="Check file accessibility"
                        )
                    
                    with error_boundary("content processing", LayerType.UTILITY):
                        try:
                            modified = False
                            new_lines = []
                            
                            for line in lines:
                                if line.strip().startswith(('# Sheet1', '## Sheet1')):
                                    modified = True
                                    logger.debug(f"Removed Sheet1 header from {filename}")
                                    continue
                                new_lines.append(line)
                            
                            if not modified:
                                logger.debug(f"No Sheet1 headers found in {filename}")
                                continue
                                
                        except Exception as e:
                            raise DataProcessingException(
                                message="Failed to process file content",
                                details=str(e),
                                original_exception=e,
                                context={'filename': filename, 'line_count': len(lines)},
                                suggested_action="Check file content format"
                            )
                    
                    with error_boundary("file writing", LayerType.UTILITY):
                        try:
                            with open(filepath, 'w', encoding='utf-8') as f:
                                f.writelines(new_lines)
                            files.append(filepath)
                            logger.debug(f"Updated {filename} after removing Sheet1 headers")
                            
                        except Exception as e:
                            raise FileProcessingException(
                                message="Failed to write processed file",
                                file_path=filepath,
                                operation="write",
                                details=str(e),
                                original_exception=e,
                                context={'filename': filename},
                                suggested_action="Check file permissions and disk space"
                            )
            
            logger.debug(f"Title cleaning completed: {len(files)} files processed")
            return files
            
        except OSError as e:
            raise FileProcessingException(
                message="Failed to access directory contents",
                file_path=directory,
                details=str(e),
                original_exception=e,
                context={'operation': 'directory_listing'},
                suggested_action="Check directory permissions and accessibility"
            )
        except Exception as e:
            raise DataProcessingException(
                message="Unexpected error during title cleaning",
                details=str(e),
                original_exception=e,
                context={'directory': directory},
                suggested_action="Check directory structure and file permissions"
            )

@handle_layer_boundary(LayerType.UTILITY, "group merging")
def group_merge(directory: str, output_dir: str, delete: bool, groups: Dict[str, List[str]]) -> List[str]:
    """
    分组合并文件 with comprehensive error handling
    """
    set_operation_context("group_merging")
    
    with error_boundary("input validation", LayerType.UTILITY):
        if not directory or not isinstance(directory, str):
            raise ValidationException(
                message="Directory path must be a non-empty string",
                field_name="directory",
                details=f"Invalid directory type: {type(directory)}",
                context={'directory': str(directory)},
                suggested_action="Provide a valid directory path string"
            )
        
        if not output_dir or not isinstance(output_dir, str):
            raise ValidationException(
                message="Output directory path must be a non-empty string",
                field_name="output_dir",
                details=f"Invalid output_dir type: {type(output_dir)}",
                context={'output_dir': str(output_dir)},
                suggested_action="Provide a valid output directory path string"
            )
        
        if not isinstance(delete, bool):
            raise ValidationException(
                message="Delete parameter must be a boolean",
                field_name="delete",
                details=f"Expected bool, got {type(delete)}",
                context={'delete_type': type(delete).__name__},
                suggested_action="Provide a boolean value for delete parameter"
            )
        
        if not isinstance(groups, dict):
            raise ValidationException(
                message="Groups parameter must be a dictionary",
                field_name="groups",
                details=f"Expected dict, got {type(groups)}",
                context={'groups_type': type(groups).__name__},
                suggested_action="Provide a dictionary mapping group names to file lists"
            )
    
    with error_boundary("directory validation", LayerType.UTILITY):
        if not os.path.exists(directory):
            raise FileProcessingException(
                message="Source directory does not exist",
                file_path=directory,
                context={'directory': directory, 'operation': 'directory_access'},
                suggested_action="Check the source directory path and ensure it exists"
            )
        
        if not os.path.isdir(directory):
            raise ValidationException(
                message="Source path is not a directory",
                field_name="directory",
                details=f"Path exists but is not a directory: {directory}",
                context={'directory': directory},
                suggested_action="Provide a valid directory path"
            )
        
        # Ensure output directory exists
        try:
            os.makedirs(output_dir, exist_ok=True)
        except OSError as e:
            raise FileProcessingException(
                message="Failed to create output directory",
                file_path=output_dir,
                operation="directory_creation",
                details=str(e),
                original_exception=e,
                suggested_action="Check directory permissions and disk space"
            )
    
    logger.debug(f"Group merging {len(groups)} groups")
    merged_files = []
    
    for group_name, files in groups.items():
        with error_boundary(f"group processing: {group_name}", LayerType.UTILITY):
            if len(files) < 2:
                logger.debug(f"Skipping group {group_name} - less than 2 files")
                continue
                
            logger.debug(f"Processing group {group_name} with {len(files)} files")
            
            # Validate file list
            if not isinstance(files, list):
                logger.warning(f"Invalid file list for group {group_name} - expected list, got {type(files)}")
                continue
            
            # 使用临时文件进行迭代合并
            try:
                # Extract base filename without path
                base_file1 = os.path.basename(files[0])
                base_file2 = os.path.basename(files[1])

                # Parse filenames to get cell coordinates
                parsed_file1 = parse_filename(base_file1)
                parsed_file2 = parse_filename(base_file2)

                if not parsed_file1 or not parsed_file2:
                    logger.warning(f"Could not parse filenames for group {group_name}. Skipping merge.")
                    continue

                prefix1, _, cell1, _ = parsed_file1
                _, _, cell2, _ = parsed_file2

                # Extract start cell from first file (e.g., A1 from A1C30)
                start_cell_match = re.match(r"^([A-Za-z]+\d+)", cell1)
                start_cell = start_cell_match.group(1) if start_cell_match else ""

                # Extract end cell from second file (e.g., C34 from A32C34)
                end_cell_match = re.search(r"([A-Za-z]+\d+)$", cell2)
                end_cell = end_cell_match.group(1) if end_cell_match else ""

                # Construct the new cell range for the output filename
                new_cell_range = f"{start_cell}{end_cell}" if start_cell and end_cell else "AC"

                # Construct the base filename for the output
                base_output_filename = f"{prefix1}_{new_cell_range}"

                temp_file = os.path.join(output_dir, f"{base_output_filename}_temp.md")
                output_file = os.path.join(output_dir, f"{base_output_filename}_merged.md")
                
                # 合并前两个文件
                file1 = os.path.join(directory, files[0])
                file2 = os.path.join(directory, files[1])
                
                with error_boundary("initial merge", LayerType.UTILITY):
                    logger.debug(f"Merging first two files: {file1} and {file2} into temp file {temp_file}")
                    if not os.path.exists(file1):
                        raise FileProcessingException(
                            message="Source file not found",
                            file_path=file1,
                            operation="file_access",
                            context={'group_name': group_name, 'file_index': 0},
                            suggested_action="Check if source files still exist"
                        )
                    if not os.path.exists(file2):
                        raise FileProcessingException(
                            message="Source file not found",
                            file_path=file2,
                            operation="file_access",
                            context={'group_name': group_name, 'file_index': 1},
                            suggested_action="Check if source files still exist"
                        )
                    merge_files(file1, file2, temp_file, delete=False)
                
                # 合并剩余文件
                for i in range(2, len(files)):
                    with error_boundary(f"merge iteration {i}", LayerType.UTILITY):
                        next_file = os.path.join(directory, files[i])
                        logger.debug(f"Merging next file: {next_file} into output file {output_file}")
                        
                        if not os.path.exists(next_file):
                            raise FileProcessingException(
                                message="Source file not found",
                                file_path=next_file,
                                operation="file_access",
                                context={'group_name': group_name, 'file_index': i},
                                suggested_action="Check if source files still exist"
                            )
                        
                        merge_files(temp_file, next_file, output_file, delete=False)
                        if i != len(files) - 1:
                            logger.debug(f"Replacing {output_file} with {temp_file} for next iteration")
                            os.replace(output_file, temp_file)
                
                # 处理最后一个合并结果
                with error_boundary("finalize merge", LayerType.UTILITY):
                    if len(files) == 2:
                        logger.debug(f"Replacing temp file {temp_file} with final output {output_file}")
                        os.replace(temp_file, output_file)
                    else:
                        logger.debug(f"Removing temporary file {temp_file}")
                        os.remove(temp_file)
                        
                logger.info(f"已合并组 {group_name} 到: {output_file}")
                merged_files.append(output_file)
                
                # 如果需要删除原文件
                if delete:
                    with error_boundary("file deletion", LayerType.UTILITY):
                        for filename in files:
                            file_path = os.path.join(directory, filename)
                            try:
                                if os.path.exists(file_path):
                                    os.remove(file_path)
                                    logger.debug(f"Deleted original file: {file_path}")
                                else:
                                    logger.warning(f"Original file not found for deletion: {file_path}")
                            except OSError as e:
                                logger.warning(f"Failed to delete original file {file_path} - {e}")
                                
            except Exception as e:
                if isinstance(e, (FileProcessingException, DataProcessingException, ValidationException)):
                    raise
                else:
                    raise DataProcessingException(
                        message=f"Unexpected error processing group {group_name}",
                        details=str(e),
                        original_exception=e,
                        context={'group_name': group_name, 'file_count': len(files)},
                        suggested_action="Check file formats and directory permissions"
                    )
    
    logger.debug(f"Group merge completed, {len(merged_files)} files merged")
    return merged_files

@handle_layer_boundary(LayerType.UTILITY, "markdown table processing")
def process_markdown_tables(input_file: str, output_file: str = None) -> str:
    """Process markdown tables with unified error handling"""
    set_operation_context("markdown_table_processing")
    
    with error_boundary("input file validation", LayerType.UTILITY):
        if not os.path.exists(input_file):
            raise ValidationException(
                message="Input file not found",
                field_name="input_file",
                context={'file_path': input_file},
                suggested_action="Check the input file path and ensure it exists"
            )

    with error_boundary("file reading", LayerType.UTILITY):
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if not content.strip():
                raise DataProcessingException(
                    message="Input file is empty",
                    data_type="markdown_content",
                    context={'file_path': input_file},
                    suggested_action="Ensure input file contains markdown content"
                )
                
        except UnicodeDecodeError as e:
            raise FileProcessingException(
                message="Cannot read file due to encoding issues",
                file_path=input_file,
                details=str(e),
                original_exception=e,
                context={'operation': 'read'},
                suggested_action="Ensure file is saved in UTF-8 encoding"
            )
        except Exception as e:
            raise FileProcessingException(
                message="Failed to read input file",
                file_path=input_file,
                details=str(e),
                original_exception=e,
                context={'operation': 'read'},
                suggested_action="Check file permissions and accessibility"
            )

    with error_boundary("table detection", LayerType.UTILITY):
        try:
            # Detect markdown tables
            table_pattern = r'\|.*\|'
            tables_found = re.findall(table_pattern, content, re.MULTILINE)
            
            if not tables_found:
                logger.info("No markdown tables found in file")
                return content  # Return original content if no tables
                
            logger.info(f"Found {len(tables_found)} table rows to process")
            
        except Exception as e:
            raise DataProcessingException(
                message="Failed to detect markdown tables",
                data_type="table_detection",
                details=str(e),
                original_exception=e,
                suggested_action="Check markdown table format"
            )

    with error_boundary("table reformatting", LayerType.UTILITY):
        try:
            # Initialize table reformatter
            reformatter = MarkdownTableReformer()
            reformed_content = reformatter.reform_tables(content)
            
        except Exception as e:
            raise DataProcessingException(
                message="Table reformatting failed",
                data_type="table_reformatting",
                details=str(e),
                original_exception=e,
                suggested_action="Check table structure and formatting"
            )

    with error_boundary("header adjustment", LayerType.UTILITY):
        try:
            # Initialize header adjuster
            header_adjuster = MarkdownTableHeaderAdjuster()
            final_content = header_adjuster.adjust_headers(reformed_content)
            
        except Exception as e:
            raise DataProcessingException(
                message="Header adjustment failed",
                data_type="header_adjustment",
                details=str(e),
                original_exception=e,
                suggested_action="Check table header format and data types"
            )

    with error_boundary("output file writing", LayerType.UTILITY):
        try:
            if output_file:
                output_path = output_file
            else:
                # Generate output filename
                input_path = Path(input_file)
                output_path = str(input_path.with_suffix('.processed.md'))
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(final_content)
                
            logger.info(f"Processed markdown tables saved to: {output_path}")
            return output_path
            
        except Exception as e:
            raise FileProcessingException(
                message="Failed to write output file",
                file_path=output_path if 'output_path' in locals() else "unknown",
                details=str(e),
                original_exception=e,
                context={'operation': 'write'},
                suggested_action="Check directory permissions and disk space"
            )

@handle_layer_boundary(LayerType.UTILITY, "table statistics analysis")
def analyze_table_statistics(content: str) -> Dict[str, Any]:
    """Analyze markdown table statistics with unified error handling"""
    set_operation_context("table_statistics_analysis")
    
    with error_boundary("content validation", LayerType.UTILITY):
        if not content or not content.strip():
            raise ValidationException(
                message="Content cannot be empty for analysis",
                field_name="content",
                suggested_action="Provide valid markdown content with tables"
            )

    with error_boundary("table parsing", LayerType.UTILITY):
        try:
            # Parse tables and collect statistics
            table_pattern = r'\|.*\|'
            table_rows = re.findall(table_pattern, content, re.MULTILINE)
            
            if not table_rows:
                return {
                    'total_tables': 0,
                    'total_rows': 0,
                    'total_columns': 0,
                    'analysis': 'No tables found'
                }
            
            # Analyze table structure
            total_tables = len([row for row in table_rows if '---' in row or '===' in row])
            total_rows = len(table_rows)
            
            # Estimate columns from first data row
            first_data_row = next((row for row in table_rows if '---' not in row and '===' not in row), '')
            total_columns = len(first_data_row.split('|')) - 2 if first_data_row else 0
            
            return {
                'total_tables': total_tables,
                'total_rows': total_rows,
                'total_columns': total_columns,
                'analysis': f'Found {total_tables} tables with {total_rows} total rows'
            }
            
        except Exception as e:
            raise DataProcessingException(
                message="Failed to analyze table statistics",
                data_type="table_analysis",
                details=str(e),
                original_exception=e,
                suggested_action="Check markdown table format and structure"
            )
