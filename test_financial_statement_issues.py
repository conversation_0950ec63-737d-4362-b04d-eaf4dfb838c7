#!/usr/bin/env python3
"""
Test and analyze issues with the financial statement file
"""

import sys
import os
from pathlib import Path
import json
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer
from app.utils.reheader import MarkdownTableHeaderAdjuster

def analyze_current_issues():
    """Analyze the current processing issues"""
    print("="*80)
    print("ANALYZING FINANCIAL STATEMENT PROCESSING ISSUES")
    print("="*80)
    
    # Read the file
    filename = "1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted.md"
    with open('test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Processing file: {filename}")
    print()
    
    # Test Issue 1: Date Extraction
    print("="*60)
    print("ISSUE 1: DATE EXTRACTION ANALYSIS")
    print("="*60)
    
    reformer = MarkdownTableReformer()
    
    # Parse the table structure
    table_data, separator_line, markdown_headers = reformer.parse_markdown_table(content)
    
    print(f"Separator line: {separator_line}")
    print(f"Total rows: {len(table_data)}")
    print()
    
    print("Table structure:")
    for i, row in enumerate(table_data[:6]):
        print(f"  Row {i+1}: {row}")
    
    print()
    
    # Test date extraction from header lines only (as required)
    header_rows = table_data[:separator_line] if separator_line > 0 else []
    print(f"Header rows (before separator): {header_rows}")
    
    # Test date extraction from header lines only
    date_from_headers = reformer.extract_date_from_header_lines(header_rows)
    print(f"Date from header lines only: '{date_from_headers}'")
    
    # Test date extraction from filename
    date_from_filename = reformer.extract_date_from_filename(filename)
    print(f"Date from filename: '{date_from_filename}'")
    
    # Expected: should extract "2024年9月" from filename since header has no dates
    expected_date = "2024年9月"
    print(f"Expected date: '{expected_date}'")
    
    if date_from_filename == expected_date:
        print("✅ Filename date extraction working correctly")
    else:
        print("❌ Filename date extraction issue")
    
    print()
    
    # Test Issue 2: Header Detection
    print("="*60)
    print("ISSUE 2: HEADER DETECTION ANALYSIS")
    print("="*60)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Parse table
    rows = adjuster.parse_table(content)
    print(f"Total rows parsed: {len(rows)}")
    
    print("\nFirst 8 rows:")
    for i, row in enumerate(rows[:8]):
        if adjuster.is_separator_row(row):
            print(f"  Row {i+1}: SEPARATOR - {row}")
        else:
            print(f"  Row {i+1}: {row}")
    
    print()
    
    # Test header detection for "项 目" row
    xiangmu_row = ['项 目', '本年累计金额', '上年金额']
    print(f"Testing '项 目' row: {xiangmu_row}")
    
    has_keywords = adjuster.contains_header_keywords(xiangmu_row)
    keyword_count = adjuster.count_header_keywords(xiangmu_row)
    has_strong = adjuster.has_strong_header_keywords(xiangmu_row)
    row_density = adjuster.calculate_row_density(xiangmu_row)
    string_density = adjuster.calculate_string_density(xiangmu_row)
    
    print(f"  Has header keywords: {has_keywords}")
    print(f"  Keyword count: {keyword_count}")
    print(f"  Has strong keywords: {has_strong}")
    print(f"  Row density: {row_density:.2f}")
    print(f"  String density: {string_density:.2f}")
    
    # Calculate score
    score = adjuster.calculate_comprehensive_header_score(xiangmu_row, 2, len(rows), rows)
    print(f"  Comprehensive score: {score:.2f}")
    
    print()
    
    # Find header candidates
    candidates = adjuster.find_all_header_candidates(rows)
    print("Top 5 header candidates:")
    xiangmu_found = False
    for i, (row_idx, candidate_score) in enumerate(candidates[:5]):
        row = rows[row_idx]
        print(f"  {i+1}. Row {row_idx+1}: Score {candidate_score:.2f} - {row}")
        if '项' in ' '.join(row):
            print(f"      ✅ This contains '项目' keyword")
            xiangmu_found = True
    
    if not xiangmu_found:
        print("❌ '项目' row not found in top candidates")
    
    print()
    
    # Test current processing results
    print("="*60)
    print("CURRENT PROCESSING RESULTS")
    print("="*60)
    
    # Extract current metadata
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', content)
    if metadata_match:
        current_metadata = json.loads(metadata_match.group(1))
        print("Current metadata:")
        for key, value in current_metadata.items():
            print(f"  {key}: {value}")
        
        current_date = current_metadata.get('date', '')
        print(f"\nCurrent date in metadata: '{current_date}'")
        print(f"Expected date: '{expected_date}'")
        
        if current_date == expected_date:
            print("✅ Date in metadata is correct")
        else:
            print("❌ Date in metadata is incorrect")
    
    return xiangmu_found, date_from_filename == expected_date

def test_fixes():
    """Test the fixes for both issues"""
    print("\n" + "="*80)
    print("TESTING FIXES")
    print("="*80)
    
    filename = "1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted.md"
    with open('test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    reformer = MarkdownTableReformer()
    
    # Test processing with original content (without existing metadata)
    # Remove existing metadata to test fresh processing
    clean_content = re.sub(r'<!-- METADATA:.*?-->\s*\n?', '', content)
    
    print("Testing fresh processing without existing metadata...")
    
    try:
        result = reformer.process_table(clean_content, filename)
        print("✅ Processing successful")
        
        # Extract new metadata
        metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
        if metadata_match:
            new_metadata = json.loads(metadata_match.group(1))
            print("\nNew metadata:")
            for key, value in new_metadata.items():
                print(f"  {key}: {value}")
            
            # Check date
            new_date = new_metadata.get('date', '')
            expected_date = "2024年9月"
            
            print(f"\nDate extraction test:")
            print(f"  Expected: '{expected_date}'")
            print(f"  Actual: '{new_date}'")
            
            if new_date == expected_date:
                print("  ✅ Date extraction working correctly")
                date_fix_works = True
            else:
                print("  ❌ Date extraction still has issues")
                date_fix_works = False
        else:
            print("❌ No metadata found in result")
            date_fix_works = False
        
        # Show first 10 lines of result to check header structure
        result_lines = result.split('\n')
        print("\nFirst 10 lines of processed result:")
        for i, line in enumerate(result_lines[:10]):
            print(f"  {i+1}: {line}")
        
        # Check if '项目' is properly positioned
        header_structure_good = False
        for i, line in enumerate(result_lines[:10]):
            if '项' in line and ('本年累计' in line or '上年' in line):
                print(f"\n✅ '项目' found in proper header structure at line {i+1}")
                header_structure_good = True
                break
        
        if not header_structure_good:
            print("\n❌ '项目' not found in proper header structure")
        
        return date_fix_works and header_structure_good
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main analysis and testing function"""
    try:
        # Analyze current issues
        header_detection_ok, date_extraction_ok = analyze_current_issues()
        
        # Test fixes
        fixes_work = test_fixes()
        
        print("\n" + "="*80)
        print("SUMMARY")
        print("="*80)
        print(f"Header detection working: {'✅' if header_detection_ok else '❌'}")
        print(f"Date extraction working: {'✅' if date_extraction_ok else '❌'}")
        print(f"Fixes working: {'✅' if fixes_work else '❌'}")
        
        if header_detection_ok and date_extraction_ok and fixes_work:
            print("\n🎉 All issues resolved!")
            return 0
        else:
            print("\n❌ Some issues need attention")
            return 1
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
