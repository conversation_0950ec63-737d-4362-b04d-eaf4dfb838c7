import logging
import re
import time
import typer
from pathlib import Path
from typing import Dict, Any, Optional
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_openai import ChatOpenAI
from app.llm_factory import LLMFactory
from app.agents.base import BaseAgent
from app.config import AgentConfig

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, FileProcessingException,
    ValidationException, ConfigurationException, ExternalServiceException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()

# Set layer context for agent operations
set_layer_context("business_logic")

app = typer.Typer()

@log_and_reraise(logging.getLogger(__name__), "markdown file processing")
def process_files_internal(prompt_file: str, content_file: str, output_file: str = None, config_file: str = None):
    """
    处理Markdown内容（使用文件中的提示词） with unified error handling

    参数:
        prompt_file: 包含处理提示词的文件路径
        content_file: 需要处理的Markdown内容文件路径
        output_file: 输出文件路径（可选，默认覆盖原文件）
        config_file: 配置文件路径（可选）

    流程:
        1. 读取提示词文件和内容文件
        2. 加载配置（使用默认配置或指定配置文件）
        3. 初始化MdAgent处理内容
        4. 将处理结果写入输出文件
    """
    set_operation_context("markdown_file_processing")

    # Validate input files
    with error_boundary("input file validation", LayerType.BUSINESS_LOGIC):
        prompt_path = Path(prompt_file)
        content_path = Path(content_file)

        if not prompt_path.exists():
            raise FileProcessingException(
                message="Prompt file not found",
                file_path=prompt_file,
                details=f"Prompt file does not exist: {prompt_file}",
                suggested_action="Check the prompt file path and ensure it exists"
            )

        if not content_path.exists():
            raise FileProcessingException(
                message="Content file not found",
                file_path=content_file,
                details=f"Content file does not exist: {content_file}",
                suggested_action="Check the content file path and ensure it exists"
            )

    # Read files with error handling
    with error_boundary("file reading", LayerType.BUSINESS_LOGIC):
        try:
            with open(prompt_path, 'r', encoding='utf-8') as f:
                prompt = f.read()

            with open(content_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if not prompt.strip():
                raise ValidationException(
                    message="Empty prompt file",
                    field_name="prompt",
                    details="Prompt file is empty or contains only whitespace",
                    suggested_action="Provide a valid prompt file with content"
                )

            if not content.strip():
                raise ValidationException(
                    message="Empty content file",
                    field_name="content",
                    details="Content file is empty or contains only whitespace",
                    suggested_action="Provide a valid content file with markdown content"
                )

        except (IOError, OSError) as e:
            raise FileProcessingException(
                message="Failed to read input files",
                file_path=f"{prompt_file}, {content_file}",
                details=f"File reading error: {str(e)}",
                original_exception=e,
                suggested_action="Check file permissions and encoding"
            )

    # Load configuration with error handling
    with error_boundary("configuration loading", LayerType.BUSINESS_LOGIC):
        try:
            if config_file:
                config = AgentConfig.from_file(config_file)
            else:
                config = AgentConfig(
                    workspace_id='cli',
                    log_level="DEBUG"
                )
        except Exception as e:
            raise ConfigurationException(
                message="Failed to load configuration",
                details=f"Configuration loading error: {str(e)}",
                original_exception=e,
                suggested_action="Check configuration file format and content"
            )

    # Initialize agent and process content
    with error_boundary("agent processing", LayerType.BUSINESS_LOGIC):
        try:
            agent = MdAgent(config, prompt)
            result = agent.process_message({
                "role": "system",
                "content": content
            })

            if not result or 'message' not in result:
                raise ValidationException(
                    message="Invalid processing result",
                    field_name="result",
                    details="Agent returned empty or invalid result",
                    suggested_action="Check prompt and content format"
                )

        except Exception as e:
            if isinstance(e, (ValidationException, ConfigurationException)):
                raise
            raise ExternalServiceException(
                message="Agent processing failed",
                service_name="MdAgent",
                details=f"Processing error: {str(e)}",
                original_exception=e,
                suggested_action="Check LLM configuration and connectivity"
            )

    # Write output with error handling
    with error_boundary("output file writing", LayerType.BUSINESS_LOGIC):
        try:
            output_path = Path(output_file) if output_file else content_path
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(result['message'])

            typer.echo(f"Successfully processed content. Output saved to: {output_path}")

        except (IOError, OSError) as e:
            raise FileProcessingException(
                message="Failed to write output file",
                file_path=str(output_path),
                details=f"File writing error: {str(e)}",
                original_exception=e,
                suggested_action="Check output directory permissions and disk space"
            )

class MdAgent(BaseAgent):
    """
    Markdown处理代理，用于清理和优化Markdown内容
    
    功能:
        - 检测内容是否需要清理
        - 使用LLM和提示词模板处理Markdown内容
        - 支持文件直接处理模式
    """
    
    @log_and_reraise(logging.getLogger(__name__), "LLM setup")
    def _setup_llm(self):
        """初始化并返回LLM实例 with unified error handling"""
        set_operation_context("llm_setup")

        with error_boundary("LLM initialization", LayerType.BUSINESS_LOGIC):
            logging.info("正在初始化LLM，配置: "
                       f"模型={self.config.model_name}, "
                       f"温度={self.config.temperature}, "
                       f"API地址={self.config.api_base}")

            try:
                llm = LLMFactory.create_llm(
                    model_name=self.config.model_name,
                    temperature=self.config.temperature,
                    api_key=self.config.api_key,
                    api_base=self.config.api_base
                )
                logging.info("LLM初始化成功")
                return llm

            except Exception as e:
                raise ExternalServiceException(
                    message="Failed to initialize LLM",
                    service_name="LLM_Factory",
                    details=f"LLM initialization error: {str(e)}",
                    original_exception=e,
                    context={
                        'model_name': self.config.model_name,
                        'api_base': self.config.api_base,
                        'temperature': self.config.temperature
                    },
                    suggested_action="Check LLM configuration and API connectivity"
                )

    def __init__(self, config: AgentConfig, prompt: str):
        """初始化MdAgent
        
        参数:
            config: 代理配置
            prompt: 用于清理的提示词模板
        """
        super().__init__(config)
        self.llm = self._setup_llm()
        self.clean_prompt = ChatPromptTemplate.from_template(prompt)  # 创建提示词模板

    def _needs_cleaning(self, content: str) -> bool:
        """
        检查内容是否需要清理（通过常见模式匹配）
        
        模式包括:
            - HTML标签
            - 水印声明（如"仅"、"效"、"印"等）
            
        返回:
            bool: 如果匹配到任何模式则返回True
        """
        patterns = [
            r'<[^>]+>',  # HTML标签
            r'仅|效|印|复印|复',  # 水印声明
        ]
        return any(re.search(pattern, content) for pattern in patterns)

    @log_and_reraise(logging.getLogger(__name__), "markdown message processing")
    def process_message(self, message: Dict[str, Any], file_path: Optional[str] = None) -> Dict[str, Any]:
        """
        处理Markdown清理请求 with unified error handling

        参数:
            message: 包含角色和内容的字典
            file_path: 文件路径（可选，用于直接写入结果）

        返回:
            包含状态和处理结果的字典
        """
        set_operation_context("markdown_message_processing")

        # Validate message format
        with error_boundary("message validation", LayerType.BUSINESS_LOGIC):
            if not isinstance(message, dict):
                raise ValidationException(
                    message="Invalid message format",
                    field_name="message",
                    details="Message must be a dictionary",
                    suggested_action="Provide a valid message dictionary"
                )

            if message.get("role") != "system":
                raise ValidationException(
                    message="Invalid message role",
                    field_name="role",
                    details="MdAgent only accepts system role messages",
                    context={'provided_role': message.get("role")},
                    suggested_action="Use 'system' role for MdAgent messages"
                )

            content = message.get("content")
            if not content:
                raise ValidationException(
                    message="Empty message content",
                    field_name="content",
                    details="Message content cannot be empty",
                    suggested_action="Provide valid markdown content"
                )

            force = message.get("force", False)  # 是否强制清理

        # Check if content needs cleaning (unless forced)
        with error_boundary("content analysis", LayerType.BUSINESS_LOGIC):
            if not force:
                # 快速路径：如果内容已经干净则直接返回
                matched = self._needs_cleaning(content)
                if not matched:
                    logging.info(f"内容已清理，返回原始内容: {file_path}")

                    # Write to file if path provided
                    if file_path:
                        with error_boundary("clean content file write", LayerType.BUSINESS_LOGIC):
                            try:
                                with open(file_path, 'w', encoding='utf-8') as f:
                                    f.write(content)
                            except (IOError, OSError) as e:
                                raise FileProcessingException(
                                    message="Failed to write clean content to file",
                                    file_path=file_path,
                                    details=f"File write error: {str(e)}",
                                    original_exception=e,
                                    suggested_action="Check file permissions and disk space"
                                )

                    return {"status": "success", "message": content}

        # Process content with LLM
        with error_boundary("LLM content processing", LayerType.BUSINESS_LOGIC):
            logging.info(f"正在处理Markdown内容 (长度={len(content)})")
            start_time = time.time()

            try:
                # 创建处理链：内容 -> 提示词模板 -> LLM -> 字符串输出
                chain = (
                    {"content": RunnablePassthrough()}
                    | self.clean_prompt
                    | self.llm
                    | StrOutputParser()
                )

                cleaned = chain.invoke(content)  # 执行处理链
                duration = time.time() - start_time

                if not cleaned:
                    raise ValidationException(
                        message="LLM returned empty result",
                        field_name="cleaned_content",
                        details="LLM processing returned empty or null content",
                        suggested_action="Check prompt template and LLM configuration"
                    )

            except Exception as e:
                if isinstance(e, ValidationException):
                    raise
                raise ExternalServiceException(
                    message="LLM processing failed",
                    service_name="LLM_chain",
                    details=f"Chain execution error: {str(e)}",
                    original_exception=e,
                    suggested_action="Check LLM connectivity and prompt template"
                )

        # Write processed content to file if path provided
        if file_path:
            with error_boundary("processed content file write", LayerType.BUSINESS_LOGIC):
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(cleaned)
                    logging.info(f"Saved cleaned content to {file_path}")
                except (IOError, OSError) as e:
                    raise FileProcessingException(
                        message="Failed to write processed content to file",
                        file_path=file_path,
                        details=f"File write error: {str(e)}",
                        original_exception=e,
                        suggested_action="Check file permissions and disk space"
                    )

        logging.info(f"Markdown processing completed in {duration:.2f}s "
                    f"(output length={len(cleaned)})")
        return {"status": "success", "message": cleaned}

@app.command()
def process(
    prompt_file: str = typer.Argument(..., help="提示词文件路径"),
    content_file: str = typer.Argument(..., help="内容文件路径"),
    output_file: str = typer.Option(None, "--output", "-o", help="输出文件路径"),
    config_file: str = typer.Option(None, "--config", "-c", help="自定义配置文件路径")
):
    """使用提示词处理Markdown文件
    
    示例用法:
    python -m app.agents.md_agent process prompt.txt content.md
    python -m app.agents.md_agent process prompt.txt content.md -o output.md
    """
    try:
        process_files_internal(prompt_file, content_file, output_file, config_file)
    except Exception as e:
        typer.echo(f"Error: {str(e)}", err=True)
        raise typer.Exit(1)

def main():
    """主函数入口"""
    app()

if __name__ == "__main__":
    """命令行直接运行时启动应用"""
    main()
