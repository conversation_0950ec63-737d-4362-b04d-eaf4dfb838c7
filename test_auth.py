#!/usr/bin/env python3
"""
Test script for authentication functionality
"""

import requests
import json

# Server configuration
BASE_URL = "http://localhost:8100"

def test_authentication():
    """Test the authentication endpoints"""
    
    print("Testing Authentication System")
    print("=" * 40)
    
    # Test 1: Login with valid credentials
    print("\n1. Testing login with valid credentials...")
    login_data = {
        "username": "test",
        "password": "test123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/token", data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data["access_token"]
            print(f"✓ Login successful! Token: {access_token[:20]}...")
            
            # Test 2: Access protected endpoint
            print("\n2. Testing protected endpoint access...")
            headers = {"Authorization": f"Bearer {access_token}"}
            
            response = requests.get(f"{BASE_URL}/templates", headers=headers)
            if response.status_code == 200:
                templates = response.json()
                print(f"✓ Templates retrieved: {len(templates)} templates found")
                for template in templates:
                    print(f"  - {template['name']}")
            else:
                print(f"✗ Failed to access templates: {response.status_code}")
                
        else:
            print(f"✗ Login failed: {response.status_code} - {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server. Make sure the server is running on port 8100")
        return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False
    
    # Test 3: Login with invalid credentials
    print("\n3. Testing login with invalid credentials...")
    invalid_login_data = {
        "username": "invalid",
        "password": "wrong"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/token", data=invalid_login_data)
        if response.status_code == 401:
            print("✓ Invalid login correctly rejected")
        else:
            print(f"✗ Unexpected response for invalid login: {response.status_code}")
    except Exception as e:
        print(f"✗ Error testing invalid login: {e}")
    
    # Test 4: Access protected endpoint without token
    print("\n4. Testing protected endpoint without authentication...")
    try:
        response = requests.get(f"{BASE_URL}/templates")
        if response.status_code == 401:
            print("✓ Unauthorized access correctly rejected")
        else:
            print(f"✗ Unexpected response for unauthorized access: {response.status_code}")
    except Exception as e:
        print(f"✗ Error testing unauthorized access: {e}")
    
    print("\n" + "=" * 40)
    print("Authentication test completed!")
    
    return True

def test_health_check():
    """Test the health check endpoint"""
    print("\nTesting health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✓ Server is healthy: {health_data}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server for health check")
        return False

if __name__ == "__main__":
    print("Auto-Report Authentication Test")
    print("=" * 50)
    
    # First test health check
    if test_health_check():
        # Then test authentication
        test_authentication()
    else:
        print("Server is not responding. Please start the server first:")
        print("cd app/api && python server.py")
