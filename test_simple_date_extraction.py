#!/usr/bin/env python3
"""
Simple test for date extraction from profit statement
"""

import sys
import os
from pathlib import Path
import json
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def main():
    """Simple test for date extraction"""
    print("Testing Date Extraction from Profit Statement")
    print("="*60)
    
    reformer = MarkdownTableReformer()
    
    # Read the profit statement file
    with open('test3\\-季度合并财报（2024年Q4）_利润表_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_利润表.md"
    
    print(f"Processing: {filename}")
    
    # Process the table
    result = reformer.process_table(content, filename)
    
    # Extract metadata
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        
        print("\nExtracted metadata:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
        
        # Check the date
        metadata_date = metadata.get('date', None)
        print(f"\nExtracted date: '{metadata_date}'")
        
        # Expected: should be "2024-12-31" from table content, not "2024年Q4" from filename
        if metadata_date == "2024-12-31":
            print("✅ SUCCESS: Table content date extracted correctly")
            return 0
        elif metadata_date == "2024年Q4":
            print("❌ ISSUE: Filename date used instead of table content date")
            return 1
        else:
            print(f"❌ UNEXPECTED: Got '{metadata_date}'")
            return 1
    else:
        print("❌ No metadata found")
        return 1

if __name__ == "__main__":
    sys.exit(main())
