#!/usr/bin/env python3
"""
Comprehensive LDAP authentication test script
Tests the new two-step LDAP authentication implementation against Docker OpenLDAP
"""

import os
import sys
import json
import requests
import time
from dotenv import load_dotenv

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def check_docker_ldap():
    """Check if Docker LDAP environment is running"""
    try:
        import subprocess
        result = subprocess.run(['docker', 'ps', '--filter', 'name=test-openldap', '--format', '{{.Status}}'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and 'Up' in result.stdout:
            return True
        return False
    except Exception:
        return False

def test_ldap_connection():
    """Test basic LDAP connection"""
    print("🔍 Testing LDAP connection...")
    try:
        from ldap3 import Server, Connection, ALL

        server = Server(os.getenv('LDAP_URL'), get_info=ALL)
        conn = Connection(server, user=os.getenv('LDAP_ADMIN_DN'),
                         password=os.getenv('LDAP_ADMIN_PASSWORD'), auto_bind=True)

        # Test search
        conn.search(os.getenv('LDAP_BASE_DN'), '(objectClass=*)', attributes=['*'])
        conn.unbind()
        print("✅ LDAP connection successful")
        return True
    except Exception as e:
        print(f"❌ LDAP connection failed: {str(e)}")
        return False

def test_user_authentication():
    """Test user authentication with predefined test users"""

    # Load environment variables
    load_dotenv()

    # Import the authentication function
    from app.api.server import authenticate_user_ldap

    print("\n🧪 Testing LDAP Authentication Function")
    print("=" * 50)

    # Test users from our Docker LDAP setup
    test_users = [
        {"username": "john.doe", "password": "password123", "expected_dept": "finance"},
        {"username": "jane.smith", "password": "password123", "expected_dept": "risk"},
        {"username": "bob.wilson", "password": "password123", "expected_dept": "technology"},
        {"username": "alice.brown", "password": "password123", "expected_dept": "admin"},
        {"username": "test.user", "password": "password123", "expected_dept": None},  # No department
    ]

    # Test invalid credentials
    invalid_tests = [
        {"username": "john.doe", "password": "wrongpassword", "reason": "Wrong password"},
        {"username": "nonexistent", "password": "password123", "reason": "User not found"},
        {"username": "", "password": "password123", "reason": "Empty username"},
    ]

    success_count = 0
    total_tests = len(test_users) + len(invalid_tests)

    print(f"\n📋 Testing {len(test_users)} valid users...")

    for i, user in enumerate(test_users, 1):
        print(f"\n{i}. Testing user: {user['username']}")
        try:
            result = authenticate_user_ldap(user['username'], user['password'])

            if result:
                print(f"   ✅ Authentication successful")
                print(f"   📋 User info:")
                for key, value in result.items():
                    if key == "groups" and isinstance(value, list):
                        print(f"     {key}: {len(value)} groups")
                    else:
                        print(f"     {key}: {value}")

                # Verify expected department
                if user['expected_dept']:
                    if result.get('department') == user['expected_dept']:
                        print(f"   ✅ Department matches expected: {user['expected_dept']}")
                    else:
                        print(f"   ⚠️  Department mismatch. Expected: {user['expected_dept']}, Got: {result.get('department')}")

                success_count += 1
            else:
                print(f"   ❌ Authentication failed unexpectedly")

        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

    print(f"\n📋 Testing {len(invalid_tests)} invalid scenarios...")

    for i, test in enumerate(invalid_tests, 1):
        print(f"\n{i}. Testing invalid case: {test['reason']}")
        try:
            result = authenticate_user_ldap(test['username'], test['password'])

            if not result:
                print(f"   ✅ Correctly rejected: {test['reason']}")
                success_count += 1
            else:
                print(f"   ❌ Unexpectedly accepted invalid credentials")

        except Exception as e:
            print(f"   ✅ Correctly failed with error: {str(e)}")
            success_count += 1

    print(f"\n📊 Test Results: {success_count}/{total_tests} tests passed")
    return success_count == total_tests

def test_fastapi_integration():
    """Test integration with FastAPI server"""
    print("\n🌐 Testing FastAPI Integration")
    print("=" * 40)

    # Check if server is running
    server_url = os.getenv('REPORT_SERVER_URL', 'http://localhost:8100')

    try:
        # Test health endpoint first
        response = requests.get(f"{server_url}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not responding at {server_url}")
            print("   Please start the server with: python -m app.api.server")
            return False

        print(f"✅ Server is running at {server_url}")

        # Test authentication endpoint with valid credentials
        test_users = [
            {"username": "john.doe", "password": "password123"},
            {"username": "jane.smith", "password": "password123"},
        ]

        for user in test_users:
            print(f"\n🔐 Testing login for {user['username']}...")

            # Prepare form data for OAuth2PasswordRequestForm
            form_data = {
                'username': user['username'],
                'password': user['password']
            }

            try:
                response = requests.post(f"{server_url}/token", data=form_data, timeout=10)

                if response.status_code == 200:
                    token_data = response.json()
                    print(f"   ✅ Login successful")
                    print(f"   🎫 Token type: {token_data.get('token_type')}")
                    print(f"   🔑 Access token: {token_data.get('access_token')[:50]}...")

                    # Test authenticated endpoint
                    headers = {'Authorization': f"Bearer {token_data['access_token']}"}
                    user_response = requests.get(f"{server_url}/user/me", headers=headers, timeout=5)

                    if user_response.status_code == 200:
                        user_info = user_response.json()
                        print(f"   ✅ User info retrieved: {user_info}")
                    else:
                        print(f"   ❌ Failed to get user info: {user_response.status_code}")

                else:
                    print(f"   ❌ Login failed: {response.status_code}")
                    print(f"   📋 Response: {response.text}")

            except requests.exceptions.RequestException as e:
                print(f"   ❌ Request failed: {str(e)}")

        # Test invalid credentials
        print(f"\n🔐 Testing login with invalid credentials...")
        form_data = {'username': 'invalid', 'password': 'wrong'}

        try:
            response = requests.post(f"{server_url}/token", data=form_data, timeout=10)
            if response.status_code == 401:
                print(f"   ✅ Invalid credentials correctly rejected")
            else:
                print(f"   ❌ Unexpected response: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request failed: {str(e)}")

        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {str(e)}")
        print("   Please ensure the server is running")
        return False

def interactive_test():
    """Interactive test mode"""
    print("\n🎮 Interactive Test Mode")
    print("=" * 30)

    # Load environment variables
    load_dotenv()

    # Import the authentication function
    from app.api.server import authenticate_user_ldap

    print("Current LDAP Configuration:")
    print(f"  LDAP URL: {os.getenv('LDAP_URL')}")
    print(f"  Base DN: {os.getenv('LDAP_BASE_DN')}")
    print(f"  User Filter: {os.getenv('LDAP_USER_FILTER')}")
    print(f"  Admin DN: {os.getenv('LDAP_ADMIN_DN')}")
    print()

    while True:
        username = input("Enter username (or 'quit' to exit): ").strip()
        if username.lower() == 'quit':
            break

        if not username:
            print("Please enter a username")
            continue

        password = input("Enter password: ").strip()
        if not password:
            print("Please enter a password")
            continue

        print(f"\n🔍 Testing authentication for {username}...")

        try:
            result = authenticate_user_ldap(username, password)

            if result:
                print("✅ Authentication successful!")
                print("📋 User information:")
                for key, value in result.items():
                    if key == "groups" and isinstance(value, list):
                        print(f"  {key}: {len(value)} groups")
                        for i, group in enumerate(value[:3]):
                            print(f"    {i+1}. {group}")
                        if len(value) > 3:
                            print(f"    ... and {len(value) - 3} more groups")
                    else:
                        print(f"  {key}: {value}")
            else:
                print("❌ Authentication failed")

        except Exception as e:
            print(f"❌ Error: {str(e)}")

        print()

def show_config_example():
    """Show current Docker LDAP configuration"""
    print("Current Docker LDAP Configuration:")
    print("=" * 50)
    print("""
🐳 Docker LDAP Test Environment Configuration:

# LDAP Authentication Settings (from .env)
AUTH_MODE=ldap
LDAP_URL=ldap://localhost:389
LDAP_BASE_DN=dc=company,dc=com
LDAP_USER_FILTER=(uid={username})
LDAP_DEPT_ATTR=departmentNumber
LDAP_ADMIN_DN=cn=ldap-admin,ou=people,dc=company,dc=com
LDAP_ADMIN_PASSWORD=ldap-admin123
LDAP_USERNAME_ATTR=uid
LDAP_NICKNAME_ATTR=cn
LDAP_CONNECTION_TIMEOUT=5

👥 Available Test Users:
- john.doe / password123 (Finance Department)
- jane.smith / password123 (Risk Department)
- bob.wilson / password123 (Technology Department)
- alice.brown / password123 (Admin Department)
- test.user / password123 (No Department)

🔧 Admin User:
- cn=admin,dc=company,dc=com / admin123 (Root admin)
- cn=ldap-admin,ou=people,dc=company,dc=com / ldap-admin123 (Service admin)

🌐 Management Interface:
- phpLDAPAdmin: http://localhost:8080

🚀 To start the environment:
- Windows: start-ldap-test.bat
- Linux/Mac: ./start-ldap-test.sh
""")

def main():
    """Main test function"""
    print("🧪 LDAP Authentication Test Suite")
    print("=" * 50)

    # Load environment variables
    load_dotenv()

    # Check if Docker LDAP is running
    if not check_docker_ldap():
        print("❌ Docker LDAP environment is not running!")
        print("   Please start it first:")
        print("   - Windows: start-ldap-test.bat")
        print("   - Linux/Mac: ./start-ldap-test.sh")
        print("   - Manual: docker-compose -f docker-compose.ldap.yml up -d")
        return

    print("✅ Docker LDAP environment is running")

    # Test basic LDAP connection
    if not test_ldap_connection():
        print("❌ Basic LDAP connection failed. Please check the Docker environment.")
        return

    # Show menu
    while True:
        print("\n📋 Test Options:")
        print("1. Run automated authentication tests")
        print("2. Test FastAPI integration")
        print("3. Interactive authentication test")
        print("4. Show configuration")
        print("5. Exit")

        choice = input("\nEnter your choice (1-5): ").strip()

        if choice == "1":
            test_user_authentication()
        elif choice == "2":
            test_fastapi_integration()
        elif choice == "3":
            interactive_test()
        elif choice == "4":
            show_config_example()
        elif choice == "5":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-5.")

if __name__ == "__main__":
    main()
