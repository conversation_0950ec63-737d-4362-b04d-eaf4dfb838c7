import time
import typer
import json
import logging
from pathlib import Path
from typing import Optional
from ragflow_api.client import RAGFlowClient

# Configure logging using the unified logging system
import sys
sys.path.append(str(Path(__file__).parent))

from app.logging_config import setup_logging, get_component_logger, set_layer_context, set_operation_context

# Setup unified logging
setup_logging()
logger = get_component_logger('cli', '__main__')
set_layer_context('cli')

app = typer.Typer()

def load_config():
    """Load RAGFlow API configuration from .env file"""
    from dotenv import load_dotenv
    import os
    load_dotenv(override=True)
    
    base_url = os.getenv('RAGFLOW_BASE_URL')
    api_key = os.getenv('RAGFLOW_API_KEY')
    
    if not base_url or not api_key:
        raise typer.BadParameter(
            "Please configure RAGFLOW_BASE_URL and RAGFLOW_API_KEY in .env file\n"
            "Example:\n"
            "RAGFLOW_BASE_URL=http://your-ragflow-server-url\n"
            "RAGFLOW_API_KEY=your-api-key-here"
        )
    
    return {
        'base_url': base_url,
        'api_key': api_key
    }

def extract_questions(template_path: str) -> list:
    """Extract questions from {} brackets in template.md"""
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    questions = []
    start = 0
    while True:
        start = content.find('{', start)
        if start == -1:
            break
        end = content.find('}', start)
        if end == -1:
            break
        questions.append(content[start+1:end].strip())
        start = end + 1
    return questions

def process_template(template_path: str, answers: dict) -> str:
    """Replace {} sections in template with answers"""
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    result = []
    start = 0
    answer_idx = 0
    while True:
        brace_start = content.find('{', start)
        if brace_start == -1:
            result.append(content[start:])
            break
        result.append(content[start:brace_start])
        
        brace_end = content.find('}', brace_start)
        if brace_end == -1:
            result.append(content[brace_start:])
            break
            
        if answer_idx < len(answers):
            result.append(answers[str(answer_idx)])
        answer_idx += 1
        start = brace_end + 1
    
    return ''.join(result)

@app.command()
def generate_report(
    template_path: str = typer.Argument(..., help="Path to template.md file"),
    assistant_id: Optional[str] = typer.Option(None, help="Optional assistant ID"),
    dataset_ids: Optional[list[str]] = typer.Option(None, help="List of dataset IDs to use with assistant")
):
    """Generate market report by processing template with RAGFlow assistant"""
    set_operation_context('generate_report')
    config = load_config()
    logger.info("RAGFlow configuration loaded successfully")
    client = RAGFlowClient(base_url=config['base_url'], api_key=config['api_key'])
    
    # Create assistant if not provided
    if not assistant_id:
        # Get datasets if none provided
        if dataset_ids is None:
            logger.info("No dataset IDs provided, listing available datasets")
            datasets = client.list_datasets()
            dataset_ids = [d['id'] for d in datasets] if datasets else []
            logger.info(f"Using datasets: {dataset_ids if dataset_ids else 'none'}")
        
        logger.info(f"Creating new assistant with {len(dataset_ids)} datasets")
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        assistant = client.create_chat_assistant(
            name=f"Report Generator {timestamp}",
            dataset_ids=dataset_ids
        )
        assistant_id = assistant['id']
        logger.info(f"Created assistant with ID: {assistant_id}")
        with open('assistant.id', 'w') as f:
            f.write(assistant_id)
    
    # Extract questions from template
    logger.info(f"Extracting questions from template: {template_path}")
    questions = extract_questions(template_path)
    logger.info(f"Found {len(questions)} questions in template")
    answers = {}
    
    # Process each question
    for i, question in enumerate(questions):
        # Create session and converse
        session = client.create_chat_session(
            chat_id=assistant_id,
            name=f"Report Q{i+1}"
        )
        logger.info(f"Created session with ID: {session['id']}")
        try:
            logger.info(f"Conversing with chat for question {i+1}: {question[:50]}...")
            response = client.converse_with_chat(
                chat_id=assistant_id,
                question=question,
                session_id=session['id'],
                stream=False
            )
            logger.info(f"Received response for question {i+1}\n{response}")
            
            # Save response to JSON
            answer_file = f"answer_{i}.json"
            with open(answer_file, 'w', encoding='utf-8') as f:
                json.dump(response, f, ensure_ascii=False, indent=2)
            logger.info(f"Saved response to {answer_file}")
            time.sleep(5)
        except Exception as e:
            logger.error(f"Failed to converse with chat for question {i+1}: {str(e)}")
            raise
        
        # Extract answer
        answers[str(i)] = response.get('answer', '')
    
    # Process template with answers
    processed_content = process_template(template_path, answers)
    
    # Save final report
    with open('market.md', 'w', encoding='utf-8') as f:
        f.write(processed_content)
    
    logger.info("Report generation completed successfully")
    typer.echo(f"Report generated successfully as market.md")

@app.command()
def list_datasets():
    """List all available datasets with names and IDs"""
    config = load_config()
    logger.info("RAGFlow configuration loaded successfully")
    client = RAGFlowClient(base_url=config['base_url'], api_key=config['api_key'])
    
    logger.info("Listing available datasets")
    datasets = client.list_datasets()
    
    if not datasets:
        logger.info("No datasets found")
        typer.echo("No datasets available")
        return
    
    result = [{'name': d['name'], 'id': d['id']} for d in datasets]
    logger.info(f"Found {len(datasets)} datasets")
    typer.echo(json.dumps(result, indent=2, ensure_ascii=False))

@app.command()
def document_progress(
    dataset_id: str = typer.Argument(..., help="Dataset ID to check document progress")
):
    """List document processing progress for a dataset"""
    config = load_config()
    logger.info("RAGFlow configuration loaded successfully")
    client = RAGFlowClient(base_url=config['base_url'], api_key=config['api_key'])
    
    logger.info(f"Listing documents for dataset {dataset_id}")
    documents = client.list_documents(dataset_id)
    
    if not documents or 'docs' not in documents or not documents['docs']:
        logger.info("No documents found")
        typer.echo("No documents available in this dataset")
        return
    
    # Extract name and progress for each document
    doc_progress = []
    complete_count = 0
    progressing_count = 0
    
    for doc in documents['docs']:
        progress = doc.get('progress', 0)
        run_status = doc.get('run', '')
        if run_status in ['DONE', 'CANCEL', 'FAIL']:
            status = run_status
            complete_count += 1
        else:
            status = "PROGRESSING"
            progressing_count += 1
            
        doc_progress.append({
            'name': doc.get('name', 'Unknown'),
            'progress': f"{progress * 100:.1f}%",
            'status': run_status
        })
    
    # Format output
    output = {
        'documents': doc_progress,
        'summary': {
            'total': len(documents['docs']),
            'complete': complete_count,
            'progressing': float(complete_count) / (progressing_count + complete_count)
        }
    }
    
    logger.info(f"Found {len(documents['docs'])} documents in dataset")
    typer.echo(json.dumps(output, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    app()
