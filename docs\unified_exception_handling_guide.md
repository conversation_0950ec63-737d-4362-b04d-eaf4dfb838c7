# Unified Exception Handling Guide

## Overview

This guide documents the unified approach to `suggested_action` parameter handling across all custom exception classes in the auto-report application. The implementation ensures consistency, backward compatibility, and prevents KeyError issues.

## Recent Fixes (2024)

### KeyError: 'suggested_action' Resolution
Fixed critical issues where exception classes with hard-coded parameter values were causing KeyError exceptions when instantiated with conflicting keyword arguments:

- ✅ **CLIException**: Fixed KeyError when `suggested_action` passed as kwarg
- ✅ **AuthenticationException**: Fixed KeyError when `suggested_action` passed as kwarg
- ✅ **UIException**: Fixed KeyError when `suggested_action` passed as kwarg
- ✅ **WorkflowException**: Fixed multiple values error for `error_code` parameter
- ✅ **RAGWorkflowException**: Fixed inheritance chain parameter conflicts
- ✅ **ReportWorkflowException**: Fixed inheritance chain parameter conflicts
- ✅ **LoginException**: Fixed multiple values error for `error_code` and `severity`
- ✅ **AccountRestrictedException**: Fixed parameter conflict issues

## Architecture

### FlexibleArgumentMixin

A mixin class that provides standardized flexible argument handling for exception constructors:

```python
class FlexibleArgumentMixin:
    @staticmethod
    def _process_flexible_args(context_or_specific_param, suggested_action, specific_param_name, kwargs):
        # Handles multiple calling patterns uniformly
```

### Exception Class Categories

#### 1. Flexible Argument Exceptions
These exceptions support multiple calling patterns using the `FlexibleArgumentMixin`:

- `DataProcessingException`
- `FileProcessingException` 
- `ConfigurationException`
- `ValidationException`

#### 2. Standard **kwargs Exceptions
These exceptions use the standard `**kwargs` pattern:

- `CLIException` (hard-coded suggested action)
- `AuthenticationException` (hard-coded suggested action)
- `UIException` (hard-coded suggested action)
- `WorkflowException`
- `AgentException`
- `ModelException`
- `ExternalServiceException`

## Supported Calling Patterns

### Pattern 1: Keyword Arguments (Recommended)
```python
raise DataProcessingException(
    message="Failed to process data",
    data_type="csv_data",
    context={"file_path": "/path/to/file"},
    suggested_action="Check file format and permissions",
    details="Additional technical details"
)
```

### Pattern 2: Positional Arguments (Legacy Support)
```python
raise DataProcessingException(
    "Failed to process data",
    {"file_path": "/path/to/file", "error": "parse error"},
    "Check file format and permissions"
)
```

### Pattern 3: Mixed Arguments
```python
raise DataProcessingException(
    "Failed to process data",
    {"context_key": "context_value"},
    "Check configuration",
    details="Additional details"
)
```

### Pattern 4: Legacy Specific Parameter
```python
raise DataProcessingException(
    "Failed to process data",
    "csv_data"  # data_type parameter
)
```

## Exception Class Reference

### DataProcessingException
```python
DataProcessingException(
    message: str,
    context_or_data_type=None,  # str (data_type) or dict (context)
    suggested_action=None,
    **kwargs
)
```

### FileProcessingException
```python
FileProcessingException(
    message: str,
    context_or_file_path=None,  # str (file_path) or dict (context)
    suggested_action=None,
    **kwargs
)
```

### ConfigurationException
```python
ConfigurationException(
    message: str,
    context_or_config_key=None,  # str (config_key) or dict (context)
    suggested_action=None,
    **kwargs
)
```

### ValidationException
```python
ValidationException(
    message: str,
    context_or_field_name=None,  # str (field_name) or dict (context)
    suggested_action=None,
    **kwargs
)
```

### Standard Exceptions
```python
CLIException(message: str, **kwargs)
AuthenticationException(message: str, **kwargs)
UIException(message: str, **kwargs)
WorkflowException(message: str, workflow_name: str, step=None, **kwargs)
AgentException(message: str, agent_type: str, **kwargs)
ModelException(message: str, model_name=None, **kwargs)
ExternalServiceException(message: str, service_name: str, **kwargs)
```

## Best Practices

### 1. Use Keyword Arguments When Possible
```python
# ✅ Good - Clear and explicit
raise ConfigurationException(
    message="Missing API key",
    config_key="OPENAI_API_KEY",
    suggested_action="Set OPENAI_API_KEY environment variable"
)
```

### 2. Provide Meaningful Suggested Actions
```python
# ✅ Good - Actionable suggestion
suggested_action="Check file permissions and ensure the file exists"

# ❌ Bad - Vague suggestion
suggested_action="Fix the error"
```

### 3. Include Rich Context Information
```python
# ✅ Good - Rich context
raise DataProcessingException(
    message="Failed to parse CSV",
    context={
        "file_path": file_path,
        "line_number": line_num,
        "error_details": str(e)
    },
    suggested_action="Check CSV format and encoding"
)
```

### 4. Maintain Exception Chaining
```python
# ✅ Good - Preserves original exception
raise DataProcessingException(
    message="Processing failed",
    context={"operation": "data_transform"},
    suggested_action="Check input data format",
    original_exception=e
)
```

## Migration Guide

### Updating Existing Code

#### Before (Problematic)
```python
raise DataProcessingException(
    "Error message",
    {"context": "dict"},
    "suggested action"
)
# This would cause KeyError: 'suggested_action'
```

#### After (Fixed)
```python
raise DataProcessingException(
    "Error message",
    {"context": "dict"},
    "suggested action"
)
# Now works correctly with flexible argument handling
```

### Adding New Exception Classes

When creating new exception classes that need flexible argument handling:

1. Inherit from the appropriate base class and `FlexibleArgumentMixin`
2. Use the `_process_flexible_args` method in the constructor
3. Follow the established pattern for parameter handling

```python
class NewException(UtilityException, FlexibleArgumentMixin):
    def __init__(self, message: str, context_or_param=None, suggested_action=None, **kwargs):
        param_value, processed_kwargs = self._process_flexible_args(
            context_or_param, suggested_action, 'param_name', kwargs
        )
        
        super().__init__(
            message=message,
            error_code="NEW_001",
            **processed_kwargs
        )
        
        if param_value:
            self.context = self.context or {}
            self.context['param_name'] = param_value
```

## Testing

Use the provided test suite to verify exception handling:

```bash
python test_unified_exception_handling.py
```

The test suite verifies:
- Flexible argument patterns work correctly
- Standard **kwargs patterns work correctly  
- Backward compatibility is maintained
- suggested_action parameter is handled uniformly

## Benefits

1. **Consistency**: All exception classes handle `suggested_action` uniformly
2. **Backward Compatibility**: Existing code continues to work without changes
3. **Flexibility**: Supports multiple calling patterns for different use cases
4. **Maintainability**: Centralized logic in `FlexibleArgumentMixin`
5. **Error Prevention**: Eliminates KeyError: 'suggested_action' issues
6. **Rich Context**: Supports both simple parameters and complex context dictionaries

## Troubleshooting

### Common Issues

1. **KeyError: 'suggested_action'**: Ensure the exception class inherits from `FlexibleArgumentMixin` or properly handles `**kwargs`

2. **Missing Context**: Verify that context information is being passed correctly through the flexible argument handling

3. **Backward Compatibility**: Test existing code patterns to ensure they still work after updates

### Debug Tips

- Use the test suite to verify exception behavior
- Check that `suggested_action` is properly set in exception instances
- Verify that context information is preserved correctly
- Ensure exception chaining works with `original_exception` parameter
