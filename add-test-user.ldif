dn: cn=ldap-admin,ou=people,dc=company,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
cn: ldap-admin
sn: Administrator
givenName: LDAP
displayName: LDAP Administrator
uid: ldap-admin
uidNumber: 10000
gidNumber: 10000
homeDirectory: /home/<USER>
loginShell: /bin/bash
userPassword: ldap-admin123
mail: <EMAIL>
departmentNumber: admin
title: LDAP Administrator

dn: cn=john.doe,ou=people,dc=company,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
cn: john.doe
sn: Doe
givenName: John
displayName: John Doe
uid: john.doe
uidNumber: 10001
gidNumber: 10001
homeDirectory: /home/<USER>
loginShell: /bin/bash
userPassword: password123
mail: <EMAIL>
departmentNumber: finance
title: Financial Analyst
