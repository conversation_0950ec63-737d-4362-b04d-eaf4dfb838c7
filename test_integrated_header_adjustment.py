#!/usr/bin/env python3
"""
Test the integrated header adjustment in reform.py
"""

import sys
import os
from pathlib import Path
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def test_integrated_header_adjustment():
    """Test the integrated header adjustment"""
    print("="*80)
    print("TESTING INTEGRATED HEADER ADJUSTMENT")
    print("="*80)
    
    # Read the original table content
    with open('test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract just the table content without metadata
    lines = content.split('\n')
    
    # Find the start of the actual table content
    table_start = -1
    for i, line in enumerate(lines):
        if line.startswith('| 编制单位：'):
            table_start = i
            break
    
    if table_start == -1:
        print("❌ Could not find table start")
        return False
    
    # Extract just the table content
    table_content = '\n'.join(lines[table_start:])
    
    print("Original table structure:")
    table_lines = table_content.split('\n')
    for i, line in enumerate(table_lines[:6]):
        print(f"  Line {i+1}: {line}")
    
    print()
    
    # Process with integrated header adjustment
    filename = "1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted.md"
    
    reformer = MarkdownTableReformer()
    
    try:
        result = reformer.process_table(table_content, filename)
        print("✅ Processing with integrated header adjustment successful")
        
        # Show the result structure
        result_lines = result.split('\n')
        print("\nProcessed table structure:")
        for i, line in enumerate(result_lines[:10]):
            print(f"  Line {i+1}: {line}")
        
        # Check the header structure
        metadata_row = -1
        xiangmu_row = -1
        separator_row = -1
        
        for i, line in enumerate(result_lines):
            if '编制单位：' in line:
                metadata_row = i
            elif '项 目' in line and '本年累计' in line:
                xiangmu_row = i
            elif '---' in line and separator_row == -1:
                separator_row = i
        
        print(f"\nStructure analysis:")
        print(f"  Metadata row: {metadata_row}")
        print(f"  '项 目' row: {xiangmu_row}")
        print(f"  Separator row: {separator_row}")
        
        # Check if the structure is correct
        if xiangmu_row > metadata_row and separator_row == xiangmu_row + 1:
            print("✅ Header structure is correct!")
            print("  - Metadata row comes first")
            print("  - '项 目' header row comes second")
            print("  - Separator row comes immediately after header")
            structure_correct = True
        else:
            print("❌ Header structure needs improvement")
            structure_correct = False
        
        # Check date extraction
        metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
        if metadata_match:
            metadata_str = metadata_match.group(1)
            
            if '"date": "2024年9月"' in metadata_str:
                print("✅ Date extraction correct: '2024年9月'")
                date_correct = True
            else:
                print("❌ Date extraction incorrect")
                date_correct = False
        else:
            print("❌ No metadata found")
            date_correct = False
        
        # Write the corrected file
        if structure_correct and date_correct:
            output_file = 'test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed_corrected.md'
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"\n✅ Corrected file written to: {output_file}")
        
        return structure_correct and date_correct
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_final_result():
    """Verify the final corrected result"""
    print("\n" + "="*80)
    print("VERIFYING FINAL RESULT")
    print("="*80)
    
    try:
        with open('test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed_corrected.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("Final corrected file analysis:")
        
        # Check Issue 1: Date extraction
        metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', content)
        if metadata_match:
            metadata_str = metadata_match.group(1)
            print(f"Metadata: {metadata_str}")
            
            if '"date": "2024年9月"' in metadata_str:
                print("✅ Issue 1 RESOLVED: Date '2024年9月' correctly extracted")
                issue1_resolved = True
            else:
                print("❌ Issue 1 NOT RESOLVED: Date extraction incorrect")
                issue1_resolved = False
        else:
            print("❌ Issue 1 NOT RESOLVED: No metadata found")
            issue1_resolved = False
        
        # Check Issue 2: Header structure
        lines = content.split('\n')
        
        print("\nTable structure:")
        for i, line in enumerate(lines[:8]):
            print(f"  Line {i+1}: {line}")
        
        # Find the structure elements
        xiangmu_found = False
        separator_after_xiangmu = False
        
        for i, line in enumerate(lines):
            if '项 目' in line and '本年累计' in line:
                xiangmu_found = True
                print(f"\n'项 目' row found at line {i+1}")
                # Check if next line is separator
                if i + 1 < len(lines) and '---' in lines[i + 1]:
                    separator_after_xiangmu = True
                    print(f"Separator correctly positioned at line {i+2}")
                break
        
        if xiangmu_found and separator_after_xiangmu:
            print("✅ Issue 2 RESOLVED: Header structure correct")
            issue2_resolved = True
        else:
            print("❌ Issue 2 NOT RESOLVED: Header structure incorrect")
            issue2_resolved = False
        
        # Final summary
        if issue1_resolved and issue2_resolved:
            print("\n🎉 ALL ISSUES SUCCESSFULLY RESOLVED!")
            print("✅ Date extraction: '2024年9月' correctly extracted from filename")
            print("✅ Header detection: '项 目' row properly positioned")
            print("✅ Separator positioning: Correctly placed after header row")
            print("✅ Format preservation: Original date format maintained")
            print("✅ Multi-level headers: Proper structure achieved")
            return True
        else:
            print("\n❌ Some issues remain unresolved")
            return False
        
    except FileNotFoundError:
        print("❌ Corrected file not found")
        return False
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main function"""
    try:
        # Test integrated header adjustment
        success = test_integrated_header_adjustment()
        
        if success:
            # Verify final result
            final_success = verify_final_result()
            return 0 if final_success else 1
        else:
            return 1
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
