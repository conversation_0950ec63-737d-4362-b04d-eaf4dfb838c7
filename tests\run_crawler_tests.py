#!/usr/bin/env python3
"""
Crawler Error Handling Test Runner

This script runs comprehensive tests for the crawler unified error handling
implementation and provides detailed reporting on test results.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import test modules and crawler components
from crawler.exceptions import *
from crawler.utilities import sanitize_filename, ensure_output_dir
from crawler.session_manager import SessionManager
from crawler.logging_config import setup_crawler_logging

# Setup logging for test runner
setup_crawler_logging()
logger = logging.getLogger('test_runner')


class CrawlerTestRunner:
    """Test runner for crawler error handling validation"""
    
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results = []
    
    def run_test(self, test_name: str, test_func):
        """Run a single test and record results"""
        try:
            logger.info(f"Running test: {test_name}")
            if asyncio.iscoroutinefunction(test_func):
                asyncio.run(test_func())
            else:
                test_func()
            
            self.passed_tests += 1
            self.test_results.append((test_name, "PASSED", None))
            logger.info(f"✅ {test_name} PASSED")
            
        except Exception as e:
            self.failed_tests += 1
            self.test_results.append((test_name, "FAILED", str(e)))
            logger.error(f"❌ {test_name} FAILED: {str(e)}")
    
    def test_exception_creation(self):
        """Test basic exception creation and properties"""
        # Test BrowserException
        exc = BrowserException(
            message="Test browser error",
            browser_action="test_action"
        )
        assert exc.message == "Test browser error"
        assert exc.error_code == "CRAWLER_BROWSER_001"
        assert exc.context['browser_action'] == "test_action"
        
        # Test PageLoadException
        exc = PageLoadException(
            message="Page load failed",
            url="https://example.com",
            timeout=30
        )
        assert exc.context['url'] == "https://example.com"
        assert exc.context['timeout'] == 30
        
        # Test LoginException
        exc = LoginException(
            message="Login failed",
            login_status="invalid_credentials"
        )
        assert exc.context['login_status'] == "invalid_credentials"
    
    def test_utilities_error_handling(self):
        """Test error handling in utility functions"""
        # Test sanitize_filename with valid input
        result = sanitize_filename("test<>file|name?.txt")
        assert result == "test__file_name_.txt"
        
        # Test sanitize_filename with invalid input
        try:
            sanitize_filename(None)
            assert False, "Should have raised exception"
        except Exception as e:
            assert "Invalid filename input" in str(e)
        
        # Test ensure_output_dir with valid path
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = os.path.join(temp_dir, "test_output")
            ensure_output_dir(test_dir)
            assert os.path.exists(test_dir)
    
    async def test_session_manager_error_handling(self):
        """Test error handling in SessionManager"""
        import tempfile
        import json
        
        with tempfile.TemporaryDirectory() as temp_dir:
            session_file = os.path.join(temp_dir, "test_session.json")
            session_manager = SessionManager(session_file)
            
            # Test load_session with non-existent file
            from unittest.mock import AsyncMock
            mock_context = AsyncMock()
            result = await session_manager.load_session(mock_context)
            assert result is False
            
            # Test load_session with invalid JSON
            with open(session_file, 'w') as f:
                f.write("invalid json")
            
            try:
                await session_manager.load_session(mock_context)
                assert False, "Should have raised exception"
            except Exception as e:
                assert "Invalid session file format" in str(e)
    
    def test_unified_exception_handling(self):
        """Test unified exception handling patterns"""
        # Test direct exception creation instead of conversion
        login_exc = LoginException(
            message="Authentication failed",
            login_status="invalid_credentials"
        )
        assert isinstance(login_exc, LoginException)
        assert login_exc.context['login_status'] == "invalid_credentials"

        # Test timeout exception
        timeout_exc = TimeoutException(
            message="Operation timed out",
            operation="page load",
            timeout_duration=30
        )
        assert isinstance(timeout_exc, TimeoutException)
        assert timeout_exc.context['operation'] == "page load"

        # Test element exception
        element_exc = ElementNotFoundException(
            message="Element not found",
            selector="#test-element"
        )
        assert isinstance(element_exc, ElementNotFoundException)
        assert element_exc.context['selector'] == "#test-element"

        # Test network exception
        network_exc = NetworkException(
            message="Network connection failed",
            url="https://example.com"
        )
        assert isinstance(network_exc, NetworkException)
    
    def test_error_context_and_logging(self):
        """Test error context and logging functionality"""
        exc = BrowserException(
            message="Test error with context",
            browser_action="test_action",
            details="Detailed error information",
            context={'custom_field': 'custom_value'}
        )
        
        # Test context preservation
        assert exc.context['browser_action'] == "test_action"
        assert exc.context['custom_field'] == "custom_value"
        assert exc.details == "Detailed error information"
        
        # Test error logging (mock logger to avoid actual logging)
        from unittest.mock import Mock
        mock_logger = Mock()
        exc.log_error(mock_logger)
        mock_logger.error.assert_called_once()
    
    def test_error_boundary_functionality(self):
        """Test error boundary functionality"""
        from app.error_handling import error_boundary
        
        # Test error boundary with successful operation
        with error_boundary("test operation", "core"):
            result = "success"
        
        # Test error boundary with exception
        try:
            with error_boundary("test operation", "core"):
                raise ValueError("Test error")
        except ValueError as e:
            assert str(e) == "Test error"
    
    def test_crawler_layer_mapping(self):
        """Test crawler layer type mapping"""
        from crawler.exceptions import CrawlerLayerType
        
        # Test layer type constants
        assert hasattr(CrawlerLayerType, 'PRESENTATION')
        assert hasattr(CrawlerLayerType, 'SERVER')
        assert hasattr(CrawlerLayerType, 'CORE')
    
    def test_error_codes_consistency(self):
        """Test error code consistency across exceptions"""
        from crawler.exceptions import CRAWLER_ERROR_CODES
        
        # Test that error codes dictionary exists and has expected entries
        assert 'UI_ERROR' in CRAWLER_ERROR_CODES
        assert 'LOGIN_FAILED' in CRAWLER_ERROR_CODES
        assert 'BROWSER_ERROR' in CRAWLER_ERROR_CODES
        assert 'NETWORK_ERROR' in CRAWLER_ERROR_CODES
        
        # Test that error codes follow expected pattern
        for code in CRAWLER_ERROR_CODES.values():
            assert code.startswith('CRAWLER_')
            assert '_' in code
    
    def run_all_tests(self):
        """Run all crawler error handling tests"""
        logger.info("Starting crawler error handling tests...")
        
        # List of all tests to run
        tests = [
            ("Exception Creation", self.test_exception_creation),
            ("Utilities Error Handling", self.test_utilities_error_handling),
            ("Session Manager Error Handling", self.test_session_manager_error_handling),
            ("Unified Exception Handling", self.test_unified_exception_handling),
            ("Error Context and Logging", self.test_error_context_and_logging),
            ("Error Boundary Functionality", self.test_error_boundary_functionality),
            ("Crawler Layer Mapping", self.test_crawler_layer_mapping),
            ("Error Codes Consistency", self.test_error_codes_consistency),
        ]
        
        # Run each test
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test results summary"""
        total_tests = self.passed_tests + self.failed_tests
        
        print("\n" + "="*60)
        print("CRAWLER ERROR HANDLING TEST SUMMARY")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.failed_tests}")
        print(f"Success Rate: {(self.passed_tests/total_tests)*100:.1f}%")
        print("="*60)
        
        if self.failed_tests > 0:
            print("\nFAILED TESTS:")
            for test_name, status, error in self.test_results:
                if status == "FAILED":
                    print(f"❌ {test_name}: {error}")
        
        print("\nPASSED TESTS:")
        for test_name, status, error in self.test_results:
            if status == "PASSED":
                print(f"✅ {test_name}")
        
        print("\n" + "="*60)
        
        if self.failed_tests == 0:
            print("🎉 All tests passed! Crawler error handling is working correctly.")
        else:
            print(f"⚠️  {self.failed_tests} test(s) failed. Please review the errors above.")
        
        return self.failed_tests == 0


def main():
    """Main test runner function"""
    print("Crawler Error Handling Test Runner")
    print("="*60)
    
    # Create and run test runner
    runner = CrawlerTestRunner()
    success = runner.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
