#!/usr/bin/env python3
"""
Test script to verify UTF-8 logging configuration works correctly.
This script tests that Chinese characters are properly displayed in log files
without Unicode escape sequences.
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import and setup logging
from app.logging_config import setup_logging

def test_utf8_logging():
    """Test that Chinese characters are properly logged in UTF-8 format."""
    
    # Setup logging
    setup_logging()
    
    # Get loggers for different modules
    app_logger = logging.getLogger('app')
    crawler_logger = logging.getLogger('crawler')
    
    print("Testing UTF-8 logging configuration...")
    print("=" * 60)
    
    # Test messages with Chinese characters
    test_messages = [
        "开始提取年份和季度信息",
        "处理文件: 三季度各合作平台投诉情况统计表",
        "输出到: test3\\三季度各合作平台投诉情况统计表_reformed.md",
        "已删除原文件: 原始文件.md",
        "平台名称: 黑猫",
        "监管: 金融消保",
        "人行: 税务",
        "总计: 去重后投诉量",
        "已完成投诉量: 完成率",
        "平均处理时长(天): 催收问题",
        "催收问题占总投诉比: 费用问题",
        "费用问题占总投诉比: 统计完成"
    ]
    
    print("Logging test messages to app logger...")
    for i, message in enumerate(test_messages):
        if i % 4 == 0:
            app_logger.debug(message)
        elif i % 4 == 1:
            app_logger.info(message)
        elif i % 4 == 2:
            app_logger.warning(message)
        else:
            app_logger.error(message)
    
    print("Logging test messages to crawler logger...")
    for i, message in enumerate(test_messages):
        if i % 2 == 0:
            crawler_logger.info(f"爬虫模块: {message}")
        else:
            crawler_logger.debug(f"数据处理: {message}")
    
    print("\nTest messages logged successfully!")
    print("Check the following log files for UTF-8 encoded Chinese text:")
    print("- logs/app.log")
    print("- logs/crawler.log")
    print("\nThe Chinese characters should appear as readable text, not Unicode escape sequences.")
    
    # Also test some complex scenarios
    app_logger.info("复杂测试: 包含数字和符号的中文 - 2024年第3季度财务报表（资产负债表）")
    crawler_logger.error("错误信息: 无法解析文件 '财务数据_资产负债表.xlsx' - 编码问题")
    
    print("\nAdditional complex test messages logged.")
    print("=" * 60)

def check_log_files():
    """Check if log files contain proper UTF-8 encoded Chinese text."""
    
    logs_dir = Path("logs")
    if not logs_dir.exists():
        print("Logs directory does not exist yet. Run the test first.")
        return
    
    print("\nChecking log file contents...")
    print("=" * 60)
    
    for log_file in ["app.log", "crawler.log"]:
        log_path = logs_dir / log_file
        if log_path.exists():
            print(f"\nChecking {log_file}:")
            print("-" * 40)
            
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                # Show last few lines that contain Chinese characters
                chinese_lines = [line for line in lines[-20:] if any('\u4e00' <= char <= '\u9fff' for char in line)]
                
                if chinese_lines:
                    print(f"Found {len(chinese_lines)} recent lines with Chinese characters:")
                    for i, line in enumerate(chinese_lines[-5:], 1):  # Show last 5
                        print(f"{i}. {line.strip()}")
                        
                        # Check if line contains Unicode escape sequences
                        if '\\u' in line:
                            print("   ⚠️  WARNING: Contains Unicode escape sequences!")
                        else:
                            print("   ✅ OK: Proper UTF-8 encoding")
                else:
                    print("No recent lines with Chinese characters found.")
                    
            except Exception as e:
                print(f"Error reading {log_file}: {e}")
        else:
            print(f"{log_file} does not exist.")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        check_log_files()
    else:
        test_utf8_logging()
        print("\nTo check the log file contents, run:")
        print("python test_utf8_logging.py check")
