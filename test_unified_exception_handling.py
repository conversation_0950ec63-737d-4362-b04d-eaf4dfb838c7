#!/usr/bin/env python3
"""
Test script to verify the unified exception handling approach for suggested_action parameter.

This test verifies that all exception classes consistently handle the suggested_action
parameter and support flexible argument patterns where applicable.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.exceptions import (
    DataProcessingException, FileProcessingException, ConfigurationException,
    ValidationException, CLIException, AuthenticationException, UIException,
    WorkflowException, AgentException, ModelException, ExternalServiceException
)
from app.logging_config import setup_logging

# Setup logging
setup_logging()

def test_flexible_argument_exceptions():
    """Test exceptions that support flexible argument patterns"""
    print("🧪 Testing flexible argument pattern exceptions...")
    
    test_cases = [
        {
            'name': 'DataProcessingException',
            'class': DataProcessingException,
            'specific_param': 'data_type'
        },
        {
            'name': 'FileProcessingException', 
            'class': FileProcessingException,
            'specific_param': 'file_path'
        },
        {
            'name': 'ConfigurationException',
            'class': ConfigurationException,
            'specific_param': 'config_key'
        },
        {
            'name': 'ValidationException',
            'class': ValidationException,
            'specific_param': 'field_name'
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        print(f"\n   Testing {test_case['name']}...")
        exception_class = test_case['class']
        specific_param = test_case['specific_param']
        
        try:
            # Test 1: Keyword argument pattern
            exc1 = exception_class(
                message="Test error",
                **{specific_param: "test_value"},
                context={"key": "value"},
                suggested_action="Test action"
            )
            assert exc1.message == "Test error"
            assert exc1.context[specific_param] == "test_value"
            assert exc1.context["key"] == "value"
            assert exc1.suggested_action == "Test action"
            print(f"      ✅ Keyword argument pattern works")
            
            # Test 2: Positional argument pattern (context dict)
            exc2 = exception_class(
                "Test error positional",
                {"table": "test_table", "error": "test error"},
                "Positional action"
            )
            assert exc2.message == "Test error positional"
            assert exc2.context["table"] == "test_table"
            assert exc2.context["error"] == "test error"
            assert exc2.suggested_action == "Positional action"
            print(f"      ✅ Positional argument pattern works")
            
            # Test 3: Legacy specific parameter pattern
            exc3 = exception_class(
                "Test legacy",
                "legacy_value"
            )
            assert exc3.message == "Test legacy"
            assert exc3.context[specific_param] == "legacy_value"
            print(f"      ✅ Legacy specific parameter pattern works")
            
        except Exception as e:
            print(f"      ❌ {test_case['name']} failed: {e}")
            all_passed = False
    
    return all_passed

def test_standard_kwargs_exceptions():
    """Test exceptions that use standard **kwargs pattern"""
    print("\n🧪 Testing standard **kwargs pattern exceptions...")
    
    test_cases = [
        {
            'name': 'CLIException',
            'class': CLIException,
            'args': ["Test CLI error"],
            'expected_suggested_action': "Please check command syntax and arguments"
        },
        {
            'name': 'AuthenticationException', 
            'class': AuthenticationException,
            'args': ["Test auth error"],
            'expected_suggested_action': "Please check credentials and try again"
        },
        {
            'name': 'UIException',
            'class': UIException,
            'args': ["Test UI error"],
            'expected_suggested_action': "Please refresh the page and try again"
        },
        {
            'name': 'WorkflowException',
            'class': WorkflowException,
            'args': ["Test workflow error", "TestWorkflow"],
            'kwargs': {'suggested_action': 'Check workflow configuration'}
        },
        {
            'name': 'AgentException',
            'class': AgentException,
            'args': ["Test agent error", "TestAgent"],
            'kwargs': {'suggested_action': 'Check agent configuration'}
        },
        {
            'name': 'ModelException',
            'class': ModelException,
            'args': ["Test model error"],
            'kwargs': {'model_name': 'test_model', 'suggested_action': 'Check model availability'}
        },
        {
            'name': 'ExternalServiceException',
            'class': ExternalServiceException,
            'args': ["Test service error", "TestService"],
            'kwargs': {'suggested_action': 'Check service connectivity'}
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        print(f"\n   Testing {test_case['name']}...")
        exception_class = test_case['class']
        args = test_case['args']
        kwargs = test_case.get('kwargs', {})
        
        try:
            # Test with suggested_action passed as kwarg
            exc = exception_class(*args, **kwargs)
            assert exc.message == args[0]
            
            if 'expected_suggested_action' in test_case:
                # Hard-coded suggested action
                assert exc.suggested_action == test_case['expected_suggested_action']
                print(f"      ✅ Hard-coded suggested action works")
            elif 'suggested_action' in kwargs:
                # Custom suggested action
                assert exc.suggested_action == kwargs['suggested_action']
                print(f"      ✅ Custom suggested action works")
            else:
                print(f"      ✅ No suggested action (as expected)")
                
        except Exception as e:
            print(f"      ❌ {test_case['name']} failed: {e}")
            all_passed = False
    
    return all_passed

def test_backward_compatibility():
    """Test that existing code patterns still work"""
    print("\n🧪 Testing backward compatibility...")
    
    try:
        # Test existing patterns from the codebase
        
        # Pattern from config.py
        config_exc = ConfigurationException(
            message="Missing required configuration",
            config_key="OPENAI_API_KEY",
            details="Environment variable not set",
            suggested_action="Set OPENAI_API_KEY in environment or .env file"
        )
        assert config_exc.context['config_key'] == "OPENAI_API_KEY"
        assert config_exc.suggested_action == "Set OPENAI_API_KEY in environment or .env file"
        print("   ✅ ConfigurationException backward compatibility works")
        
        # Pattern from validation
        val_exc = ValidationException(
            message="Invalid input data",
            field_name="email",
            details="Email format is invalid",
            context={'provided_email': '<EMAIL>'}
        )
        assert val_exc.context['field_name'] == "email"
        assert val_exc.context['provided_email'] == '<EMAIL>'
        print("   ✅ ValidationException backward compatibility works")
        
        # Pattern from data_tools.py (positional)
        data_exc = DataProcessingException(
            "Failed to query data",
            {"table": "test_table", "error": "test error"},
            "Check table name and query parameters"
        )
        assert data_exc.context['table'] == "test_table"
        assert data_exc.suggested_action == "Check table name and query parameters"
        print("   ✅ DataProcessingException positional pattern works")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Backward compatibility test failed: {e}")
        return False

def main():
    """Run all unified exception handling tests"""
    print("🚀 Testing Unified Exception Handling Approach")
    print("=" * 60)
    
    tests = [
        ("Flexible Argument Exceptions", test_flexible_argument_exceptions),
        ("Standard **kwargs Exceptions", test_standard_kwargs_exceptions),
        ("Backward Compatibility", test_backward_compatibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All unified exception handling tests passed!")
        print("✅ Flexible argument patterns work consistently")
        print("✅ Standard **kwargs patterns work correctly")
        print("✅ Backward compatibility maintained")
        print("✅ suggested_action parameter handled uniformly")
        return 0
    else:
        print("\n❌ Some unified exception handling tests need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())
