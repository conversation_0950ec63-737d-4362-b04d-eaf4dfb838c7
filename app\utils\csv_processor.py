import pandas as pd
import logging
from io import String<PERSON>
from typing import List, Dict, Union
from .common import GroupType

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    FileProcessingException, DataProcessingException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

@log_and_reraise(logger, "CSV data loading")
def load_csv_data(csv_data: str) -> pd.DataFrame:
    """Load CSV data into pandas DataFrame with unified error handling."""
    set_operation_context("csv_data_loading")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if not csv_data:
            raise ValidationException(
                message="Empty CSV data provided",
                field_name="csv_data",
                details="CSV data cannot be empty or None",
                suggested_action="Provide valid CSV data string"
            )

        if not isinstance(csv_data, str):
            raise ValidationException(
                message="CSV data must be a string",
                field_name="csv_data",
                details=f"Expected string, got {type(csv_data)}",
                context={'input_type': type(csv_data).__name__},
                suggested_action="Provide a valid CSV data string"
            )

    # Load CSV data
    with error_boundary("CSV parsing", LayerType.UTILITY):
        logger.debug("Loading CSV data")
        try:
            df = pd.read_csv(StringIO(csv_data))

            if df.empty:
                raise ValidationException(
                    message="CSV data resulted in empty DataFrame",
                    field_name="csv_data",
                    details="CSV parsing resulted in no data rows",
                    suggested_action="Check CSV format and content"
                )

            logger.info(f"Successfully loaded CSV data with {len(df)} rows")
            return df

        except pd.errors.EmptyDataError:
            raise ValidationException(
                message="CSV data is empty or invalid",
                field_name="csv_data",
                details="CSV parsing failed due to empty or invalid data",
                suggested_action="Provide valid CSV data with headers and data rows"
            )
        except Exception as e:
            raise FileProcessingException(
                message="Failed to parse CSV data",
                file_path="csv_string",
                details=f"CSV parsing error: {str(e)}",
                original_exception=e,
                suggested_action="Check CSV format and encoding"
            )

@log_and_reraise(logger, "date preprocessing")
def preprocess_dates(df: pd.DataFrame, date_col: str, group_type: GroupType) -> pd.DataFrame:
    """Preprocess date column based on grouping type with unified error handling."""
    set_operation_context("date_preprocessing")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if df is None or df.empty:
            raise ValidationException(
                message="Empty or None DataFrame provided",
                field_name="df",
                details="DataFrame cannot be empty or None",
                suggested_action="Provide a valid DataFrame with data"
            )

        if not date_col or date_col not in df.columns:
            raise ValidationException(
                message="Date column not found in DataFrame",
                field_name="date_col",
                details=f"Column '{date_col}' not found in DataFrame columns: {list(df.columns)}",
                context={'available_columns': list(df.columns), 'requested_column': date_col},
                suggested_action="Check column name or provide a valid column name"
            )

        if not isinstance(group_type, GroupType):
            raise ValidationException(
                message="Invalid group type",
                field_name="group_type",
                details=f"Expected GroupType enum, got {type(group_type)}",
                context={'provided_type': type(group_type).__name__},
                suggested_action="Use a valid GroupType enum value"
            )

    # Process dates
    with error_boundary("date extraction", LayerType.UTILITY):
        logger.debug(f"Preprocessing dates for column: {date_col}, group_type: {group_type}")
        df = df.copy()

        try:
            dates = pd.to_datetime(df[date_col].str.extract(r'(\d{4})年(\d{1,2})月')[0] + '-' +
                                 df[date_col].str.extract(r'(\d{4})年(\d{1,2})月')[1] + '-01')

            logger.debug(f"Extracted {len(dates)} valid dates")

        except Exception as e:
            raise DataProcessingException(
                message="Failed to extract dates from column",
                details=f"Date extraction error: {str(e)}",
                original_exception=e,
                context={'date_col': date_col, 'sample_values': df[date_col].head().tolist()},
                suggested_action="Check date format in the column (expected: YYYY年MM月)"
            )

    # Apply grouping
    with error_boundary("date grouping", LayerType.UTILITY):
        try:
            if group_type == GroupType.YEAR:
                df['_group'] = dates.dt.year.astype(str) + '年'
                logger.debug("Applied YEAR grouping")
            elif group_type == GroupType.HALF_YEAR:
                df['_group'] = dates.dt.year.astype(str) + '年' + \
                              ['上半年' if m <= 6 else '下半年' for m in dates.dt.month]
                logger.debug("Applied HALF_YEAR grouping")
            elif group_type == GroupType.QUARTER:
                df['_group'] = dates.dt.year.astype(str) + '年' + \
                              dates.dt.quarter.astype(str) + '季度'
                logger.debug("Applied QUARTER grouping")
            else:
                raise ValidationException(
                    message="Unsupported group type for date preprocessing",
                    field_name="group_type",
                    details=f"Group type {group_type} is not supported for date preprocessing",
                    context={'group_type': group_type},
                    suggested_action="Use YEAR, HALF_YEAR, or QUARTER for date grouping"
                )

            logger.info(f"Date preprocessing completed. Added '_group' column")
            return df

        except Exception as e:
            if isinstance(e, ValidationException):
                raise
            raise DataProcessingException(
                message="Failed to apply date grouping",
                details=f"Grouping error: {str(e)}",
                original_exception=e,
                context={'group_type': group_type},
                suggested_action="Check date values and group type compatibility"
            )