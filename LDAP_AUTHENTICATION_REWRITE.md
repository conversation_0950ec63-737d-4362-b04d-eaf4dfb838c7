# LDAP Authentication Rewrite

## Overview

The `authenticate_user_ldap` function in `app/api/server.py` has been completely rewritten to follow the Go LDAP authentication pattern, implementing a secure two-step authentication process.

## Key Changes

### 1. Authentication Flow (Following Go Pattern)

The new implementation follows the same pattern as the Go code:

1. **Admin Bind**: First authenticate with admin credentials to establish a connection
2. **User Search**: Search for the user using admin credentials and the configured filter
3. **User Bind**: Validate the user's password by attempting to bind with their DN
4. **User Info Extraction**: Extract user information including nickname, username, and department

### 2. New Configuration Variables

The following new environment variables have been added:

```bash
# Admin credentials for user search (REQUIRED)
LDAP_ADMIN_DN=cn=admin,dc=company,dc=com
LDAP_ADMIN_PASSWORD=admin-password

# Attribute mappings (following Go code pattern)
LDAP_USERNAME_ATTR=sAMAccountName  # UNameKey equivalent
LDAP_NICKNAME_ATTR=cn              # NameKey equivalent

# Connection timeout (matching <PERSON>'s 5-second timeout)
LDAP_CONNECTION_TIMEOUT=5
```

### 3. Enhanced Error Handling

- **Connection timeouts**: Handles network timeouts and connection failures
- **Authentication failures**: Distinguishes between admin bind failures and user authentication failures
- **User not found**: Proper handling when user doesn't exist in LDAP
- **Configuration validation**: Validates required configuration before attempting authentication

### 4. Security Improvements

- **Admin credentials separation**: Admin credentials are only used for user search, not stored or exposed
- **Connection timeout**: Prevents hanging connections with configurable timeout
- **Proper connection cleanup**: Ensures LDAP connections are properly closed
- **Detailed logging**: Comprehensive logging for debugging and monitoring

## Configuration Examples

### Active Directory

```bash
AUTH_MODE=ldap
LDAP_URL=ldap://ad.company.com:389
LDAP_BASE_DN=dc=company,dc=com
LDAP_USER_FILTER=(sAMAccountName={username})
LDAP_DEPT_ATTR=department
LDAP_ADMIN_DN=cn=admin,dc=company,dc=com
LDAP_ADMIN_PASSWORD=admin-password
LDAP_USERNAME_ATTR=sAMAccountName
LDAP_NICKNAME_ATTR=cn
LDAP_CONNECTION_TIMEOUT=5
```

### OpenLDAP

```bash
AUTH_MODE=ldap
LDAP_URL=ldap://openldap.company.com:389
LDAP_BASE_DN=dc=company,dc=com
LDAP_USER_FILTER=(uid={username})
LDAP_DEPT_ATTR=ou
LDAP_ADMIN_DN=cn=admin,dc=company,dc=com
LDAP_ADMIN_PASSWORD=admin-password
LDAP_USERNAME_ATTR=uid
LDAP_NICKNAME_ATTR=cn
LDAP_CONNECTION_TIMEOUT=5
```

## User Information Extraction

The function now extracts and returns the following user information:

- **username**: From `LDAP_USERNAME_ATTR` (e.g., sAMAccountName)
- **nickname**: From `LDAP_NICKNAME_ATTR` (e.g., cn)
- **department**: From `LDAP_DEPT_ATTR` with fallbacks to other department attributes
- **workspace_id**: Set to department if available, otherwise username
- **email**: From mail attribute if available
- **title**: From title attribute if available
- **groups**: From memberOf attribute if available

## Error Scenarios

The function handles the following error scenarios:

1. **Missing configuration**: Returns `False` if required LDAP settings are missing
2. **Admin bind failure**: Returns `False` if admin credentials are invalid
3. **Connection timeout**: Returns `False` if LDAP server is unreachable
4. **User not found**: Returns `False` if user doesn't exist in LDAP directory
5. **Authentication failure**: Returns `False` if user password is incorrect
6. **LDAP errors**: Returns `False` for any LDAP-specific errors

## Testing

Use the updated `test_ldap_auth.py` script to test the new implementation:

```bash
python test_ldap_auth.py
```

The test script now includes all the new configuration variables and provides comprehensive testing of the authentication flow.

## Migration from Old Implementation

### Required Changes

1. **Add admin credentials** to your `.env` file:
   ```bash
   LDAP_ADMIN_DN=cn=admin,dc=company,dc=com
   LDAP_ADMIN_PASSWORD=admin-password
   ```

2. **Update attribute mappings** if needed:
   ```bash
   LDAP_USERNAME_ATTR=sAMAccountName
   LDAP_NICKNAME_ATTR=cn
   ```

3. **Set connection timeout** (optional, defaults to 5 seconds):
   ```bash
   LDAP_CONNECTION_TIMEOUT=5
   ```

### Backward Compatibility

The new implementation maintains backward compatibility with existing configuration variables:
- `LDAP_URL`
- `LDAP_BASE_DN`
- `LDAP_USER_FILTER`
- `LDAP_DEPT_ATTR`
- `LDAP_USE_TLS`

## Security Considerations

1. **Admin credentials**: Store admin credentials securely and use a dedicated service account with minimal privileges
2. **TLS encryption**: Enable TLS for production environments to encrypt LDAP traffic
3. **Connection timeout**: Keep timeout values reasonable to prevent resource exhaustion
4. **Logging**: Monitor authentication logs for suspicious activity

## Troubleshooting

### Common Issues

1. **Admin bind failure**: Check admin DN and password
2. **User not found**: Verify user filter and base DN
3. **Connection timeout**: Check network connectivity and LDAP server status
4. **TLS errors**: Verify TLS configuration and certificates

### Debug Logging

Enable debug logging to troubleshoot authentication issues:

```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

The function provides detailed debug information about each step of the authentication process.
