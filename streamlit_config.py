"""
Streamlit Configuration Module

This module provides early configuration for Streamlit to prevent torch.classes
and other module inspection issues that can cause RuntimeError during startup.

This should be imported before any modules that might load PyTorch or other
heavy ML libraries.
"""

import os
import sys

def configure_streamlit_early():
    """
    Configure Streamlit settings before any heavy imports.
    
    This function sets environment variables and Streamlit options to prevent
    file watcher and module inspection issues, particularly with PyTorch.
    """
    
    # Set environment variables to prevent torch issues
    os.environ['STREAMLIT_SERVER_FILE_WATCHER_TYPE'] = 'none'
    os.environ['STREAMLIT_SERVER_RUN_ON_SAVE'] = 'false'
    os.environ['STREAMLIT_RUNNER_MAGIC_ENABLED'] = 'false'
    os.environ['STREAMLIT_GLOBAL_DEVELOPMENT_MODE'] = 'false'
    
    # Prevent torch from being examined by file watchers
    # Note: TORCH_LOGS can cause issues, so we don't set it
    os.environ['TORCH_SHOW_CPP_STACKTRACES'] = '0'
    
    # Import and configure Streamlit
    try:
        import streamlit as st
        from streamlit import config as st_config
        
        # Core file watcher settings
        st_config.set_option('server.fileWatcherType', 'none')
        st_config.set_option('server.runOnSave', False)
        st_config.set_option('server.allowRunOnSave', False)
        
        # Security and CORS settings
        st_config.set_option('server.enableCORS', False)
        st_config.set_option('server.enableXsrfProtection', False)
        
        # Runner settings to prevent module inspection
        st_config.set_option('runner.magicEnabled', False)

        # Development mode settings
        st_config.set_option('global.developmentMode', False)
        
        # Additional settings to prevent module scanning
        try:
            st_config.set_option('server.headless', True)
        except:
            pass  # This option might not exist in all versions
            
        try:
            st_config.set_option('browser.gatherUsageStats', False)
        except:
            pass  # This option might not exist in all versions
            
        print("✅ Streamlit configured successfully to prevent torch.classes issues")
        return True
        
    except Exception as e:
        print(f"⚠️ Warning: Could not configure Streamlit: {e}")
        return False

def setup_torch_environment():
    """
    Set up environment variables to prevent torch-related issues.
    """
    
    # Prevent torch from creating unnecessary files/directories
    os.environ['TORCH_HOME'] = os.path.join(os.getcwd(), '.torch_cache')
    
    # Disable torch distributed logging
    os.environ['TORCH_DISTRIBUTED_DEBUG'] = 'OFF'
    
    # Prevent torch from examining system paths
    os.environ['TORCH_CUDNN_V8_API_DISABLED'] = '1'
    
    # Disable torch profiler
    os.environ['TORCH_PROFILER_ENABLED'] = '0'
    
    print("✅ Torch environment configured to prevent path inspection issues")

if __name__ == "__main__":
    # Test the configuration
    print("Testing Streamlit configuration...")
    configure_streamlit_early()
    setup_torch_environment()
    print("Configuration test completed.")
