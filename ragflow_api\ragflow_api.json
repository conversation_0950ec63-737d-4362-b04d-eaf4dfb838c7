{"info": {"_postman_id": "2249ddd2-468f-4d7c-aac0-563e64e095b8", "name": "ragflow_api", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "44272298"}, "item": [{"name": "数据集管理", "item": [{"name": "创建数据库", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\":\"test01\",\n    \"embedding_model\":\"bge-m3:latest\",\n    \"chunk_method\":\"one\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets"]}}, "response": []}, {"name": "删除数据集", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"ids\":[\"c020592c218611f08fdad67fd003ca6c\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets"]}}, "response": []}, {"name": "更改数据库配置", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\":\"updated_test01\",\n    \"chunk_method\":\"naive\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c"]}}, "response": []}, {"name": "列出数据库", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "url": {"raw": "http://*************/api/v1/datasets", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets"]}}, "response": []}]}, {"name": "数据集中的文件管理", "item": [{"name": "上传文档", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": ["/Users/<USER>/projects/data_v1/data_html/季度合并财报表.html", "/Users/<USER>/projects/data_v1/data_html/金额vintage.html", "/Users/<USER>/projects/data_v1/data_html/损益表.html", "/Users/<USER>/projects/data_v1/data_html/现金流量表.html", "/Users/<USER>/projects/data_v1/data_html/业务数据表.html", "/Users/<USER>/projects/data_v1/data_html/资产负债表_12月.html"]}]}, "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/documents", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "documents"]}}, "response": []}, {"name": "更改文档配置", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"资产负债表_12月.html\",\n    \"chunk_method\": \"one\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/documents/58bd7786219c11f088d6d67fd003ca6c", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "documents", "58bd7786219c11f088d6d67fd003ca6c"]}}, "response": []}, {"name": "列出文档信息", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/documents", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "documents"]}}, "response": []}, {"name": "删除文档", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"ids\":[\"58bd7786219c11f088d6d67fd003ca6c\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/documents", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "documents"]}}, "response": []}, {"name": "解析文档", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"document_ids\":[\"58bb59ec219c11f088d6d67fd003ca6c\",\"58b98fe0219c11f088d6d67fd003ca6c\",\"58b81bf6219c11f088d6d67fd003ca6c\",\"58b68a3e219c11f088d6d67fd003ca6c\",\"0352489e219c11f0afa6d67fd003ca6c\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/chunks", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "chunks"]}}, "response": []}, {"name": "终止解析文档", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"document_ids\":[\"0352489e219c11f0afa6d67fd003ca6c\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/chunks", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "chunks"]}}, "response": []}]}, {"name": "数据集中的数据块管理", "item": [{"name": "添加数据块", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"content\":\"Excel数据行号 A B C1 损益表2 单位:深圳信合元科技有限公司 日期:2024-9-303 项目 本月数 本年累计4 一、营业收入 160599476.67 1274659862.545 其中：主营业务收入 160599476.67 1274404841.486 其他业务收入 0 255021.067 减：营业成本 0 08 其中：主营业务成本 0 0\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/documents/01af587021a511f0b5d0d67fd003ca6c/chunks", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "documents", "01af587021a511f0b5d0d67fd003ca6c", "chunks"]}}, "response": []}, {"name": "列出数据块", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/documents/01af587021a511f0b5d0d67fd003ca6c/chunks", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "documents", "01af587021a511f0b5d0d67fd003ca6c", "chunks"]}}, "response": []}, {"name": "删除数据块", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/documents/01af587021a511f0b5d0d67fd003ca6c/chunks", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "documents", "01af587021a511f0b5d0d67fd003ca6c", "chunks"], "query": [{"key": "", "value": null, "disabled": true}]}}, "response": []}, {"name": "更改数据块", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"content\":\"update chunk\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/datasets/93bba954218611f091f7d67fd003ca6c/documents/3d66820a21ad11f0887fd67fd003ca6c/chunks/5abb91079b98f4d2", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "datasets", "93bba954218611f091f7d67fd003ca6c", "documents", "3d66820a21ad11f0887fd67fd003ca6c", "chunks", "5abb91079b98f4d2"]}}, "response": []}, {"name": "检索数据块", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"question\":\"请分析2022年到2024年到资产结构、资金流动及盈利能力等重点财务情况变动，并计算ROA，ROE，净利润，流动比例，资产负债率等相关财务比例指标，请分析公司的经营真实性，监控性，可持续性，同时参照金融行业的经营情况对比分析。\",\n    \"dataset_ids\":[\"93bba954218611f091f7d67fd003ca6c\"],\n    \"document_ids\":[\"3d66820a21ad11f0887fd67fd003ca6c\",\"01ad183021a511f0b5d0d67fd003ca6c\",\"58bb59ec219c11f088d6d67fd003ca6c\",\"58b98fe0219c11f088d6d67fd003ca6c\",\"58b81bf6219c11f088d6d67fd003ca6c\"],\n    \"top_k\":2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/retrieval", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "retrieval"]}}, "response": []}]}, {"name": "聊天助手管理", "item": [{"name": "创建聊天助手", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\":\"ai_report_assistant\",\n    \"dataset_ids\":[\"93bba954218611f091f7d67fd003ca6c\"],\n    \"llm\":{\"model_name\":\"qwq:latest\",\"temperature\":0.3,\"top_p\":0.5},\n    \"prompt\":{\"empty_response\":\"本知识库暂时没有该数据，请重新上传此类数据！\"}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/chats", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "chats"]}}, "response": []}, {"name": "更改聊天助手配置", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\"name\":\"report_assistant\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/chats/0aaec6b221b511f0ab10d67fd003ca6c", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "chats", "0aaec6b221b511f0ab10d67fd003ca6c"]}}, "response": []}, {"name": "删除聊天助手", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"ids\":[\"0aaec6b221b511f0ab10d67fd003ca6c\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/chats", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "chats"]}}, "response": []}, {"name": "列出聊天助手", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "url": {"raw": "http://*************/api/v1/chats", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "chats"]}}, "response": []}]}, {"name": "会话管理", "item": [{"name": "创建会话", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\":\"new_session\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/chats/da19ee0821b611f0a9bfd67fd003ca6c/sessions", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "chats", "da19ee0821b611f0a9bfd67fd003ca6c", "sessions"]}}, "response": []}, {"name": "更改对话配置", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\":\"update_session\",\n    \"user_id\":\"**************************\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/chats/da19ee0821b611f0a9bfd67fd003ca6c/sessions/751aaba821b811f0a3ecd67fd003ca6c", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "chats", "da19ee0821b611f0a9bfd67fd003ca6c", "sessions", "751aaba821b811f0a3ecd67fd003ca6c"]}}, "response": []}, {"name": "列出该聊天助手中的会话", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "url": {"raw": "http://*************/api/v1/chats/da19ee0821b611f0a9bfd67fd003ca6c/sessions", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "chats", "da19ee0821b611f0a9bfd67fd003ca6c", "sessions"]}}, "response": []}, {"name": "删除会话", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"ids\":[\"751aaba821b811f0a3ecd67fd003ca6c\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/chats/da19ee0821b611f0a9bfd67fd003ca6c/sessions", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "chats", "da19ee0821b611f0a9bfd67fd003ca6c", "sessions"]}}, "response": []}, {"name": "进入会话对话", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer ragflow-IxYTE2ZGY0MWE4OTExZjBiODU5MDJiMz", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"question\":\"通过数据分析处理，请分析2022年到2024年到资产结构、资金流动及盈利能力等重点财务情况变动，并计算ROA，ROE，净利润，流动比例，资产负债率等相关财务比例指标，请分析公司的经营真实性，监控性，可持续性，同时参照金融行业的经营情况对比分析。注意：如遇到需要计算的数据必须给出详细的计算过程；知识库检索到数据在计算过程中直接使用原来单位，不要换算单位，例如知识库检索到数据到是541,232,434,1238.00元，直接在回答中或者计算过程中给出541,232,434,1238.00元，不要转换成5.41万亿元。\",\n    \"stream\":true,\n    \"session_id\":\"dbf001fe230a11f0bded56977bf2fbfa\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://*************/api/v1/chats/da19ee0821b611f0a9bfd67fd003ca6c/completions", "protocol": "http", "host": ["10", "10", "160", "249"], "path": ["api", "v1", "chats", "da19ee0821b611f0a9bfd67fd003ca6c", "completions"]}}, "response": []}]}]}