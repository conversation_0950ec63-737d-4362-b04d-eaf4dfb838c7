#!/usr/bin/env python3
"""
Test script to verify the error_boundary fix in ReportAgent
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.agents.report import ReportAgent
from app.config import AgentConfig

def test_report_agent_fix():
    """Test the ReportAgent with the problematic input"""
    print("Testing ReportAgent with error_boundary fix")
    print("="*60)
    
    # Create a test configuration
    config = AgentConfig(
        workspace_id="test_workspace",
        llm_backend="OPENAI",  # or whatever backend you're using
        model_name="gpt-3.5-turbo"
    )
    
    try:
        # Create the agent
        print("Creating ReportAgent...")
        agent = ReportAgent(config)
        print("✅ ReportAgent created successfully")
        
        # Test the problematic input
        test_input = "友微科技2025年1季度报告"
        print(f"\nTesting with input: '{test_input}'")
        
        # Call process_message which was causing the error
        result = agent.process_message(
            message=test_input,
            current_company=None,
            current_year=None,
            current_quarter=None,
            extra_context={}
        )
        
        print("✅ process_message completed successfully")
        print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
        
        return True
        
    except AttributeError as e:
        if "'NoneType' object has no attribute 'add_context'" in str(e):
            print(f"❌ FAILED: Original error still present: {e}")
            return False
        else:
            print(f"❌ FAILED: Different AttributeError: {e}")
            return False
    except Exception as e:
        print(f"⚠️  Other error occurred (may be expected): {type(e).__name__}: {e}")
        # Other errors might be expected (like missing LLM config, etc.)
        # The important thing is that we don't get the NoneType error
        if "'NoneType' object has no attribute 'add_context'" not in str(e):
            print("✅ The specific NoneType error has been fixed")
            return True
        else:
            print("❌ The NoneType error is still present")
            return False

def main():
    """Main test function"""
    print("Testing error_boundary fix for ReportAgent")
    print("="*80)
    
    success = test_report_agent_fix()
    
    print("\n" + "="*80)
    if success:
        print("✅ TEST PASSED: error_boundary fix is working")
        return 0
    else:
        print("❌ TEST FAILED: error_boundary fix is not working")
        return 1

if __name__ == "__main__":
    sys.exit(main())
