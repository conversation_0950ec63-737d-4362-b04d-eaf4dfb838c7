# Comprehensive 4-File Logging System Documentation

## Overview

This document describes the comprehensive logging system implemented for the auto-report project. The system generates exactly 4 separate log files, each dedicated to a specific application component, with UTF-8 encoding support, proper log rotation, and thread-safe operations.

## Log Files Structure

### 1. `streamlit.log` - Streamlit UI Application Logs
- **Purpose**: Captures all logs from the Streamlit user interface application
- **Components**: 
  - `app/main.py` - Main Streamlit application
  - `run_streamlit.py` - Streamlit launcher
  - UI-related operations and user interactions
- **Size Limit**: 50MB with 5 backup files
- **Logger Names**: `app`, `app.main`

### 2. `server.log` - Server/API Component Logs  
- **Purpose**: Captures all logs from the FastAPI server and business logic
- **Components**:
  - `app/api/server.py` - Main API server
  - `app/workflows/` - Business logic workflows
  - `app/agents/` - AI agents
  - `app/utils/` - Utility functions
  - Uvicorn server logs
- **Size Limit**: 100MB with 5 backup files
- **Logger Names**: `app.api`, `app.api.server`, `uvicorn`, `app.workflows`, `app.agents`, `app.utils`

### 3. `crawler.log` - Crawler Component Logs
- **Purpose**: Captures all logs from the web crawler system
- **Components**:
  - `crawler/server.py` - Crawler server
  - `crawler/core.py` - Core crawler functionality
  - `crawler/streamlit_app.py` - Crawler UI
  - All crawler-related operations
- **Size Limit**: 100MB with 5 backup files
- **Logger Names**: `crawler`, `crawler.server`, `crawler.core`, `crawler.streamlit_app`

### 4. `cli.log` - CLI Component Logs
- **Purpose**: Captures all logs from command-line interface tools
- **Components**:
  - `app/cli/` - All CLI tools
  - `cli.py` - Main CLI script
  - Command-line operations and batch processing
- **Size Limit**: 50MB with 5 backup files
- **Logger Names**: `app.cli`, `__main__` (for CLI scripts)

## Key Features

### UTF-8 Encoding Support
- All log files use UTF-8 encoding to support international characters
- Leverages the existing `UTF8JsonFormatter` class for consistent JSON formatting
- Ensures proper handling of Chinese characters and other Unicode content

### Log Rotation and File Management
- Automatic log rotation when files reach size limits
- Configurable backup count (default: 5 backup files)
- Thread-safe file operations for concurrent logging

### Component-Specific Filtering
- **ComponentSpecificFilter**: Routes logs to appropriate files based on:
  - Layer context (presentation, server, crawler, cli)
  - Module name patterns
  - File path analysis
  - Logger name classification

### Context-Aware Logging
- **Request ID tracking**: Each request gets a unique identifier
- **Layer context**: Identifies which application layer generated the log
- **Operation context**: Tracks specific operations being performed
- **Crawler context**: Additional context for crawler operations (session ID, browser ID, page URL, etc.)

### Sensitive Data Protection
- **SensitiveDataFilter**: Automatically masks sensitive information
- API keys, private keys, and other secrets are replaced with `***MASKED***`
- Configurable patterns for different types of sensitive data

## Configuration

### Environment Variables
- `LOG_LEVEL`: Set logging level (DEBUG, INFO, WARNING, ERROR) - default: INFO
- `LOG_TO_FILE`: Enable/disable file logging (true/false) - default: true
- `CRAWLER_LOG_LEVEL`: Specific log level for crawler components - default: INFO

### Programmatic Configuration
```python
from app.logging_config import setup_logging, get_component_logger

# Initialize the logging system
setup_logging()

# Get component-specific loggers
streamlit_logger = get_component_logger('streamlit')
server_logger = get_component_logger('server')
crawler_logger = get_component_logger('crawler')
cli_logger = get_component_logger('cli')
```

## Usage Examples

### Streamlit Application Logging
```python
from app.logging_config import get_component_logger, set_operation_context

logger = get_component_logger('streamlit', 'app.main')
set_operation_context('user_interaction')

logger.info("User logged in successfully")
logger.warning("Session timeout approaching")
```

### Server/API Logging
```python
from app.logging_config import get_component_logger, set_layer_context

logger = get_component_logger('server', 'app.api.server')
set_layer_context('server')

logger.info("API endpoint called")
logger.error("Database connection failed")
```

### Crawler Logging
```python
from crawler.logging_config import get_crawler_logger, set_crawler_session_context

logger = get_crawler_logger('crawler.server')
set_crawler_session_context('session_123')

logger.info("Crawler session started")
logger.warning("Login required for target site")
```

### CLI Logging
```python
from app.logging_config import get_component_logger, set_layer_context

logger = get_component_logger('cli', 'app.cli.data_tools_cli')
set_layer_context('cli')

logger.info("CLI command executed")
logger.error("Invalid command arguments")
```

## Log Format

All logs use JSON format with the following structure:
```json
{
  "asctime": "2025-07-23 17:10:27,785",
  "level": "INFO",
  "name": "app.main",
  "process": 30624,
  "thread": 26948,
  "module": "main",
  "funcName": "user_login",
  "pathname": "/path/to/file.py",
  "lineno": 42,
  "message": "User logged in successfully",
  "layer": "presentation",
  "operation": "user_authentication",
  "request_id": "req_123456"
}
```

## Testing

Run the comprehensive test suite:
```bash
python test_logging_system.py
```

This test verifies:
- All 4 log files are created correctly
- Component-specific filtering works
- Sensitive data masking functions properly
- JSON formatting is consistent
- UTF-8 encoding handles international characters

## Migration from Previous System

The new system maintains backward compatibility while consolidating logs:
- Old `app.log`, `api.log`, `business_logic.log`, `utility.log` → `streamlit.log` + `server.log`
- Existing `crawler.log` → Enhanced `crawler.log` with better filtering
- New `cli.log` for all command-line operations

## Troubleshooting

### Common Issues
1. **Log files not created**: Check `LOG_TO_FILE` environment variable
2. **Logs in wrong files**: Verify logger names and component classification
3. **Missing context**: Ensure `set_layer_context()` and `set_operation_context()` are called
4. **Encoding issues**: All files use UTF-8 by default

### Debug Mode
Enable debug logging to troubleshoot issues:
```bash
export LOG_LEVEL=DEBUG
python your_application.py
```

## Performance Considerations

- Log rotation prevents disk space issues
- Component-specific filtering reduces I/O overhead
- Thread-safe operations support concurrent logging
- JSON format enables efficient log parsing and analysis

## Future Enhancements

- Log aggregation and centralized monitoring
- Real-time log streaming capabilities
- Enhanced filtering based on user roles
- Integration with external logging services
