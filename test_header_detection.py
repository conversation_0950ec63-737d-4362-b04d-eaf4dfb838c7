#!/usr/bin/env python3
"""
Test header detection for the profit statement
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reheader import MarkdownTableHeaderAdjuster

def main():
    """Test header detection"""
    print("Testing Header Detection for Profit Statement")
    print("="*60)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Read the profit statement file
    with open('test3\\-季度合并财报（2024年Q4）_利润表_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Processing profit statement...")
    
    # Parse the table
    rows = adjuster.parse_table(content)
    
    print(f"Total rows: {len(rows)}")
    print("\nFirst 5 rows:")
    for i, row in enumerate(rows[:5]):
        if adjuster.is_separator_row(row):
            print(f"  Row {i+1}: SEPARATOR")
        else:
            print(f"  Row {i+1}: {row}")
    
    # Find header candidates
    candidates = adjuster.find_all_header_candidates(rows)
    
    print(f"\nTop 5 header candidates:")
    for i, (row_idx, score) in enumerate(candidates[:5]):
        row = rows[row_idx]
        print(f"  {i+1}. Row {row_idx+1}: Score {score:.2f}")
        print(f"      Content: {row}")
        
        # Check if this is the '科目' row
        if '科目' in row:
            print(f"      ✅ This is the '科目' row!")
    
    # Check if '科目' row is detected as a header
    kemu_found = False
    for row_idx, score in candidates:
        if '科目' in rows[row_idx]:
            print(f"\n✅ '科目' row found at position {row_idx+1} with score {score:.2f}")
            kemu_found = True
            break
    
    if not kemu_found:
        print("\n❌ '科目' row not found in header candidates")
        return 1
    
    # Process the table to see the final result
    print("\nProcessing table with header adjustment...")
    result = adjuster.adjust_table(content)
    
    # Show first 8 lines of result
    result_lines = result.split('\n')
    print("\nFirst 8 lines of processed result:")
    for i, line in enumerate(result_lines[:8]):
        print(f"  {i+1}: {line}")
    
    # Check if '科目' appears in the header area
    header_structure_good = False
    for i, line in enumerate(result_lines[:8]):
        if '科目' in line and ('2024-12-31' in line or i <= 3):
            print(f"\n✅ '科目' found in header structure at line {i+1}")
            header_structure_good = True
            break
    
    if not header_structure_good:
        print("\n❌ '科目' not properly positioned in header structure")
        return 1
    
    print("\n✅ SUCCESS: Header detection working correctly")
    return 0

if __name__ == "__main__":
    sys.exit(main())
