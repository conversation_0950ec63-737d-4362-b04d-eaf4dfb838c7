import unittest
import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

from app.utils.reform import MarkdownTableReformer


class TestContentProcessor(unittest.TestCase):
    """测试内容处理器"""
    
    def setUp(self):
        self.processor = ContentProcessor()
    
    def test_preserve_non_table_content(self):
        """测试保留非表格内容"""
        content = """# 标题
一些文本内容
| 表头1 | 表头2 |
| --- | --- |
| 数据1 | 数据2 |"""
        
        non_table, table = self.processor.preserve_non_table_content(content)
        
        self.assertEqual(len(non_table), 2)
        self.assertIn("# 标题", non_table)
        self.assertIn("一些文本内容", non_table)
        self.assertIn("| 表头1 | 表头2 |", table)
    
    def test_extract_table_data(self):
        """测试表格数据提取"""
        content = """| 表头1 | 表头2 |
| --- | --- |
| 数据1 | 数据2 |
| 数据3 | 数据4 |"""
        
        table_data, separator_line = self.processor.extract_table_data(content)
        
        self.assertEqual(len(table_data), 3)
        self.assertEqual(separator_line, 1)
        self.assertEqual(table_data[0], ['表头1', '表头2'])
        self.assertEqual(table_data[1], ['数据1', '数据2'])
    
    def test_process_single_header_row(self):
        """测试单行表头处理"""
        header_rows = [['项目', '金额', '备注']]
        body_rows = [['收入', '1000', '主营业务']]
        
        final_header, final_body = self.processor.process_single_header_row(header_rows, body_rows)
        
        self.assertEqual(final_header, ['项目', '金额', '备注'])
        self.assertEqual(final_body, [['收入', '1000', '主营业务']])
    
    def test_process_multiple_header_rows(self):
        """测试多行表头处理"""
        header_rows = [
            ['利润表', '', ''],
            ['2024年9月', '', '单位：元'],
            ['项目', '本期金额', '上期金额']
        ]
        
        final_header, metadata = self.processor.process_multiple_header_rows(header_rows)
        
        self.assertEqual(final_header, ['项目', '本期金额', '上期金额'])
        self.assertIn('利润表', metadata)
        self.assertIn('2024年9月', metadata)
        self.assertIn('单位：元', metadata)


class TestMetadataExtractor(unittest.TestCase):
    """测试元数据提取器"""
    
    def setUp(self):
        self.extractor = MetadataExtractor()
    
    def test_extract_from_headers(self):
        """测试从表头提取元数据"""
        header_rows = [
            ['利润表', '', ''],
            ['编制单位：测试公司', '', '单位：元'],
            ['项目', '本期金额', '上期金额']
        ]
        
        table_name, metadata = self.extractor.extract_from_headers(header_rows)
        
        self.assertEqual(table_name, '利润表')
        self.assertIn('编制单位：测试公司', metadata)
        self.assertIn('单位：元', metadata)
    
    def test_extract_key_value_pairs(self):
        """测试键值对提取"""
        metadata_list = ['编制单位：测试公司', '单位：元', '时间：2024年9月']
        
        key_value_pairs = self.extractor.extract_key_value_pairs(metadata_list)
        
        self.assertEqual(key_value_pairs['编制单位'], '测试公司')
        self.assertEqual(key_value_pairs['单位'], '元')
        self.assertEqual(key_value_pairs['时间'], '2024年9月')


class TestFilenameAnalyzer(unittest.TestCase):
    """测试文件名分析器"""
    
    def setUp(self):
        self.analyzer = FilenameAnalyzer()
    
    def test_extract_time_from_filename(self):
        """测试从文件名提取时间"""
        test_cases = [
            ('财务报表2024年9月_A1C25_Sheet1.md', '2024-09-30'),
            ('数据统计（2024年一季度）_A1C25_Sheet1.md', '2024-03-31'),
            ('报告_2024-06-30_A1C25_Sheet1.md', '2024-06-30'),
            ('Q1数据_A1C25_Sheet1.md', f'{2025}-03-31'),  # 当前年份
        ]
        
        for filename, expected in test_cases:
            with self.subTest(filename=filename):
                result = self.analyzer.extract_time_from_filename(filename)
                self.assertEqual(result, expected)
    
    def test_extract_table_name_from_filename(self):
        """测试从文件名提取表名"""
        test_cases = [
            ('三季度各合作平台投诉情况统计表_三季度投诉分析_A1O23_Sheet1.md', 
             '三季度各合作平台投诉情况统计表'),
            ('财务报表（2024.09）_利润表_A32C34_Sheet1.xlsx', 
             '财务报表_利润表'),
        ]
        
        for filename, expected in test_cases:
            with self.subTest(filename=filename):
                result = self.analyzer.extract_table_name_from_filename(filename)
                self.assertEqual(result, expected)


class TestOutputGenerator(unittest.TestCase):
    """测试输出生成器"""
    
    def setUp(self):
        self.generator = OutputGenerator()
    
    def test_generate_json_metadata(self):
        """测试JSON元数据生成"""
        metadata = {
            "文件名": "test.md",
            "表名": "利润表",
            "时间": "2024-09-30",
            "元数据": ["编制单位：测试公司", "单位：元"]
        }
        
        result = self.generator.generate_json_metadata(metadata)
        
        self.assertIsInstance(result, str)
        self.assertIn('"文件名":"test.md"', result)
        self.assertIn('"表名":"利润表"', result)
    
    def test_generate_markdown_title(self):
        """测试markdown标题生成"""
        result = self.generator.generate_markdown_title("利润表")
        self.assertEqual(result, "## 利润表")
        
        result = self.generator.generate_markdown_title("")
        self.assertEqual(result, "")
    
    def test_generate_final_output(self):
        """测试最终输出生成"""
        non_table_content = ["# 财务报告", ""]
        metadata = {"表名": "利润表", "时间": "2024-09-30"}
        table_name = "利润表"
        header = ["项目", "金额"]
        body_rows = [["收入", "1000"], ["支出", "800"]]
        
        result = self.generator.generate_final_output(
            non_table_content, metadata, table_name, header, body_rows
        )
        
        self.assertIn("# 财务报告", result)
        self.assertIn('"表名":"利润表"', result)
        self.assertIn("## 利润表", result)
        self.assertIn("| 项目 | 金额 |", result)
        self.assertIn("| 收入 | 1000 |", result)


class TestMarkdownTableReformer(unittest.TestCase):
    """测试主要的重整器类"""
    
    def setUp(self):
        self.reformer = MarkdownTableReformer()
    
    def test_process_content_new_simple_table(self):
        """测试新的内容处理方法 - 简单表格"""
        content = """# 财务数据
| 项目 | 金额 |
| --- | --- |
| 收入 | 1000 |
| 支出 | 800 |"""
        
        result = self.reformer.process_content_new(content, "财务报表_2024年9月_A1C25_Sheet1.md")
        
        self.assertIn("财务报表_2024年9月_A1C25_Sheet1.md", result)
        self.assertIn("2024-09-30", result)
        self.assertIn("| 项目 | 金额 |", result)
    
    def test_process_content_new_complex_table(self):
        """测试新的内容处理方法 - 复杂表格"""
        content = """| 利润表 | | |
| --- | --- | --- |
| 2024年9月 | | 单位：元 |
| 编制单位：测试公司 | | |
| 项目 | 本期金额 | 上期金额 |
| 营业收入 | 1000000 | 900000 |
| 营业成本 | 600000 | 550000 |"""
        
        result = self.reformer.process_content_new(content, "利润表_A1C25_Sheet1.md")
        
        self.assertIn('"表名":"利润表"', result)
        self.assertIn('编制单位', result)
        self.assertIn('单位', result)
        self.assertIn("## 利润表", result)
        self.assertIn("| 项目 | 本期金额 | 上期金额 |", result)


class TestEdgeCases(unittest.TestCase):
    """测试边缘情况"""

    def setUp(self):
        self.reformer = MarkdownTableReformer()

    def test_empty_content(self):
        """测试空内容"""
        result = self.reformer.process_content_new("", "")
        self.assertEqual(result, "")

    def test_no_table_separator(self):
        """测试没有表格分隔符的情况"""
        content = """| 表头1 | 表头2 |
| 数据1 | 数据2 |"""

        result = self.reformer.process_content_new(content, "test.md")
        self.assertEqual(result, content)  # 应该返回原内容

    def test_malformed_table(self):
        """测试格式错误的表格"""
        content = """| 表头1 | 表头2
| --- | ---
数据1 | 数据2 |"""

        result = self.reformer.process_content_new(content, "test.md")
        # 应该能够处理或返回原内容
        self.assertIsInstance(result, str)

    def test_complex_filename_patterns(self):
        """测试复杂的文件名模式"""
        analyzer = FilenameAnalyzer()

        test_cases = [
            ('财务报表（2024年一季度数据）_利润表_A1C34_Sheet1.xls', '财务报表_利润表'),
            ('2024年Q1业务数据_客户分析_B2D50_Sheet2.xlsx', '业务数据_客户分析'),
            ('投诉统计_2024.9_分析报告_C1E25_Sheet1.md', '投诉统计_分析报告'),
        ]

        for filename, expected_table in test_cases:
            with self.subTest(filename=filename):
                table_name = analyzer.extract_table_name_from_filename(filename)
                self.assertEqual(table_name, expected_table)

    def test_various_time_formats(self):
        """测试各种时间格式"""
        analyzer = FilenameAnalyzer()

        test_cases = [
            ('报告_2024年_A1C25_Sheet1.md', '2024-12-31'),
            ('数据_2023年9月_A1C25_Sheet1.md', '2023-09-30'),
            ('统计_2024年一季度_A1C25_Sheet1.md', '2024-03-31'),
            ('分析_一季度_A1C25_Sheet1.md', f'{2025}-03-31'),
            ('报表_Q1_A1C25_Sheet1.md', f'{2025}-03-31'),
            ('数据_2024年Q1_A1C25_Sheet1.md', '2024-03-31'),
            ('报告_2024年6月30日_A1C25_Sheet1.md', '2024-06-30'),
            ('统计_2024-06-30_A1C25_Sheet1.md', '2024-06-30'),
            ('分析_2024.9_A1C25_Sheet1.md', '2024-09-30'),
        ]

        for filename, expected_time in test_cases:
            with self.subTest(filename=filename):
                time_result = analyzer.extract_time_from_filename(filename)
                self.assertEqual(time_result, expected_time)

    def test_metadata_with_special_characters(self):
        """测试包含特殊字符的元数据"""
        extractor = MetadataExtractor()

        metadata_list = [
            '编制单位：深圳无域科技技术有限公司',
            '单位：万元',
            '时间：2024年9月30日',
            '备注：包含特殊字符@#$%'
        ]

        key_value_pairs = extractor.extract_key_value_pairs(metadata_list)

        self.assertEqual(key_value_pairs['编制单位'], '深圳无域科技技术有限公司')
        self.assertEqual(key_value_pairs['单位'], '万元')
        self.assertEqual(key_value_pairs['时间'], '2024年9月30日')
        self.assertEqual(key_value_pairs['备注'], '包含特殊字符@#$%')


class TestIntegration(unittest.TestCase):
    """集成测试"""

    def setUp(self):
        self.reformer = MarkdownTableReformer()

    def test_real_world_example_1(self):
        """测试真实世界示例1 - 利润表"""
        content = """# Sheet1
## Sheet1
|  |  |  |
| --- | --- | --- |
| 利润表 |  |  |
| 2024 年 9 月 |  | 会企02表 |
| 编制单位：深圳无域科技技术有限公司 |  | 单位：元 |
| 项 目 | 本年累计金额 | 上年金额 |
| 一、营业收入 | 647360144.68 | 761704361.12 |
| 减:营业成本 | 149986190.07 | 178548137.18 |"""

        result = self.reformer.process_content_new(content, "利润表_A1C25_Sheet1.md")

        # 验证JSON元数据
        self.assertIn('"表名":"利润表"', result)
        self.assertIn('编制单位', result)
        self.assertIn('单位', result)

        # 验证markdown标题
        self.assertIn('## 利润表', result)

        # 验证表格结构
        self.assertIn('| 项 目 | 本年累计金额 | 上年金额 |', result)
        self.assertIn('| 一、营业收入 | 647360144.68 | 761704361.12 |', result)

    def test_real_world_example_2(self):
        """测试真实世界示例2 - 复杂表头"""
        content = """| 三季度各合作平台投诉情况统计表 |  |  |  |
| --- | --- | --- | --- |
| 时间：2024年第三季度 | 编制：客服部 |  |  |
| 序号 | 平台名称 | 投诉数量 | 处理状态 |
| 1 | 平台A | 25 | 已处理 |
| 2 | 平台B | 18 | 处理中 |"""

        result = self.reformer.process_content_new(
            content,
            "三季度各合作平台投诉情况统计表_三季度投诉分析_A1O23_Sheet1.md"
        )

        # 验证表名提取
        self.assertIn('"表名":"三季度各合作平台投诉情况统计表"', result)

        # 验证元数据提取
        self.assertIn('时间', result)
        self.assertIn('编制', result)

        # 验证表格结构
        self.assertIn('| 序号 | 平台名称 | 投诉数量 | 处理状态 |', result)

    def test_backward_compatibility(self):
        """测试向后兼容性"""
        content = """| 表头1 | 表头2 |
| --- | --- |
| 数据1 | 数据2 |"""

        # 测试新方法
        result_new = self.reformer.process_content_new(content, "test.md")

        # 测试旧方法（如果存在）
        if hasattr(self.reformer, 'process_table'):
            result_old = self.reformer.process_table(content, "test.md")
            # 两种方法都应该能处理基本表格
            self.assertIn('表头1', result_new)
            self.assertIn('表头1', result_old)


if __name__ == '__main__':
    unittest.main()
