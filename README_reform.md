# Markdown表格重整工具 (reform.py)

这是一个用于重整Markdown表格格式的Python工具，能够自动处理多级表头合并和元数据提取。

## 功能特性

1. **元数据提取**：自动识别并提取表格中的元数据信息
   - 表名（包含"表"、"数据"、"统计"、"分析"等关键词）
   - 时间信息（截至时间、年月日期等）
   - 公司信息（编制单位、公司名称等）
   - 单位信息（单位、会企表等）

2. **多级表头处理**：将多级表头垂直合并为单级表头
   - 格式：主表头(子表头1,子表头2)
   - 自动处理空单元格和重复内容

3. **表格重整**：
   - 用提取的表名作为markdown标题
   - 将元数据作为独立文本放在表格前
   - 清理空行和分隔行
   - 标准化表格格式

## 安装要求

- Python 3.6+
- 无需额外依赖包

## 使用方法

### 命令行使用

```bash
# 基本用法
python reform.py input_file.md

# 指定输出文件
python reform.py input_file.md -o output_file.md

# 查看帮助
python reform.py -h
```

### 编程接口使用

```python
from reform import MarkdownTableReformer

# 创建重整器实例
reformer = MarkdownTableReformer()

# 重整markdown内容
content = "你的markdown内容"
reformed_content = reformer.reform_markdown(content)

print(reformed_content)
```

## 转换示例

### 示例1：多级表头合并

**输入：**
```markdown
# 利润表
## **公司:厦门友微合并 单位:元**
|                                                                | 月度                | 季度                   | 年度                  |
|----------------------------------------------------------------|-------------------|----------------------|---------------------|
| 科目                                                             | 2024-12-31        | 2024年Q4              | 2024年               |
| 一、营业收入                                                         |1125744936.81|2394080012.57|7174902973.52|
| 减:营业成本                                                         |167123826.95|696944946.75|2780835002.55|
```

**输出：**
```markdown
# 利润表
## **公司:厦门友微合并 单位:元**

|  | 月度(2024-12-31) | 季度(2024年Q4) | 年度(2024年) |
| --- | --- | --- | --- |
| 科目 |  |  |  |
| 一、营业收入 | 1125744936.81 | 2394080012.57 | 7174902973.52 |
| 减:营业成本 | 167123826.95 | 696944946.75 | 2780835002.55 |
```

### 示例2：元数据提取

**输入：**
```markdown
# Sheet1
## Sheet1
|  |  |  |
| --- | --- | --- |
| 截至时间：2024年9月30日 |  |  |
| 序号 | 资金方 | 在贷（亿） |
|1| 众邦银行 |26.296857|
|2| 中关村银行 |12.744644|
```

**输出：**
```markdown
# Sheet1
## Sheet1

截至时间：2024年9月30日

| 序号 | 资金方 | 在贷（亿） |
| --- | --- | --- |
| 1 | 众邦银行 | 26.296857 |
| 2 | 中关村银行 | 12.744644 |
```

### 示例3：复杂元数据处理

**输入：**
```markdown
# Sheet1
## Sheet1
|  |  |  |
| --- | --- | --- |
| 利润表 |  |  |
| 2024 年 9 月 |  | 会企02表 |
| 编制单位：深圳无域科技技术有限公司 |  | 单位：元 |
| 项 目 | 本年累计金额 | 上年金额 |
| 一、营业收入 | 647360144.68 | 761704361.12 |
| 减:营业成本 | 149986190.07 | 178548137.18 |
```

**输出：**
```markdown
# 利润表(会企02表)
2024 年 9 月
编制单位：深圳无域科技技术有限公司
单位：元

| 项 目 | 本年累计金额 | 上年金额 |
| --- | --- | --- |
| 一、营业收入 | 647360144.68 | 761704361.12 |
| 减:营业成本 | 149986190.07 | 178548137.18 |
```

## 算法原理

### 元数据识别模式

工具使用正则表达式识别以下类型的元数据：

1. **表名模式**：
   - `^([^|]*(?:表|数据|统计|分析|报告|清单|明细)(?:[^|]*)?)\s*$`
   - `^(\d+、[^|]+)\s*$`
   - `^([一二三四五六七八九十]+、[^|]+)\s*$`

2. **时间信息模式**：
   - `(截至时间[：:][^|]+)`
   - `(\d{4}年\d{1,2}月)`
   - `(\d{4}-\d{2}-\d{2})`
   - `(\d{4}年Q\d)`

3. **公司信息模式**：
   - `(编制单位[：:][^|]+)`
   - `(公司[：:][^|]+)`

4. **单位信息模式**：
   - `(单位[：:][^|]+)`
   - `(会企\d+表)`

### 处理流程

1. **内容分割**：按表格将文档分割为多个段落
2. **表格解析**：将每个表格解析为行列结构
3. **元数据提取**：识别并提取元数据行
4. **表头处理**：合并多级表头为单级
5. **格式重整**：重新组织内容结构
6. **输出生成**：生成标准化的markdown格式

## 注意事项

1. 输入文件必须是UTF-8编码的markdown文件
2. 表格必须使用标准的markdown表格格式（使用`|`分隔）
3. 工具会自动跳过分隔行（如`--- | --- | ---`）
4. 空行和纯空白单元格会被自动清理
5. 数据行的识别基于是否包含3位以上的数字

## 测试

运行测试脚本：

```bash
python test_reform.py
```

这将测试所有示例场景并生成输出文件。

## 许可证

MIT License
