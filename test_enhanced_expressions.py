#!/usr/bin/env python3
"""
Test script to demonstrate enhanced mathematical expression support in query_sqlite.

This script shows how the enhanced query_sqlite function now supports:
- Basic arithmetic: +, -, *, /
- Parentheses for grouping: ()
- Complex expressions like: (col1 + col2) * col3 / col4 as result
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from app.utils.data_tools import query_sqlite, load_to_sqlite
import pandas as pd
import sqlite3

def create_sample_data():
    """Create sample data for testing mathematical expressions."""
    # Create sample CSV data
    data = {
        '日期': ['2024-01-01', '2024-02-01', '2024-03-01', '2024-04-01'],
        '收入': [1000, 1200, 1100, 1300],
        '成本': [600, 700, 650, 750],
        '数量': [100, 120, 110, 130],
        '单价': [10, 10, 10, 10]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_data.csv', index=False)
    print("Created test_data.csv with sample data")
    
    # Load into SQLite
    result = load_to_sqlite('test_data.csv', 'test_table', '日期')
    print(result)

def test_basic_expressions():
    """Test basic mathematical expressions."""
    print("\n=== Testing Basic Mathematical Expressions ===")
    
    # Test 1: Simple addition
    print("\n1. Simple addition: 收入 + 成本 as 总额")
    result = query_sqlite(
        table='test_table',
        columns='日期,收入 + 成本 as 总额',
        output_format='table'
    )
    print(result)
    
    # Test 2: Simple subtraction (profit)
    print("\n2. Simple subtraction: 收入 - 成本 as 利润")
    result = query_sqlite(
        table='test_table',
        columns='日期,收入 - 成本 as 利润',
        output_format='table'
    )
    print(result)
    
    # Test 3: Simple multiplication
    print("\n3. Simple multiplication: 数量 * 单价 as 总价值")
    result = query_sqlite(
        table='test_table',
        columns='日期,数量 * 单价 as 总价值',
        output_format='table'
    )
    print(result)
    
    # Test 4: Simple division (profit margin)
    print("\n4. Simple division: (收入 - 成本) / 收入 as 利润率")
    result = query_sqlite(
        table='test_table',
        columns='日期,(收入 - 成本) / 收入 as 利润率',
        output_format='table'
    )
    print(result)

def test_complex_expressions():
    """Test complex mathematical expressions with parentheses."""
    print("\n=== Testing Complex Mathematical Expressions ===")
    
    # Test 1: Complex expression with parentheses
    print("\n1. Complex expression: (收入 + 成本) * 数量 / 单价 as 复合指标")
    result = query_sqlite(
        table='test_table',
        columns='日期,(收入 + 成本) * 数量 / 单价 as 复合指标',
        output_format='table'
    )
    print(result)
    
    # Test 2: Multiple operations
    print("\n2. Multiple operations: 收入 * 2 - 成本 + 数量 as 综合评分")
    result = query_sqlite(
        table='test_table',
        columns='日期,收入 * 2 - 成本 + 数量 as 综合评分',
        output_format='table'
    )
    print(result)
    
    # Test 3: Nested parentheses
    print("\n3. Nested parentheses: ((收入 - 成本) * 数量) / (单价 + 1) as 效率指标")
    result = query_sqlite(
        table='test_table',
        columns='日期,((收入 - 成本) * 数量) / (单价 + 1) as 效率指标',
        output_format='table'
    )
    print(result)

def test_multiple_expressions():
    """Test multiple expressions in one query."""
    print("\n=== Testing Multiple Expressions in One Query ===")
    
    result = query_sqlite(
        table='test_table',
        columns='日期,收入 - 成本 as 利润,收入 + 成本 as 总额,(收入 - 成本) / 收入 as 利润率',
        output_format='table'
    )
    print(result)

def test_with_grouping():
    """Test expressions with time grouping."""
    print("\n=== Testing Expressions with Time Grouping ===")
    
    # Create more sample data for grouping test
    data = {
        '日期': ['2024-01-01', '2024-01-15', '2024-02-01', '2024-02-15', 
                '2024-03-01', '2024-03-15', '2024-04-01', '2024-04-15'],
        '收入': [1000, 1100, 1200, 1250, 1100, 1150, 1300, 1350],
        '成本': [600, 650, 700, 720, 650, 680, 750, 780],
        '数量': [100, 110, 120, 125, 110, 115, 130, 135],
        '单价': [10, 10, 10, 10, 10, 10, 10, 10]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('test_data_grouped.csv', index=False)
    load_to_sqlite('test_data_grouped.csv', 'test_table_grouped', '日期')
    
    print("\n1. Monthly grouping with profit calculation:")
    result = query_sqlite(
        table='test_table_grouped',
        columns='收入 - 成本 as 月度利润',
        group_by='month',
        output_format='table'
    )
    print(result)
    
    print("\n2. Quarterly grouping with profit margin:")
    result = query_sqlite(
        table='test_table_grouped',
        columns='(收入 - 成本) / 收入 as 季度利润率',
        group_by='quarter',
        output_format='table'
    )
    print(result)

def test_strict_mode():
    """Test strict mode to show only calculated columns."""
    print("\n=== Testing Strict Mode ===")
    
    print("\n1. Without strict mode (shows all columns):")
    result = query_sqlite(
        table='test_table',
        columns='收入 - 成本 as 利润',
        strict=False,
        output_format='table'
    )
    print(result)
    
    print("\n2. With strict mode (shows only specified columns):")
    result = query_sqlite(
        table='test_table',
        columns='日期,收入 - 成本 as 利润',
        strict=True,
        output_format='table'
    )
    print(result)

def main():
    """Run all tests."""
    print("Enhanced Mathematical Expression Support Test")
    print("=" * 50)
    
    # Create sample data
    create_sample_data()
    
    # Run tests
    test_basic_expressions()
    test_complex_expressions()
    test_multiple_expressions()
    test_with_grouping()
    test_strict_mode()
    
    print("\n" + "=" * 50)
    print("All tests completed successfully!")
    print("\nSupported mathematical operations:")
    print("- Addition: +")
    print("- Subtraction: -")
    print("- Multiplication: *")
    print("- Division: /")
    print("- Parentheses: ()")
    print("- Complex expressions: (col1 + col2) * col3 / col4")
    print("\nExample usage:")
    print("python -m app.utils.data_tools --columns '收入 - 成本 as 利润,(收入 - 成本) / 收入 as 利润率'")

if __name__ == "__main__":
    main()
