#!/usr/bin/env python3
"""
Fix the incorrectly processed balance sheet file using enhanced algorithms
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reheader import MarkdownTableHeaderAdjuster
from app.utils.reform import MarkdownTableReformer
import json
import re

def show_before_after_comparison():
    """Show the before and after comparison"""
    print("="*80)
    print("BEFORE/AFTER COMPARISON")
    print("="*80)
    
    # Read the incorrectly processed file
    with open('test3\\-季度合并财报（2024年Q4）_资产负债表_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        incorrect_content = f.read()
    
    print("BEFORE (Incorrectly Processed):")
    print("-" * 40)
    print(incorrect_content[:500] + "..." if len(incorrect_content) > 500 else incorrect_content)
    
    # Read the original input file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    print("\nORIGINAL INPUT:")
    print("-" * 40)
    print(original_content[:500] + "..." if len(original_content) > 500 else original_content)
    
    return original_content, incorrect_content

def apply_enhanced_header_detection(content):
    """Apply enhanced header detection algorithm"""
    print("\n" + "="*80)
    print("STEP 1: ENHANCED HEADER DETECTION")
    print("="*80)
    
    adjuster = MarkdownTableHeaderAdjuster()
    adjuster.debug_output = True
    
    print("Applying enhanced header detection...")
    adjusted_content = adjuster.adjust_table(content)
    
    print("\nHeader-adjusted content:")
    print("-" * 40)
    print(adjusted_content[:800] + "..." if len(adjusted_content) > 800 else adjusted_content)
    
    return adjusted_content

def apply_enhanced_reform_algorithm(content, filename):
    """Apply enhanced reform algorithm with context-aware classification"""
    print("\n" + "="*80)
    print("STEP 2: ENHANCED REFORM WITH CONTEXT-AWARE CLASSIFICATION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    print("Applying enhanced reform algorithm...")
    reformed_content = reformer.process_table(content, filename)
    
    print("\nReformed content:")
    print("-" * 40)
    print(reformed_content)
    
    return reformed_content

def analyze_metadata_extraction(content):
    """Analyze the metadata extraction"""
    print("\n" + "="*80)
    print("METADATA ANALYSIS")
    print("="*80)
    
    # Extract metadata from the reformed content
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', content)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        
        print("Extracted metadata:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
        
        # Analyze metadata quality
        metadata_list = metadata.get('metadata', [])
        
        print(f"\nMetadata quality analysis:")
        print(f"  Total metadata items: {len(metadata_list)}")
        
        # Check for table structure elements that shouldn't be in metadata
        table_structure_items = []
        legitimate_metadata = []
        
        for item in metadata_list:
            # Check if item looks like table structure
            if any(keyword in item for keyword in ['科目', '流动资产:', '流动负债:', '非流动资产', '负债合计']):
                table_structure_items.append(item)
            else:
                legitimate_metadata.append(item)
        
        if table_structure_items:
            print(f"  ❌ Table structure items incorrectly in metadata: {table_structure_items}")
        else:
            print(f"  ✅ No table structure items found in metadata")
        
        if legitimate_metadata:
            print(f"  ✅ Legitimate metadata items: {legitimate_metadata}")
        else:
            print(f"  ⚠️  No legitimate metadata items found")
        
        # Check table name
        table_name = metadata.get('table_name', '')
        expected_elements = ['季度合并财报', '2024年Q4', '资产负债表']
        
        print(f"\nTable name analysis:")
        print(f"  Extracted: '{table_name}'")
        
        missing_elements = [elem for elem in expected_elements if elem not in table_name]
        if missing_elements:
            print(f"  ❌ Missing elements: {missing_elements}")
        else:
            print(f"  ✅ All expected elements present")
    
    else:
        print("❌ No metadata found in content")

def main():
    """Main function to fix the balance sheet file"""
    print("Fixing Balance Sheet File with Enhanced Algorithms")
    print("="*80)
    
    try:
        # Step 1: Show before/after comparison
        original_content, incorrect_content = show_before_after_comparison()
        
        # Step 2: Apply enhanced header detection
        header_adjusted_content = apply_enhanced_header_detection(original_content)
        
        # Step 3: Apply enhanced reform algorithm
        filename = "-季度合并财报（2024年Q4）_资产负债表.md"
        final_content = apply_enhanced_reform_algorithm(header_adjusted_content, filename)
        
        # Step 4: Analyze metadata extraction
        analyze_metadata_extraction(final_content)
        
        # Step 5: Save the corrected file
        output_file = 'test3\\-季度合并财报（2024年Q4）_资产负债表_FIXED.md'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"\n" + "="*80)
        print("FINAL RESULT")
        print("="*80)
        print(f"✅ Fixed file saved to: {output_file}")
        
        print("\nFinal corrected content:")
        print("-" * 40)
        print(final_content)
        
        return 0
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
