import argparse
import json
import logging
import glob
from pathlib import Path
import sys
from typing import List, Optional, Dict

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

# Import unified error handling
from app.exceptions import CLIException
from app.error_handling import log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for CLI operations
set_layer_context("cli")

from app.cli.engine import BaseCommand, CLIEngine
from app.utils.md_tools import MarkdownTools

class FromDocCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Process documents (Excel/PDF) into markdown chunks.')
        self.add_argument('input_files', nargs='+', 
                          help='Input document files/directories/patterns (supports .xls, .xlsx, .pdf)')
        self.add_argument('-o', '--output_dir', 
                          help='Output directory for markdown files (default: same as first input file)')

    def execute(self, args):
        tools = MarkdownTools()
        return tools.command_from_doc(args.input_files, args.output_dir)

class RagflowUploadCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Upload files to RAGFlow dataset.')
        self.add_argument('--files', nargs='*', 
                          help='List of files to upload')
        self.add_argument('--directory', 
                          help='Directory to scan for files (.xls, .xlsx, .md, .pdf)')
        self.add_argument('--company-md', 
                          help='Company markdown file to process and upload')

    def execute(self, args):
        tools = MarkdownTools()
        return tools.command_ragflow_upload(
            files=args.files,
            directory=args.directory,
            company_md=args.company_md
        )

class SplitXlsxCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Split Excel file into separate sheets with data block detection.')
        self.add_argument('input_file', help='Input Excel file')

    def execute(self, args):
        tools = MarkdownTools()
        return tools.command_split_xlsx(args.input_file)

class SplitCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Split markdown by size.')
        self.add_argument('input_file', help='Input markdown file')
        self.add_argument('-o', '--output_dir', default='.', 
                          help='Output directory (default: current dir)')
        self.add_argument('-s', '--size_limit', type=int, default=10000,
                          help='Max size per chunk in characters')

    def execute(self, args):
        tools = MarkdownTools()
        return tools.command_split(args.input_file, args.output_dir, args.size_limit)

class SplitTablesCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Split markdown by tables.')
        self.add_argument('input_file', help='Input markdown file')
        self.add_argument('-o', '--output_dir', default='.', 
                          help='Output directory (default: current dir)')
        self.add_argument('--format-dates', action='store_true',
                          help='Format dates to YYYY-MM-DD')
        self.add_argument('--format-numbers', action='store_true',
                          help='Format numbers by removing separators')

    def execute(self, args):
        tools = MarkdownTools()
        return tools.command_split_by_tables(args.input_file, args.output_dir, 
                                           args.format_dates, args.format_numbers)

class MergeCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Merge split markdown files.')
        self.add_argument('input_files', nargs='+', 
                          help='Input markdown files (wildcards supported)')
        self.add_argument('-o', '--output_file', 
                          help='Output file path (default: derived from input pattern)')

    def execute(self, args):
        tools = MarkdownTools()
        file_paths = []
        for pattern in args.input_files:
            file_paths.extend(glob.glob(pattern))
        file_paths.sort()
        return tools.command_merge(file_paths, args.output_file)

class ConvertHtmlCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Convert markdown to HTML.')
        self.add_argument('input_file', help='Input markdown file')

    def execute(self, args):
        tools = MarkdownTools()
        return tools.convert_to_html(args.input_file)

class ConvertPdfCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Convert markdown to PDF.')
        self.add_argument('input_file', help='Input markdown file')
        self.add_argument('--pdf-options', type=str, default=None,
                          help='PDF options as JSON string')

    def execute(self, args):
        tools = MarkdownTools()
        pdf_options = json.loads(args.pdf_options) if args.pdf_options else None
        return tools.command_convert_to_pdf(args.input_file, pdf_options)

class ConvertPdfDocxCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Convert PDF to docx.')
        self.add_argument('input_file', help='Input pdf file')

    def execute(self, args):
        tools = MarkdownTools()
        return tools.convert_pdf_to_docx(args.input_file)

class ConvertExcelCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Convert markdown to Excel.')
        self.add_argument('input_path', help='Input markdown file or directory')
        self.add_argument('-o', '--output', 
                          help='Output Excel file/directory path (default: same as input with .xlsx suffix)')
        self.add_argument('-d', '--delete-original', action='store_true',
                          help='Delete original markdown file after conversion')

    def execute(self, args):
        tools = MarkdownTools()
        return tools.command_convert_to_excel(args.input_path, args.output, args.delete_original)

class CleanQccCommand(BaseCommand):
    def __init__(self):
        super().__init__(description='Clean qichacha markdown format.')
        self.add_argument('input_file', help='Input markdown file')
        self.add_argument('-o', '--output', 
                          help='Output file path (default: overwrite input)')

    def execute(self, args):
        tools = MarkdownTools()
        return tools.command_clean_qcc(args.input_file, args.output)

@log_and_reraise(logger, "md tools CLI main")
def main():
    """Main function for md tools CLI with unified error handling"""
    set_operation_context("md_tools_cli_main")

    try:
        engine = CLIEngine(prog="md-tools")

        engine.register_command('from-doc', FromDocCommand)
        engine.register_command('ragflow-upload', RagflowUploadCommand)
        engine.register_command('split-xlsx', SplitXlsxCommand)
        engine.register_command('split', SplitCommand)
        engine.register_command('split-tables', SplitTablesCommand)
        engine.register_command('merge', MergeCommand)
        engine.register_command('convert-html', ConvertHtmlCommand)
        engine.register_command('convert-pdf', ConvertPdfCommand)
        engine.register_command('convert-pdf-docx', ConvertPdfDocxCommand)
        engine.register_command('convert-excel', ConvertExcelCommand)
        engine.register_command('clean-qcc', CleanQccCommand)

        engine.run()

    except CLIException as e:
        logger.error(f"CLI error: {e.message}")
        if e.details:
            logger.error(f"Details: {e.details}")
        if e.suggested_action:
            logger.info(f"Suggested action: {e.suggested_action}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()