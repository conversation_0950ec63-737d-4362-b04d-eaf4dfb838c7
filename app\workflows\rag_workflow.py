import os
import logging
from pathlib import Path
import time
from typing import List, Optional, Callable, TypedDict, Dict, Any
from datetime import datetime
from langgraph.graph import StateGraph, END
from app.agents.md_agent import MdAgent
from app.config import AgentConfig
from markitdown import MarkItDown
from app.utils.data_tools import process_template_content, replace_template_placeholders
from app.utils.mt_tools import group_files, group_merge
from ragflow_api.client import RAGFlowClient

from app.utils.md_tools import MarkdownTools
from app.utils.reform import MarkdownTableReformer
from app.utils.reheader import MarkdownTableHeaderAdjuster
from app.error_handling import (
    handle_layer_boundary,
    log_and_reraise,
    error_boundary,
    safe_execute
)
from app.exceptions import (
    LayerType,
    ValidationException,
    DataProcessingException,
    FileProcessingException,
    ConfigurationException,
    RAGWorkflowException,
    ExternalServiceException
)
from app.logging_base import set_operation_context

# 模块级日志配置
logger = logging.getLogger(__name__)

# RAG Workflow State Schema
class RAGWorkflowState(TypedDict):
    """State schema for RAG workflow following report workflow pattern"""
    # Template processing
    template_path: Optional[Path]

    # Document processing
    md_files: Optional[List[Path]]
    cleaned_files: Optional[List[Path]]
    merged_files: Optional[List[Path]]
    split_files: Optional[List[Path]]
    html_files: Optional[List[Path]]

    # Dataset and report
    dataset_info: Optional[Dict[str, Any]]
    report_info: Optional[Dict[str, Any]]

    # Progress tracking
    current_step: Optional[str]
    progress_callback: Optional[Callable[[str, int], None]]

    # Error handling
    last_error: Optional[str]
    retry_count: Optional[int]

class RAGWorkflow:
    @handle_layer_boundary(LayerType.UTILITY, "initialize_rag_workflow")
    def __init__(self, workspace_path: str, template_file: str):
        set_operation_context("rag_workflow_initialization")
        
        with error_boundary("initialize_rag_workflow", LayerType.UTILITY):
            self.workspace_path = Path(workspace_path)
            self.template_file = template_file
            self.ragflow_base_url = os.getenv('RAGFLOW_BASE_URL')
            self.ragflow_api_key = os.getenv('RAGFLOW_API_KEY')
            self.data_dir = self.workspace_path / "data"
            
            logger.debug(f"Initializing RAGWorkflow with workspace: {workspace_path}, template: {template_file}")
            
            if not self.workspace_path.exists():
                raise ValidationException(
                    message="Workspace directory does not exist",
                    details=f"Path: {workspace_path}",
                    suggested_action="Please ensure the workspace directory exists before proceeding"
                )
            
            self.data_dir.mkdir(exist_ok=True)
            logger.debug(f"创建/验证输出目录: {self.data_dir}")
            
            with error_boundary("markitdown_initialization", LayerType.UTILITY):
                self.doc = MarkItDown(enable_plugins=False)
                logger.debug("Initialized MarkItDown processor with plugins disabled")
            
            self._pdf_converter = None
            logger.debug("PDF converter will be initialized on first use")
            
            # Parse workspace path components
            parts = self.workspace_path.parts
            self.session_id = parts[-3] if len(parts) >= 3 else "default_session"
            self.company = parts[-2] if len(parts) >= 2 else "default_company"
            self.year = parts[-1] if len(parts) >= 1 else datetime.now().strftime("%Y")
            
            logger.debug("RAGWorkflow initialization complete")
        
    @log_and_reraise(logger, "convert_template_to_markdown")
    def _convert_template_to_md(self):
        """Convert template file to markdown"""
        # template_path = Path("templates") / self.template_file
        # logger.debug(f"Looking for template at: {template_path}")
        # if not template_path.exists():
        #     logger.error(f"Template file not found: {template_path}")
        #     raise FileNotFoundError(f"Template file not found: {template_path}")
            
        # output_path = self.data_dir / "report.md"
        # logger.debug(f"Converting template to markdown: {template_path}")
        # logger.debug(f"Output will be saved to: {output_path}")
        # result = self.doc.convert(str(template_path))
        # logger.debug(f"Template conversion returned {len(result.text_content)} characters")
        # fixed_content = re.sub(r'\\|NaN|Unnamed: \d+', '', result.text_content)
        # logger.debug(f"Cleaned content to {len(fixed_content)} characters after regex processing")
        
        # logger.debug(f"Markdown conversion complete. Saving to: {output_path}")
        # logger.debug(f"Final content length: {len(fixed_content)} characters")
        # with open(output_path, 'w', encoding='utf-8') as f:
        #     f.write(fixed_content)
        output_path = Path("templates") / self.template_file

        return output_path
        
    @log_and_reraise(logger, "convert_documents_to_markdown")
    def _convert_documents_to_md(self):
        """Convert all documents in workspace to markdown"""
        set_operation_context("convert_documents_to_markdown")
        
        with error_boundary("document_conversion_pipeline", LayerType.UTILITY):
            logger.debug(f"Starting document conversion in workspace: {self.workspace_path}")
            
            # Validate workspace exists
            if not self.workspace_path.exists():
                raise ValidationException(
                    message="Workspace directory does not exist",
                    details=f"Path: {self.workspace_path}",
                    suggested_action="Please ensure the workspace directory exists"
                )
            
            # Get all documents in workspace
            docs = list(self.workspace_path.glob("*"))
            if not docs:
                logger.debug("No documents found in workspace")
                return []
            
            # Validate data directory
            if not self.data_dir.exists():
                raise FileProcessingException(
                    message="Data directory does not exist",
                    details=f"Expected directory: {self.data_dir}",
                    suggested_action="Please ensure data directory is created before document conversion"
                )
            
            # Initialize MarkdownTools
            with error_boundary("markdowntools_initialization", LayerType.UTILITY):
                md_tools = MarkdownTools(size_limit=7500)
            
            try:
                # Process all documents using the new from-doc command
                with error_boundary("document_conversion", LayerType.UTILITY):
                    md_files = md_tools.command_from_doc(
                        input_files=[str(doc) for doc in docs],
                        output_dir=str(self.data_dir)
                    )
                    
                    if not md_files:
                        logger.warning("No markdown files were generated from documents")
                        return []
                
                logger.debug(f"Converted and split {len(md_files)} markdown documents/chunks")
                md_files = [Path(f).name for f in md_files]

                # Group and merge files
                with error_boundary("file_grouping_and_merging", LayerType.UTILITY):
                    md_files = group_merge(str(self.data_dir), '.', True, group_files(md_files))
                
                # Process table headers
                with error_boundary("table_header_adjustment", LayerType.UTILITY):
                    adjuster = MarkdownTableHeaderAdjuster()
                    adjuster.debug_output = False
                    adjuster.process_directory(str(self.data_dir), None, True)

                # Reformat tables
                with error_boundary("table_reformation", LayerType.UTILITY):
                    reformer = MarkdownTableReformer()
                    md_files = reformer.process_directory(str(self.data_dir), None, True)

                return [Path(f) for f in md_files]
                
            except OSError as e:
                raise ExternalServiceException(
                    message="PDF processing service unavailable",
                    details=f"Model loading failed: {str(e)}",
                    original_exception=e,
                    suggested_action="Please contact administrator or retry operation"
                )
            except Exception as e:
                raise DataProcessingException(
                    message="Document conversion failed",
                    details=f"Error: {str(e)}",
                    original_exception=e,
                    suggested_action="Please check document formats or contact administrator"
                )
        
    @log_and_reraise(logger, "clean_markdown_content")
    def _clean_md_content(self, md_file: Path, prompt: Optional[str] = None) -> Path:
        """Clean markdown content using LLM and save to cleaned file"""
        set_operation_context("clean_markdown_content")

        with error_boundary("markdown_content_cleaning", LayerType.UTILITY):
            # Validate input file
            if not md_file.exists():
                raise FileProcessingException(
                    message="Markdown file not found",
                    details=f"File path: {md_file}",
                    suggested_action="Please check the file path and ensure the file exists"
                )

            with error_boundary("markdown_file_reading", LayerType.UTILITY):
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()

            logger.debug(f"Starting LLM cleaning for: {md_file}")
            logger.debug(f"Original content length: {len(content)} characters")

            # Initialize agent for LLM processing
            try:
                config = AgentConfig(
                    workspace_id='market',
                    log_level="DEBUG"
                )
                logger.debug(f"Initializing MdAgent with config: {config}")

                clean_prompt = """你是数据工程师，负责清理markdown财务表格内容。

【核心原则 - 优先级最高】
1. 绝对保留：表格结构、表头、数据列、数据行完整性
2. 仅清理：表格内的水印和无关文本
3. 完全保留：所有markdown标题和表格外内容

【必须保留的内容】
1. 标题内容：
   - 所有markdown标题（#、##、###等开头）
   - 星号标题（*、**等开头）
   - 表名行（如"资产负债表"、"利润表"、"现金流量表"）

2. 表格结构：
   - 完整表格框架（|分隔符、表头分隔线）
   - 所有数据列（日期、科目、数量、单价、金额等）
   - 所有数据行（特别注意"行次"列的行数完整性）
   - 量纲信息（"单位：元/个/万/亿"等）

3. 有效数据：
   - 表格内的公司名称（作为数据，非声明）
   - 所有数值、文本数据
   - 列标题和行标题

【需要清理的内容】
1. 公司声明：
   - 格式："公司：XXX" → 替换为空字符串
   - 格式："单位：XXX公司" → 替换为空字符串
   - 注意：量纲"单位：元"等必须保留

2. 表格内水印：
   - 随机字符（仅、效、无、印、贷、复等）
   - HTML标签（<br>等）
   - 明显错误字符
   - 无意义文本片段

【操作步骤】
1. 识别并保护所有markdown标题和表名
2. 定位表格边界，确保结构完整
3. 清理公司声明（区分声明vs量纲）
4. 移除表格内水印，保留所有有效数据
5. 验证表格完整性（行数、列数、数据连续性）

【输出要求】
- 直接返回清理后的markdown内容
- 不添加代码块标记（```markdown```）
- 不添加解释文字
- 保持原始格式和可读性

待清理内容：
{content}
"""

                agent = MdAgent(config, clean_prompt)

                # Clean content with LLM using provided prompt or default
                result = agent.process_message({
                    "role": "system",
                    "content": content
                }, file_path=str(md_file))

                if result['status'] == 'error':
                    raise DataProcessingException(
                        message="Failed to clean markdown content with LLM",
                        details=f"File: {md_file}, Error: {result.get('message', 'Unknown error')}",
                        suggested_action="Please check the markdown content format and LLM configuration"
                    )

                logger.debug(f"LLM cleaning complete for: {md_file}")
                logger.debug(f"Cleaned content length: {len(result['message'])} characters")

                # Save cleaned content to new file
                cleaned_file = md_file.parent / f"{md_file.stem}_cleaned{md_file.suffix}"
                with open(cleaned_file, 'w', encoding='utf-8') as f:
                    f.write(result['message'])

                logger.debug(f"Saved cleaned content to: {cleaned_file}")
                return cleaned_file
                
            except Exception as e:
                if isinstance(e, (FileProcessingException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Unexpected error during markdown content cleaning",
                    details=f"File: {md_file}, Error: {str(e)}",
                    original_exception=e,
                    suggested_action="Please check system configuration and retry"
                )

#         clean = """你是数据工程师，请阅读和分析<orig>中的markdown内容，对于表格，学习<exp>中的处理方式：
#         1. 解读对表名，并用其替换表前标题的'SheetX'做为段落标题：
#           a. 若原表格头部若有明确的‘xxx表’名称，则直接提取为表名
#           b. 若原表格头部无明确的表名，则根据表头中带类似'1','2.1'等序号的表内标题做为表名，如'2.1 24+留存率'表示表名为'24+留存率表'
#           c. 若原表格头部无表名，则根据markdown段落标题和表格内容综合推断表名
#         2. 提取表格数据的截止日期，该日期存在于表头的一个或多个单元格中
#         3. 整理表格结构，模仿<exp>对<orig>表头的改写方式：
#           a. 识别数据行和其正确表头，最大限度保持表体内容不变
#           b. 对于头部的原表头，从中提取表格元数据，如日期，计量单位等，重写到表格外
#           c. 注意数据表格的表头可能存在多级，要展平表头，形成'一级-二级'这样的表头结构，最后的数据表格只有首行是标题行
        
#         任务：对<content>的markdown内容进行整理，输出易于阅读和理解的段落与表格到<result>中
#         要求：1. 输出是md格式，包括标题和内容，对于表格要求结构良好；2. 关注内容中的时间，提取“截止日期”；3. 严格输出<content>内容，信息要完整，必须遵循原内容，禁止截断表格、改写数据、编造数据等操作。
# <orig>
# ## Sheet1
# |  |  |  |  |  |  |
# | --- | --- | --- | --- | --- | --- |
# | 资产负债表 |  |  |  |  |  |
# |  | 2024 | 年 | 9 | 月 | 会企01表 |
# |  |
# | 资　　产 | 期末数 | 年初数 | 负债及所有者权益（或股东权益） | 期末数 | 年初数 |
# | 流动资产: |  |  | 流动负债: |  |  |
# | 货币资金 | 68880099.86 | 102893409.8 | 短期借款 | 35453125 | 35000000 |
# | 以公允价值计量且其变动计入当期损益的金融资产 | 126000000 | 50000000 | 以公允价值计量且其变动计入当期损益的金融负债 | 0 | 0 |
# | 衍生金融资产 |  |  | 衍生金融负债 | 0 | 0 |
# 略...
# <orig>

# <exp>
# ## 资产负债表
# 截止日期：2024年9月

# | 资　　产 | 期末数 | 年初数 | 负债及所有者权益（或股东权益） | 期末数 | 年初数 |
# | --- | --- | --- | --- | --- | --- |
# | 流动资产: |  |  | 流动负债: |  |  |
# | 货币资金 | 68880099.86 | 102893409.8 | 短期借款 | 35453125 | 35000000 |
# | 以公允价值计量且其变动计入当期损益的金融资产 | 126000000 | 50000000 | 以公允价值计量且其变动计入当期损益的金融负债 | 0 | 0 |
# | 衍生金融资产 |  |  | 衍生金融负债 | 0 | 0 |
# 略...
# <exp>

# <content>
# {content}
# <content>

# <result>
# """
#         agent = MdAgent(config, clean)
#         result = agent.process_message({
#             "role": "system",
#             "content": result['message'],
#             "force": True
#         }, file_path=str(md_file))
        
#         if result['status'] == 'error':
#             logger.error(f"Failed to clean {md_file}: {result['message']}")
            
#         logger.debug(f"LLM cleaning complete for: {md_file}")
#         logger.debug(f"Cleaned content: {result['message']}")

    @log_and_reraise(logger, "merge_markdown_files")
    def _merge_md_files(self) -> List[Path]:
        """Merge cleaned markdown files directly in data directory"""
        set_operation_context("merge_markdown_files")
        
        with error_boundary("markdown_file_merging", LayerType.UTILITY):
            logger.debug("Starting markdown file merging")
            
            # Validate data directory
            if not self.data_dir.exists():
                raise FileProcessingException(
                    message="Data directory does not exist",
                    details=f"Expected directory: {self.data_dir}",
                    suggested_action="Please ensure data directory exists before merging files"
                )
            
            # Get all subdirectories in data_dir
            subdirs = [d for d in self.data_dir.iterdir() if d.is_dir()]
            if not subdirs:
                logger.warning("No subdirectories found to merge")
                return []
                
            merged_files = []
            
            # Process each subdirectory
            for subdir in subdirs:
                with error_boundary(f"merge_subdirectory_{subdir.name}", LayerType.UTILITY):
                    md_files = list(subdir.glob("*.md"))
                    if not md_files:
                        logger.debug(f"No markdown files found in {subdir}")
                        continue
                        
                    # Merge files in this subdirectory
                    output_path = self.data_dir / f"{subdir.name}_merged.md"
                    logger.debug(f"Merging {len(md_files)} files from {subdir} to {output_path}")
                    
                    with error_boundary(f"merge_files_{subdir.name}", LayerType.UTILITY):
                        MarkdownTools().command_merge(
                            [str(f) for f in sorted(md_files)],
                            str(output_path)
                        )
                    
                    # Delete part files after successful merge
                    for md_file in md_files:
                        try:
                            os.remove(md_file)
                            logger.debug(f"Deleted part file: {md_file}")
                        except Exception as e:
                            logger.warning(
                                f"Failed to delete part file {md_file}: {str(e)}",
                                extra={"file": str(md_file), "error": str(e)}
                            )
                    
                    # Validate merged file was created
                    if not output_path.exists():
                        raise FileProcessingException(
                            message="Merged file was not created",
                            details=f"Expected file: {output_path}",
                            suggested_action="Please check merge operation and file permissions"
                        )
                    
                    merged_files.append(output_path)
                    logger.info(f"Merged {subdir.name} files to {output_path} and cleaned up part files")
            
            return merged_files

    @log_and_reraise(logger, "split_merged_tables")
    def _split_merged_tables(self, merged_files: List[Path]) -> List[Path]:
        """Split merged markdown files by tables"""
        set_operation_context("split_merged_tables")
        
        with error_boundary("table_splitting_operation", LayerType.UTILITY):
            logger.debug("Starting table splitting")
            
            # Validate input
            if not merged_files:
                logger.warning("No merged files provided for splitting")
                return []
            
            split_files = []
            
            with error_boundary("markdowntools_table_splitting_init", LayerType.UTILITY):
                tools = MarkdownTools()
            
            for merged_file in merged_files:
                with error_boundary(f"split_file_{merged_file.name}", LayerType.UTILITY):
                    if not merged_file.exists():
                        raise FileProcessingException(
                            message="Merged file not found",
                            details=f"File path: {merged_file}",
                            suggested_action="Please ensure the merged file exists before splitting"
                        )
                        
                    # Validate file is readable
                    with error_boundary("merged_file_readability_check", LayerType.UTILITY):
                        with open(merged_file, 'r', encoding='utf-8') as f:
                            f.read(1)  # Test file readability
                    
                    # Split tables directly in data directory
                    with error_boundary(f"split_tables_{merged_file.name}", LayerType.UTILITY):
                        output_files = tools.command_split_by_tables(
                            str(merged_file),
                            str(merged_file.parent),
                            format_dates=True,
                            format_numbers=True
                        )
                    
                    if not output_files:
                        logger.warning(f"No tables found in {merged_file}")
                        continue
                    
                    # Rename files to include merged file stem
                    renamed_files = []
                    for i, file_path in enumerate([Path(f) for f in output_files]):
                        with error_boundary(f"rename_file_{i}", LayerType.UTILITY):
                            new_name = merged_file.parent / f"{merged_file.stem}_table{i+1}.md"
                            file_path.rename(new_name)
                            renamed_files.append(new_name)
                            logger.debug(f"Renamed {file_path} to {new_name}")
                    
                    # Validate renamed files exist
                    for renamed_file in renamed_files:
                        if not renamed_file.exists():
                            raise FileProcessingException(
                                message="Renamed file does not exist",
                                details=f"Expected file: {renamed_file}",
                                suggested_action="Please check file system operations"
                            )
                    
                    split_files.extend(renamed_files)
                    logger.info(f"Split {merged_file} to {len(renamed_files)} tables")
            
            return split_files

    @log_and_reraise(logger, "convert_markdown_to_html")
    def _convert_md_to_html(self, md_files: List[Path]) -> List[Path]:
        """Convert markdown files to HTML format"""
        set_operation_context("convert_markdown_to_html")
        
        with error_boundary("markdown_to_html_conversion", LayerType.UTILITY):
            logger.debug("Starting markdown to HTML conversion")
            
            # Validate data directory
            if not self.data_dir.exists():
                raise FileProcessingException(
                    message="Data directory does not exist",
                    details=f"Expected directory: {self.data_dir}",
                    suggested_action="Please ensure data directory exists before conversion"
                )
            
            with error_boundary("markdowntools_html_conversion_init", LayerType.UTILITY):
                tools = MarkdownTools()
            
            # Combine input md_files with any .md files found directly in data_dir
            all_md_files = set(md_files) | set(self.data_dir.glob("*.md"))
            
            if not all_md_files:
                logger.warning("No markdown files found for conversion")
                return []
            
            html_files = []
            
            for md_file in all_md_files:
                # Ensure md_file is a Path object
                md_path = Path(md_file) if isinstance(md_file, str) else md_file

                with error_boundary(f"convert_file_{md_path.name}", LayerType.UTILITY):
                    if not md_path or not md_path.exists():
                        logger.warning(f"Markdown file not found: {md_path}")
                        continue

                    # Validate file is readable
                    try:
                        with open(md_path, 'r', encoding='utf-8') as f:
                            f.read(1)  # Test file readability
                    except Exception as e:
                        logger.warning(f"Cannot read markdown file {md_path}: {str(e)}")
                        continue

                    # Convert to HTML using md_tools
                    with error_boundary(f"html_conversion_{md_path.name}", LayerType.UTILITY):
                        html_file = tools.convert_to_excel(str(md_path))
                        if not html_file:
                            raise DataProcessingException(
                                message="HTML conversion returned empty result",
                                details=f"Source file: {md_path}",
                                suggested_action="Please check file content and format"
                            )

                        html_path = Path(html_file)
                        if not html_path.exists():
                            raise FileProcessingException(
                                message="HTML file was not created",
                                details=f"Expected file: {html_path}",
                                suggested_action="Please check conversion process and permissions"
                            )

                        html_files.append(html_path)
                        logger.info(f"Converted {md_path} to HTML: {html_file}")

                        # Clean up split markdown file after conversion
                        try:
                            os.remove(str(md_path))
                            logger.debug(f"Deleted split markdown file: {md_path}")
                        except Exception as e:
                            logger.warning(
                                f"Failed to delete split markdown file {md_path}: {str(e)}",
                                extra={"file": str(md_path), "error": str(e)}
                            )
            
            # Clean up any remaining subdirectories in data/
            for item in self.data_dir.iterdir():
                if item.is_dir():
                    with error_boundary(f"cleanup_directory_{item.name}", LayerType.UTILITY):
                        try:
                            # Remove all files in subdirectory first
                            for file in item.glob('*'):
                                try:
                                    file.unlink()
                                    logger.debug(f"Deleted file: {file}")
                                except Exception as e:
                                    logger.warning(
                                        f"Failed to delete file {file}: {str(e)}",
                                        extra={"file": str(file), "error": str(e)}
                                    )
                            # Then remove the empty directory
                            item.rmdir()
                            logger.info(f"Removed subdirectory: {item}")
                        except Exception as e:
                            logger.warning(
                                f"Failed to remove subdirectory {item}: {str(e)}",
                                extra={"directory": str(item), "error": str(e)}
                            )
            
            if not html_files:
                logger.warning("No HTML files were generated from markdown conversion")
            
            return html_files
    
    @log_and_reraise(logger, "extract_questions")
    def extract_questions(self, template_path: str) -> list:
        """Extract questions from {} brackets in template.md"""
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        questions = []
        start = 0
        while True:
            start = content.find('{', start)
            if start == -1:
                break
            end = content.find('}', start)
            if end == -1:
                break
            questions.append(content[start+1:end].strip())
            start = end + 1
        return questions

    @log_and_reraise(logger, "process_template")
    def process_template(self, template_path: str, answers: dict) -> str:
        """Replace {} sections in template with answers"""
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        result = []
        start = 0
        answer_idx = 0
        while True:
            brace_start = content.find('{', start)
            if brace_start == -1:
                result.append(content[start:])
                break
            result.append(content[start:brace_start])
            
            brace_end = content.find('}', brace_start)
            if brace_end == -1:
                result.append(content[brace_start:])
                break
                
            if answer_idx < len(answers):
                result.append(answers[str(answer_idx)])
            answer_idx += 1
            start = brace_end + 1
        
        return ''.join(result)

    @log_and_reraise(logger, "generate_final_report")
    def _generate_report(self, data: dict) -> dict:
        """Generate final report by copying template to workspace and include dataset info"""
        set_operation_context("generate_final_report")
        
        with error_boundary("report_generation", LayerType.UTILITY):
            # Handle case where template_file already includes "templates/" prefix
            if self.template_file.startswith("templates/") or self.template_file.startswith("templates\\"):
                template_path = Path(self.template_file)
            else:
                template_path = Path("templates") / self.template_file
            
            if not template_path.exists():
                raise FileProcessingException(
                    message="Template file not found",
                    details=f"Template path: {template_path}",
                    suggested_action="Please ensure the template file exists in the templates directory"
                )
                
            # Use pre-parsed company name
            report_path = self.workspace_path / f"{self.company}_report.md"
            
            # Read template content
            with error_boundary("template_file_reading", LayerType.UTILITY):
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
            
            # Execute template interpretation to get results
            with error_boundary("template_content_processing", LayerType.UTILITY):
                results = process_template_content(
                    content=template_content,
                    ragflow_base_url=self.ragflow_base_url,
                    ragflow_api_key=self.ragflow_api_key,
                    assistant_id=data['assistant_id']
                )

            # Create a context dictionary to store bind values
            context = {'root': 'PLACEHOLDER'}
            
            # Replace placeholders with results
            with error_boundary("template_placeholder_replacement", LayerType.UTILITY):
                processed_content = replace_template_placeholders(
                    context,
                    r'{([^({})]*)}',
                    template_content,
                    results
                )
                processed_content = replace_template_placeholders(
                    context,
                    r'⁅([^(⁅⁆)]*)⁆',
                    processed_content,
                    []
                )
            
            # Save final report
            with error_boundary("final_report_saving", LayerType.UTILITY):
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write(processed_content)
            
            logger.info("Report generation completed successfully")
            logger.info(f"Generated report at: {report_path}")
            
            # Include dataset info in the result
            return {
                'report_path': report_path,
                'dataset_id': data.get('add_to_dataset'),
                'document_ids': data.get('document_ids', []),
                'assistant_id': data.get('assistant_id')
            }

    @log_and_reraise(logger, "cleanup_chat_sessions")
    @log_and_reraise(logger, "cleanup_chat_sessions")
    def _cleanup_chat_sessions(self, client: RAGFlowClient, chat_id: str) -> bool:
        """Clean up all existing chat sessions for an assistant"""
        set_operation_context("cleanup_chat_sessions")
        
        with error_boundary("chat_session_cleanup", LayerType.UTILITY):
            with error_boundary("chat_sessions_cleanup", LayerType.UTILITY):
                sessions = client.list_chat_sessions(chat_id)
                if sessions and isinstance(sessions, list):
                    session_ids = [s['id'] for s in sessions]
                    if session_ids:
                        logger.debug(f"Cleaning up {len(session_ids)} chat sessions")
                        client.delete_chat_sessions(chat_id, session_ids)
                        logger.debug("Chat sessions cleanup completed")
                        return True
                else:
                    logger.debug(f"No chat sessions found: {type(sessions)}, {sessions}")
        return False

    @log_and_reraise(logger, "add_to_ragflow_dataset")
    @log_and_reraise(logger, "add_to_ragflow_dataset")
    def _add_to_ragflow_dataset(self, html_files: List[Path]) -> dict:
        """Add processed files to RAGFlow dataset using md_tools command"""
        set_operation_context("add_to_ragflow_dataset")
        
        with error_boundary("ragflow_dataset_upload", LayerType.UTILITY):
            logger.debug("Starting RAGFlow dataset upload via md_tools")
            
            # Validate input files
            if not html_files:
                raise ValidationException(
                    message="No HTML files provided for dataset upload",
                    details="Empty or None html_files list provided",
                    suggested_action="Please ensure HTML files are generated before dataset upload"
                )
            
            # Set environment variables for md_tools
            if self.ragflow_base_url:
                os.environ['RAGFLOW_BASE_URL'] = self.ragflow_base_url
            if self.ragflow_api_key:
                os.environ['RAGFLOW_API_KEY'] = self.ragflow_api_key
                
            # Use pre-parsed workspace path components
            session_id = self.session_id
            company = self.company
            year = self.year
            workspace_path = os.path.join("workspaces", self.session_id)
            regist_info = Path(workspace_path) / f"{company}.md"
            logger.debug(f"Using workspace path: {self.workspace_path}")
            logger.debug(f"Finding regist_info: {regist_info}")
            
            # Initialize MarkdownTools
            with error_boundary("markdowntools_dataset_upload_init", LayerType.UTILITY):
                md_tools = MarkdownTools()
            
            # Validate HTML files exist
            valid_files = [str(f) for f in html_files if f and os.path.exists(f)]
            if not valid_files:
                raise FileProcessingException(
                    message="No valid HTML files found for upload",
                    details=f"Provided files: {[str(f) for f in html_files]}",
                    suggested_action="Please check file paths and ensure files exist"
                )
            
            # Call the new command: pass HTML files and company markdown
            with error_boundary("ragflow_dataset_upload", LayerType.UTILITY):
                result = md_tools.command_ragflow_upload(
                    files=valid_files,
                    company_md=str(regist_info) if regist_info.exists() else None
                )
            
            dataset_id = result.get('dataset_id')
            document_ids = result.get('document_ids', [])
            
            if not dataset_id:
                raise ExternalServiceException(
                    message="RAGFlow dataset upload returned no dataset ID",
                    details=f"Upload result: {result}",
                    suggested_action="Please check RAGFlow service logs and configuration"
                )
            
            # Update chat assistant with new dataset
            assistant_id = os.getenv('RAGFLOW_ASSISTANT_ID')
            if not assistant_id:
                raise ConfigurationException(
                    message="RAGFLOW_ASSISTANT_ID not set in environment",
                    details="Required environment variable RAGFLOW_ASSISTANT_ID is missing",
                    suggested_action="Please set RAGFLOW_ASSISTANT_ID environment variable"
                )
            else:
                with error_boundary("ragflow_assistant_update", LayerType.UTILITY):
                    client = RAGFlowClient(
                        base_url=self.ragflow_base_url,
                        api_key=self.ragflow_api_key
                    )
                    # Clean up existing sessions using helper method
                    self._cleanup_chat_sessions(client, assistant_id)
                    time.sleep(30)
                    # Then update the assistant
                    logger.debug(f"Updating chat assistant {assistant_id} with dataset {dataset_id}")
                    client.update_chat_assistant(
                        chat_id=assistant_id,
                        dataset_ids=[dataset_id]
                    )

            return {
                'add_to_dataset': dataset_id,
                'document_ids': document_ids,
                'parsed': bool(document_ids),
                'assistant_id': assistant_id
            }

    # State-aware workflow node methods following report workflow pattern
    @log_and_reraise(logger, "convert_template_node")
    def convert_template_node(self, state: RAGWorkflowState) -> RAGWorkflowState:
        """Convert template to markdown - state-aware wrapper"""
        set_operation_context("convert_template_node")

        try:
            if state.get('progress_callback'):
                state['progress_callback']("Converting template to markdown...", 10)

            template_path = self._convert_template_to_md()
            state['template_path'] = template_path
            state['current_step'] = 'template_converted'

            logger.info(f"Template converted: {template_path}")
            return state

        except Exception as e:
            state['last_error'] = f"Template conversion failed: {str(e)}"
            raise

    @log_and_reraise(logger, "convert_documents_node")
    def convert_documents_node(self, state: RAGWorkflowState) -> RAGWorkflowState:
        """Convert documents to markdown - state-aware wrapper"""
        set_operation_context("convert_documents_node")

        try:
            if state.get('progress_callback'):
                state['progress_callback']("Converting documents to markdown...", 25)

            md_files = self._convert_documents_to_md()
            state['md_files'] = md_files
            state['current_step'] = 'documents_converted'

            logger.info(f"Documents converted: {len(md_files)} files")
            return state

        except Exception as e:
            state['last_error'] = f"Document conversion failed: {str(e)}"
            raise

    @log_and_reraise(logger, "clean_documents_node")
    def clean_documents_node(self, state: RAGWorkflowState) -> RAGWorkflowState:
        """Clean markdown documents - state-aware wrapper"""
        set_operation_context("clean_documents_node")

        try:
            if state.get('progress_callback'):
                state['progress_callback']("Cleaning markdown documents...", 40)

            md_files = state.get('md_files', [])
            if not md_files:
                logger.warning("No markdown files to clean")
                state['cleaned_files'] = []
                return state

            cleaned_files = []
            for md_file in md_files:
                cleaned_file = self._clean_md_content(md_file)
                cleaned_files.append(cleaned_file)

            state['cleaned_files'] = cleaned_files
            state['current_step'] = 'documents_cleaned'

            logger.info(f"Documents cleaned: {len(cleaned_files)} files")
            return state

        except Exception as e:
            state['last_error'] = f"Document cleaning failed: {str(e)}"
            raise

    @log_and_reraise(logger, "merge_documents_node")
    def merge_documents_node(self, state: RAGWorkflowState) -> RAGWorkflowState:
        """Merge cleaned markdown files - state-aware wrapper"""
        set_operation_context("merge_documents_node")

        try:
            if state.get('progress_callback'):
                state['progress_callback']("Merging markdown files...", 55)

            merged_files = self._merge_md_files()
            state['merged_files'] = merged_files
            state['current_step'] = 'documents_merged'

            logger.info(f"Documents merged: {len(merged_files)} files")
            return state

        except Exception as e:
            state['last_error'] = f"Document merging failed: {str(e)}"
            raise

    @log_and_reraise(logger, "split_tables_node")
    def split_tables_node(self, state: RAGWorkflowState) -> RAGWorkflowState:
        """Split merged files by tables - state-aware wrapper"""
        set_operation_context("split_tables_node")

        try:
            if state.get('progress_callback'):
                state['progress_callback']("Splitting tables...", 70)

            merged_files = state.get('merged_files', [])
            if not merged_files:
                logger.warning("No merged files to split")
                state['split_files'] = []
                return state

            split_files = self._split_merged_tables(merged_files)
            state['split_files'] = split_files
            state['current_step'] = 'tables_split'

            logger.info(f"Tables split: {len(split_files)} files")
            return state

        except Exception as e:
            state['last_error'] = f"Table splitting failed: {str(e)}"
            raise

    @log_and_reraise(logger, "convert_html_node")
    def convert_html_node(self, state: RAGWorkflowState) -> RAGWorkflowState:
        """Convert markdown to HTML - state-aware wrapper"""
        set_operation_context("convert_html_node")

        try:
            if state.get('progress_callback'):
                state['progress_callback']("Converting to HTML...", 80)

            # Use split files if available, otherwise use cleaned files
            md_files = state.get('split_files') or state.get('cleaned_files', [])
            if not md_files:
                logger.warning("No markdown files to convert to HTML")
                state['html_files'] = []
                return state

            html_files = self._convert_md_to_html(md_files)
            state['html_files'] = html_files
            state['current_step'] = 'html_converted'

            logger.info(f"HTML converted: {len(html_files)} files")
            return state

        except Exception as e:
            state['last_error'] = f"HTML conversion failed: {str(e)}"
            raise

    @log_and_reraise(logger, "add_to_dataset_node")
    def add_to_dataset_node(self, state: RAGWorkflowState) -> RAGWorkflowState:
        """Add files to RAGFlow dataset - state-aware wrapper"""
        set_operation_context("add_to_dataset_node")

        try:
            if state.get('progress_callback'):
                state['progress_callback']("Adding to dataset...", 90)

            html_files = state.get('html_files', [])
            if not html_files:
                logger.warning("No HTML files to add to dataset")
                state['dataset_info'] = {}
                return state

            dataset_info = self._add_to_ragflow_dataset(html_files)
            state['dataset_info'] = dataset_info
            state['current_step'] = 'dataset_updated'

            logger.info(f"Dataset updated: {dataset_info.get('dataset_id')}")
            return state

        except Exception as e:
            state['last_error'] = f"Dataset update failed: {str(e)}"
            raise

    @log_and_reraise(logger, "generate_report_node")
    def generate_report_node(self, state: RAGWorkflowState) -> RAGWorkflowState:
        """Generate final report - state-aware wrapper"""
        set_operation_context("generate_report_node")

        try:
            if state.get('progress_callback'):
                state['progress_callback']("Generating report...", 95)

            dataset_info = state.get('dataset_info', {})
            report_info = self._generate_report(dataset_info)
            state['report_info'] = report_info
            state['current_step'] = 'report_generated'

            if state.get('progress_callback'):
                state['progress_callback']("Report generation complete!", 100)

            logger.info(f"Report generated: {report_info.get('report_path')}")
            return state

        except Exception as e:
            state['last_error'] = f"Report generation failed: {str(e)}"
            raise

    @handle_layer_boundary(LayerType.UTILITY, "build_workflow_graph")
    @log_and_reraise(logger, "build_workflow_graph")
    def build_graph(self) -> StateGraph:
        """Build langgraph workflow"""
        set_operation_context("build_workflow_graph")
        
        with error_boundary("workflow_graph_construction", LayerType.UTILITY):
            logger.debug("Building langgraph workflow with StateGraph")

            # Create StateGraph with RAGWorkflowState schema
            workflow = StateGraph(RAGWorkflowState)
            logger.debug("Created new StateGraph instance with RAGWorkflowState schema")

            # Define state-aware nodes following report workflow pattern
            workflow.add_node("convert_template", self.convert_template_node)
            workflow.add_node("convert_docs", self.convert_documents_node)
            workflow.add_node("clean_docs", self.clean_documents_node)
            workflow.add_node("merge_docs", self.merge_documents_node)
            workflow.add_node("split_tables", self.split_tables_node)
            workflow.add_node("convert_html", self.convert_html_node)
            workflow.add_node("add_to_dataset", self.add_to_dataset_node)
            workflow.add_node("generate_report", self.generate_report_node)

            logger.debug("Added all workflow nodes with state-aware methods")

            # Define complete processing pipeline with all nodes connected
            workflow.add_edge("convert_template", "convert_docs")
            workflow.add_edge("convert_docs", "clean_docs")
            workflow.add_edge("clean_docs", "merge_docs")  # Connect previously orphaned merge_docs
            workflow.add_edge("merge_docs", "split_tables")  # Connect previously orphaned split_tables
            workflow.add_edge("split_tables", "convert_html")
            workflow.add_edge("convert_html", "add_to_dataset")
            workflow.add_edge("add_to_dataset", "generate_report")
            workflow.add_edge("generate_report", END)

            logger.debug("Connected all nodes in complete processing pipeline")

            # Set entry point
            workflow.set_entry_point("convert_template")

            logger.debug("Workflow graph construction completed successfully")
            return workflow
        
    @handle_layer_boundary(LayerType.UTILITY, "execute_rag_workflow")
    def execute(self, progress_callback: Optional[Callable[[str, int], None]] = None):
        """Execute the full RAG workflow with state management"""
        set_operation_context("execute_rag_workflow")

        with error_boundary("rag_workflow_execution", LayerType.UTILITY):
            logger.info("Starting RAG workflow execution with state management")
            logger.debug(f"Workspace path: {self.workspace_path}")
            logger.debug(f"Template file: {self.template_file}")

            # Initialize workflow state
            initial_state: RAGWorkflowState = {
                'template_path': None,
                'md_files': None,
                'cleaned_files': None,
                'merged_files': None,
                'split_files': None,
                'html_files': None,
                'dataset_info': None,
                'report_info': None,
                'current_step': 'initializing',
                'progress_callback': progress_callback,
                'last_error': None,
                'retry_count': 0
            }

            logger.debug("Initialized workflow state")

            # Build and compile workflow graph
            with error_boundary("workflow_graph_compilation", LayerType.UTILITY):
                graph = self.build_graph()
                logger.debug("Compiling StateGraph workflow")
                app = graph.compile()

            # Execute workflow with initial state
            with error_boundary("workflow_execution", LayerType.UTILITY):
                logger.debug("Invoking workflow execution with state")
                final_state = app.invoke(initial_state)
                logger.debug(f"Workflow execution completed: {final_state.get('current_step')}")

            # Extract results from final state
            report_info = final_state.get('report_info', {})
            dataset_info = final_state.get('dataset_info', {})
            report_path = report_info.get('report_path', self.workspace_path / f"{self.company}_report.md")
            dataset_id = dataset_info.get('dataset_id', None)
            
            if not report_path:
                raise ValidationException(
                    message="No report path returned from workflow",
                    details=f"Final state: {final_state.get('current_step')}, Report info: {report_info}",
                    suggested_action="Please check workflow configuration and template processing"
                )
            
            # Wait for report generation with timeout
            try:
                report_generated = False
                for _ in range(5):
                    if report_path.exists():
                        logger.info(f"RAG workflow completed. Report available at: {report_path}")
                        report_generated = True
                        break
                    time.sleep(5)
                
                if not report_generated:
                    raise FileProcessingException(
                        message="Report file was not generated within expected timeframe",
                        details=f"Expected report path: {report_path}",
                        suggested_action="Please check workflow execution logs and file permissions"
                    )
            except Exception as e:
                raise FileProcessingException(
                    message="Error waiting for report generation",
                    details=f"Error: {str(e)}",
                    original_exception=e,
                    suggested_action="Please check file system permissions and disk space"
                )

            logger.info(f"RAG workflow completed. Converting report to PDF and DOCX")
            
            # Convert report to PDF and DOCX
            with error_boundary("report_format_conversion", LayerType.UTILITY):
                md_tools = MarkdownTools()
                pdf_path = md_tools.convert_to_pdf(str(report_path))
                docx_path = md_tools.convert_pdf_to_docx(pdf_path)
                
            return {
                'status': 'success',
                'report_path': str(report_path),
                'pdf_path': str(pdf_path),
                'docx_path': str(docx_path),
                'dataset_id': dataset_id
            }
