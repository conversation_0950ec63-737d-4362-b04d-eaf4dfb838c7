#!/usr/bin/env python3
"""
Test header date priority with the profit statement file
"""

import sys
import os
from pathlib import Path
import json
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def test_profit_statement_date_extraction():
    """Test date extraction with the profit statement file"""
    print("="*80)
    print("TESTING PROFIT STATEMENT DATE EXTRACTION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Read the profit statement file
    with open('test3\\-季度合并财报（2024年Q4）_利润表_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_利润表_adjusted.md"
    
    print(f"Processing file: {filename}")
    print()
    
    # Parse the table to examine header structure
    table_data, separator_line, markdown_headers = reformer.parse_markdown_table(content)
    
    print(f"Separator line: {separator_line}")
    print(f"Total rows: {len(table_data)}")
    print()
    
    # Show header rows
    header_rows = table_data[:separator_line] if separator_line > 0 else []
    print("Header rows:")
    for i, row in enumerate(header_rows):
        print(f"  Row {i+1}: {row}")
    
    print()
    
    # Test date extraction from header lines
    print("Testing header line date extraction:")
    header_date = reformer.extract_date_from_header_lines(header_rows)
    print(f"Date from header lines: '{header_date}'")
    
    # Test date extraction from filename
    print("Testing filename date extraction:")
    filename_date = reformer.extract_date_from_filename(filename)
    print(f"Date from filename: '{filename_date}'")
    
    # Test comprehensive date extraction
    print("Testing comprehensive date extraction:")
    comprehensive_date = reformer.comprehensive_date_extraction(header_rows, filename)
    print(f"Comprehensive date: '{comprehensive_date}'")
    
    print()
    
    # Expected behavior: header date should have priority
    expected_date = "2024-12-31"  # From header row: ['科目', '2024-12-31', '2024年Q4', '2024年']
    
    print(f"Expected date (from header): '{expected_date}'")
    print(f"Actual comprehensive date: '{comprehensive_date}'")
    
    if comprehensive_date == expected_date:
        print("✅ Header date priority working correctly")
        priority_test_passed = True
    else:
        print("❌ Header date priority not working - filename date being used instead")
        priority_test_passed = False
    
    print()
    
    # Test full table processing
    print("Testing full table processing:")
    result = reformer.process_table(content, filename)
    
    # Extract metadata
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        metadata_date = metadata.get('date', None)
        
        print(f"Metadata: {metadata}")
        print(f"Metadata date: '{metadata_date}'")
        
        # Check if the correct date is in metadata
        if metadata_date == expected_date:
            print("✅ Correct header date stored in metadata")
            metadata_test_passed = True
        else:
            print(f"❌ Wrong date in metadata - expected '{expected_date}', got '{metadata_date}'")
            metadata_test_passed = False
    else:
        print("❌ No metadata found")
        metadata_test_passed = False
    
    return priority_test_passed and metadata_test_passed

def test_format_preservation():
    """Test that date formats are preserved exactly"""
    print("\n" + "="*80)
    print("TESTING DATE FORMAT PRESERVATION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # (header_rows, expected_date, description)
        (
            [['科目', '2024-09-30', '备注']],
            "2024-09-30",
            "International date format should be preserved"
        ),
        (
            [['项目', '2024年9月30日', '']],
            "2024年9月30日",
            "Chinese full date format should be preserved"
        ),
        (
            [['标题', '2024年Q4', '数据']],
            "2024年Q4",
            "Chinese quarter format should be preserved"
        ),
        (
            [['报表', '2024.09.30', '']],
            "2024.09.30",
            "Dot-separated date format should be preserved"
        ),
        (
            [['财务', '2024/09/30', '']],
            "2024/09/30",
            "Slash-separated date format should be preserved"
        ),
    ]
    
    print("Format Preservation Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (header_rows, expected_date, description) in enumerate(test_cases):
        extracted_date = reformer.extract_date_from_header_lines(header_rows)
        
        print(f"Test {i+1}: {description}")
        print(f"  Header: {header_rows[0]}")
        print(f"  Expected: '{expected_date}'")
        print(f"  Extracted: '{extracted_date}'")
        
        passed = extracted_date == expected_date
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_validation_requirements():
    """Test that validation requirements are met"""
    print("="*80)
    print("TESTING VALIDATION REQUIREMENTS")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # (date_string, should_be_valid, description)
        ("2024-09-30", True, "Valid date within range"),
        ("2024年9月30日", True, "Valid Chinese date"),
        ("2019-09-30", False, "Date before 2020 (too early)"),
        ("2031-09-30", False, "Date after 2030 (too late)"),
        ("2024-13-01", False, "Invalid month"),
        ("2024-09-32", False, "Invalid day"),
        ("2024-02-30", False, "Invalid February date"),
        ("2024年Q4", True, "Valid quarter"),
        ("2024年Q5", False, "Invalid quarter"),
    ]
    
    print("Validation Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (date_string, should_be_valid, description) in enumerate(test_cases):
        is_valid = reformer.validate_date_format(date_string)
        
        print(f"Test {i+1}: {description}")
        print(f"  Date: '{date_string}'")
        print(f"  Expected valid: {should_be_valid}")
        print(f"  Actually valid: {is_valid}")
        
        passed = is_valid == should_be_valid
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def main():
    """Run all header date priority tests"""
    print("Testing Header Date Priority and Format Preservation")
    print("="*80)
    
    try:
        test1 = test_profit_statement_date_extraction()
        test2 = test_format_preservation()
        test3 = test_validation_requirements()
        
        print("="*80)
        print("FINAL RESULTS")
        print("="*80)
        print(f"Profit Statement Date Extraction: {'✅ PASS' if test1 else '❌ FAIL'}")
        print(f"Format Preservation: {'✅ PASS' if test2 else '❌ FAIL'}")
        print(f"Validation Requirements: {'✅ PASS' if test3 else '❌ FAIL'}")
        
        all_passed = test1 and test2 and test3
        
        if all_passed:
            print("\n🎉 All header date priority tests passed!")
            print("✅ Header line dates have absolute priority")
            print("✅ Original formats are preserved exactly")
            print("✅ Dates are stored with 'date' key in metadata")
            print("✅ Validation ensures reasonable date ranges")
            return 0
        else:
            print("\n❌ Some header date priority tests failed")
            return 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
