#!/usr/bin/env python3
"""
Test current processing issues with the financial statement file
"""

import sys
import os
from pathlib import Path
import json
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer
from app.utils.reheader import MarkdownTableHeaderAdjuster

def analyze_current_issues():
    """Analyze the current processing issues"""
    print("="*80)
    print("ANALYZING CURRENT PROCESSING ISSUES")
    print("="*80)
    
    # Read the file
    filename = "1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted.md"
    with open('test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Processing file: {filename}")
    print()
    
    # Test with reform.py
    print("="*60)
    print("TESTING WITH REFORM.PY")
    print("="*60)
    
    reformer = MarkdownTableReformer()
    
    # Parse the table structure
    table_data, separator_line, markdown_headers = reformer.parse_markdown_table(content)
    
    print(f"Separator line: {separator_line}")
    print(f"Total rows: {len(table_data)}")
    print(f"Markdown headers: {markdown_headers}")
    print()
    
    print("Table structure:")
    for i, row in enumerate(table_data[:8]):
        print(f"  Row {i+1}: {row}")
    
    print()
    
    # Test date extraction
    header_rows = table_data[:separator_line] if separator_line > 0 else []
    print(f"Header rows: {header_rows}")
    
    # Test date extraction from header lines only
    date_from_headers = reformer.extract_date_from_header_lines(header_rows)
    print(f"Date from header lines only: '{date_from_headers}'")
    
    # Test date extraction from filename
    date_from_filename = reformer.extract_date_from_filename(filename)
    print(f"Date from filename: '{date_from_filename}'")
    
    # Test comprehensive date extraction
    comprehensive_date = reformer.comprehensive_date_extraction(header_rows, filename, table_data)
    print(f"Comprehensive date: '{comprehensive_date}'")
    
    print()
    
    # Test with reheader.py
    print("="*60)
    print("TESTING WITH REHEADER.PY")
    print("="*60)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Parse table
    rows = adjuster.parse_table(content)
    print(f"Total rows parsed: {len(rows)}")
    
    print("\nFirst 8 rows:")
    for i, row in enumerate(rows[:8]):
        if adjuster.is_separator_row(row):
            print(f"  Row {i+1}: SEPARATOR - {row}")
        else:
            print(f"  Row {i+1}: {row}")
    
    print()
    
    # Test header detection for "项 目" row
    xiangmu_row = ['项 目', '本年累计金额', '上年金额']
    print(f"Testing '项 目' row: {xiangmu_row}")
    
    has_keywords = adjuster.contains_header_keywords(xiangmu_row)
    keyword_count = adjuster.count_header_keywords(xiangmu_row)
    has_strong = adjuster.has_strong_header_keywords(xiangmu_row)
    row_density = adjuster.calculate_row_density(xiangmu_row)
    string_density = adjuster.calculate_string_density(xiangmu_row)
    
    print(f"  Has header keywords: {has_keywords}")
    print(f"  Keyword count: {keyword_count}")
    print(f"  Has strong keywords: {has_strong}")
    print(f"  Row density: {row_density:.2f}")
    print(f"  String density: {string_density:.2f}")
    
    # Calculate score
    score = adjuster.calculate_comprehensive_header_score(xiangmu_row, 2, len(rows), rows)
    print(f"  Comprehensive score: {score:.2f}")
    
    print()
    
    # Find header candidates
    candidates = adjuster.find_all_header_candidates(rows)
    print("Top 5 header candidates:")
    for i, (row_idx, candidate_score) in enumerate(candidates[:5]):
        row = rows[row_idx]
        print(f"  {i+1}. Row {row_idx+1}: Score {candidate_score:.2f} - {row}")
        if '项' in row or '项目' in ' '.join(row):
            print(f"      ✅ This contains '项目' keyword")
    
    print()
    
    # Test header adjustment
    print("Testing header adjustment...")
    try:
        result = adjuster.adjust_table(content)
        print("✅ Header adjustment successful")
        
        # Show first 10 lines of result
        result_lines = result.split('\n')
        print("\nFirst 10 lines of adjusted result:")
        for i, line in enumerate(result_lines[:10]):
            print(f"  {i+1}: {line}")
            
    except Exception as e:
        print(f"❌ Header adjustment failed: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # Test full reform processing
    print("Testing full reform processing...")
    try:
        result = reformer.process_table(content, filename)
        print("✅ Reform processing successful")
        
        # Extract metadata
        metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
        if metadata_match:
            metadata = json.loads(metadata_match.group(1))
            print("\nExtracted metadata:")
            for key, value in metadata.items():
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"❌ Reform processing failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main analysis function"""
    try:
        analyze_current_issues()
        return 0
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
