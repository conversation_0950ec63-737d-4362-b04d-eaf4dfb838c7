import typer
from pathlib import Path
from typing import Optional
import json
from .parser import parse_con

app = typer.Typer()

@app.command()
def main(
    file: Path = typer.Argument(..., help="Path to con.md file"),
    output: Optional[Path] = typer.Option(None, "--output", "-o", help="Output file path")
):
    """
    Parse con.md file and extract conversation data.
    """
    try:
        result = parse_con(file)
        
        if output:
            with open(output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            typer.echo(f"Output saved to {output}")
        else:
            typer.echo(json.dumps(result, ensure_ascii=False, indent=2))
            
    except Exception as e:
        typer.echo(f"Error: {str(e)}", err=True)
        raise typer.Exit(1)

if __name__ == "__main__":
    app()
