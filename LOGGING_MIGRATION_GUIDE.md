# Logging System Migration Guide

## Overview

This guide helps you migrate existing code to use the new 4-file logging system. The new system maintains backward compatibility while providing better organization and component-specific logging.

## Quick Migration Steps

### 1. Update Import Statements

**Old way:**
```python
from app.logging_config import setup_logging, get_layer_logger
```

**New way (recommended):**
```python
from app.logging_config import setup_logging, get_component_logger
```

### 2. Update Logger Initialization

**For Streamlit Applications:**
```python
# Old
logger = get_layer_logger('presentation', 'main')

# New
logger = get_component_logger('streamlit', 'app.main')
```

**For Server/API Components:**
```python
# Old
logger = get_layer_logger('presentation', 'api')

# New
logger = get_component_logger('server', 'app.api.server')
```

**For Crawler Components:**
```python
# Old
logger = logging.getLogger('crawler')

# New
logger = get_component_logger('crawler', 'crawler.server')
```

**For CLI Tools:**
```python
# Old
logger = logging.getLogger(__name__)

# New
logger = get_component_logger('cli', 'app.cli.data_tools_cli')
```

## Component Classification

### Streamlit UI Components → `streamlit.log`
- `app/main.py`
- `run_streamlit.py`
- Any Streamlit-related UI code

**Migration:**
```python
# Before
from app.logging_config import setup_logging
setup_logging()
logger = logging.getLogger('app')

# After
from app.logging_config import setup_logging, get_component_logger
setup_logging()
logger = get_component_logger('streamlit', 'app.main')
```

### Server/API Components → `server.log`
- `app/api/server.py`
- `app/workflows/`
- `app/agents/`
- `app/utils/`
- Uvicorn server logs

**Migration:**
```python
# Before
from app.logging_config import setup_logging, get_layer_logger
setup_logging()
logger = get_layer_logger('presentation', 'api')

# After
from app.logging_config import setup_logging, get_component_logger
setup_logging()
logger = get_component_logger('server', 'app.api.server')
```

### Crawler Components → `crawler.log`
- `crawler/server.py`
- `crawler/core.py`
- `crawler/streamlit_app.py`
- All crawler-related modules

**Migration:**
```python
# Before
from crawler.logging_config import setup_crawler_logging
setup_crawler_logging()
logger = logging.getLogger('crawler')

# After
from app.logging_config import get_component_logger
from crawler.logging_config import setup_crawler_logging
setup_crawler_logging()
logger = get_component_logger('crawler', 'crawler.server')
```

### CLI Components → `cli.log`
- `app/cli/`
- `cli.py`
- Command-line scripts

**Migration:**
```python
# Before
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# After
from app.logging_config import setup_logging, get_component_logger, set_layer_context
setup_logging()
logger = get_component_logger('cli', 'app.cli.data_tools_cli')
set_layer_context('cli')
```

## Context Setting

### Layer Context
Set appropriate layer context for better log organization:

```python
from app.logging_config import set_layer_context, set_operation_context

# For Streamlit UI
set_layer_context('presentation')

# For Server/API
set_layer_context('server')

# For Crawler
set_layer_context('crawler')

# For CLI
set_layer_context('cli')
```

### Operation Context
Set operation context for tracking specific operations:

```python
# Example for user authentication
set_operation_context('user_login')
logger.info("User authentication started")

# Example for data processing
set_operation_context('data_processing')
logger.info("Processing CSV file")
```

## Backward Compatibility

The new system maintains backward compatibility:

1. **Existing logger names still work** - they're automatically routed to appropriate files
2. **Old layer-based logging continues to function** - but consider migrating to component-based approach
3. **Existing log files remain** - new logs go to the 4 new files

## File-by-File Migration Examples

### app/main.py (Streamlit)
```python
# Before
import logging
from app.logging_config import setup_logging
setup_logging()
logger = logging.getLogger('app')

# After
from app.logging_config import setup_logging, get_component_logger, set_operation_context
setup_logging()
logger = get_component_logger('streamlit', 'app.main')

def main():
    set_operation_context('streamlit_app_init')
    logger.info("Streamlit application starting")
```

### app/api/server.py
```python
# Before
from app.logging_config import setup_logging, get_layer_logger
setup_logging()
logger = get_layer_logger('presentation', 'api')

# After
from app.logging_config import setup_logging, get_component_logger, set_layer_context
setup_logging()
logger = get_component_logger('server', 'app.api.server')

@app.on_event("startup")
async def startup_event():
    set_layer_context('server')
    logger.info("API server starting")
```

### crawler/server.py
```python
# Before
from crawler.logging_config import setup_crawler_logging, server_logger
setup_crawler_logging()
logger = server_logger

# After
from app.logging_config import get_component_logger
from crawler.logging_config import setup_crawler_logging, set_crawler_session_context
setup_crawler_logging()
logger = get_component_logger('crawler', 'crawler.server')

async def start_crawler():
    set_crawler_session_context('session_123')
    logger.info("Crawler session started")
```

### app/cli/data_tools_cli.py
```python
# Before
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# After
from app.logging_config import setup_logging, get_component_logger, set_layer_context
setup_logging()
logger = get_component_logger('cli', 'app.cli.data_tools_cli')

def main():
    set_layer_context('cli')
    logger.info("CLI tool started")
```

## Testing Your Migration

1. **Run the test script:**
   ```bash
   python test_logging_system.py
   ```

2. **Check log files are created:**
   ```bash
   ls -la logs/
   # Should show: streamlit.log, server.log, crawler.log, cli.log
   ```

3. **Verify log content:**
   ```bash
   tail -f logs/streamlit.log  # For Streamlit logs
   tail -f logs/server.log    # For Server logs
   tail -f logs/crawler.log   # For Crawler logs
   tail -f logs/cli.log       # For CLI logs
   ```

## Common Migration Issues

### Issue 1: Logs appearing in wrong files
**Cause:** Incorrect component classification or logger name
**Solution:** Use the correct `get_component_logger()` call and verify module names

### Issue 2: Missing context information
**Cause:** Not setting layer or operation context
**Solution:** Add `set_layer_context()` and `set_operation_context()` calls

### Issue 3: Duplicate logs
**Cause:** Multiple logger configurations
**Solution:** Ensure `setup_logging()` is called only once per application

## Rollback Plan

If you need to rollback to the old system:

1. **Revert import changes:**
   ```python
   # Use old imports
   from app.logging_config import setup_logging, get_layer_logger
   ```

2. **Use old logger initialization:**
   ```python
   logger = get_layer_logger('presentation', 'api')
   ```

3. **Old log files will continue to be used** alongside new ones

## Best Practices

1. **Use component-specific loggers** for new code
2. **Set appropriate context** for better log organization
3. **Test logging changes** with the provided test script
4. **Monitor log file sizes** and adjust rotation settings if needed
5. **Use meaningful operation contexts** for easier debugging

## Support

If you encounter issues during migration:
1. Check the test script output for errors
2. Verify environment variables are set correctly
3. Review the main documentation for detailed configuration options
4. Test with a simple example before migrating complex code
