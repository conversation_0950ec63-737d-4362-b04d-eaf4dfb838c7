from con_parser import parse_con
from pathlib import Path
import json

def test_parser():
    test_file = Path("con.md")
    result = parse_con(test_file)
    
    print("Parsing Results:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # Save sample output
    with open("answer.md", "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    print("Saved output to answer.md")

if __name__ == "__main__":
    test_parser()
