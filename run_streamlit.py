#!/usr/bin/env python3
"""
Streamlit Application Launcher

This script properly configures the environment and launches the Streamlit
application with settings to prevent torch.classes and event loop issues.

Usage:
    python run_streamlit.py
    
Or with custom port:
    python run_streamlit.py --port 8501
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def setup_environment():
    """Set up environment variables to prevent torch and Streamlit issues."""
    
    # Streamlit configuration to prevent file watcher issues
    env_vars = {
        'STREAMLIT_SERVER_FILE_WATCHER_TYPE': 'none',
        'STREAMLIT_SERVER_RUN_ON_SAVE': 'false',
        'STREAMLIT_RUNNER_MAGIC_ENABLED': 'false',
        'STREAMLIT_GLOBAL_DEVELOPMENT_MODE': 'false',
        'STREAMLIT_BROWSER_GATHER_USAGE_STATS': 'false',
        
        # Torch configuration to prevent path inspection issues
        # Note: TORCH_LOGS can cause issues, so we don't set it
        'TORCH_SHOW_CPP_STACKTRACES': '0',
        'TORCH_DISTRIBUTED_DEBUG': 'OFF',
        'TORCH_CUDNN_V8_API_DISABLED': '1',
        'TORCH_PROFILER_ENABLED': '0',
        
        # Python configuration
        'PYTHONUNBUFFERED': '1',
        'PYTHONIOENCODING': 'utf-8',
        
        # Disable asyncio debug mode to prevent event loop warnings
        'PYTHONASYNCIODEBUG': '0',
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"✅ Set {key}={value}")

def check_dependencies():
    """Check if required dependencies are installed."""
    
    required_packages = [
        'streamlit',
        'torch',
        'langchain_openai',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Please install them with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def run_streamlit_app(port=8501, host='localhost'):
    """Launch the Streamlit application with proper configuration."""
    
    # Ensure we're in the correct directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Path to the main application
    app_path = script_dir / 'app' / 'main.py'
    
    if not app_path.exists():
        print(f"❌ Application file not found: {app_path}")
        return False
    
    # Streamlit command with configuration
    cmd = [
        sys.executable, '-m', 'streamlit', 'run',
        str(app_path),
        '--server.port', str(port),
        '--server.address', host,
        '--server.fileWatcherType', 'none',
        '--server.runOnSave', 'false',
        '--runner.magicEnabled', 'false',
        '--global.developmentMode', 'false',
        '--server.enableCORS', 'false',
        '--server.enableXsrfProtection', 'false'
    ]
    
    print(f"\n🚀 Starting Streamlit application...")
    print(f"📍 Application: {app_path}")
    print(f"🌐 URL: http://{host}:{port}")
    print(f"🔧 Command: {' '.join(cmd)}")
    print("\n" + "="*50)
    
    try:
        # Run the Streamlit application
        result = subprocess.run(cmd, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ Streamlit application failed with exit code: {e.returncode}")
        return False
    except KeyboardInterrupt:
        print("\n🛑 Application stopped by user")
        return True

def main():
    """Main entry point."""
    
    parser = argparse.ArgumentParser(description='Launch Streamlit application with torch.classes fix')
    parser.add_argument('--port', type=int, default=8501, help='Port to run Streamlit on (default: 8501)')
    parser.add_argument('--host', type=str, default='localhost', help='Host to run Streamlit on (default: localhost)')
    parser.add_argument('--skip-deps-check', action='store_true', help='Skip dependency check')
    
    args = parser.parse_args()
    
    print("🔧 Configuring environment for Streamlit with torch.classes fix...")
    
    # Set up environment variables
    setup_environment()
    
    # Check dependencies unless skipped
    if not args.skip_deps_check:
        print("\n📦 Checking dependencies...")
        if not check_dependencies():
            sys.exit(1)
    
    # Launch the application
    success = run_streamlit_app(port=args.port, host=args.host)
    
    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
