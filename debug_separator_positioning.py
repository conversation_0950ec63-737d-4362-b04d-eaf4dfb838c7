#!/usr/bin/env python3
"""
Debug the separator positioning logic to see why it's not working correctly
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reheader import MarkdownTableHeaderAdjuster

def debug_separator_positioning():
    """Debug the separator positioning step by step"""
    print("="*80)
    print("DEBUGGING SEPARATOR POSITIONING")
    print("="*80)
    
    # Read the original input file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Original content:")
    print("-" * 40)
    print(content)
    
    # Create adjuster with debug enabled
    adjuster = MarkdownTableHeaderAdjuster()
    adjuster.debug_output = True
    
    print("\n" + "="*60)
    print("STEP-BY-STEP ALGORITHM EXECUTION")
    print("="*60)
    
    # Parse the table
    rows = adjuster.parse_table(content)
    print(f"Parsed {len(rows)} rows")
    
    # Find existing separators
    separator_indices = []
    for i, row in enumerate(rows):
        if adjuster.is_separator_row(row):
            separator_indices.append(i)
            print(f"Found existing separator at row {i+1}: {row}")
    
    # Get header candidates
    candidates = adjuster.find_all_header_candidates(rows)
    print(f"\nTop 3 header candidates:")
    for i, (row_idx, score) in enumerate(candidates[:3]):
        print(f"  {i+1}. Row {row_idx+1}: Score {score:.2f} - {rows[row_idx]}")
    
    # Check what the algorithm should do
    if candidates:
        best_header_row = candidates[0][0]  # Row index (0-based)
        expected_separator_pos = best_header_row + 1  # Should be after the header
        
        print(f"\nExpected logic:")
        print(f"  Best header row: {best_header_row + 1} ('{rows[best_header_row]}')")
        print(f"  Expected separator position: {expected_separator_pos + 1}")
        
        if expected_separator_pos < len(rows):
            print(f"  Row at expected separator position: {rows[expected_separator_pos]}")
        
        # Check if there's already a separator there
        if expected_separator_pos in separator_indices:
            print(f"  ✅ Separator already exists at correct position")
        else:
            print(f"  ❌ No separator at expected position - should be inserted")
    
    print(f"\n" + "="*60)
    print("RUNNING FULL ALGORITHM")
    print("="*60)
    
    # Run the full algorithm and see what happens
    result = adjuster.adjust_table(content)
    
    print("Final result:")
    print("-" * 40)
    print(result)
    
    # Parse the result to see where the separator ended up
    result_rows = adjuster.parse_table(result)
    result_separator_indices = []
    
    for i, row in enumerate(result_rows):
        if adjuster.is_separator_row(row):
            result_separator_indices.append(i)
            print(f"\nResult: Separator at row {i+1}: {row}")
            
            # Show what's before and after the separator
            if i > 0:
                print(f"  Before separator: {result_rows[i-1]}")
            if i + 1 < len(result_rows):
                print(f"  After separator: {result_rows[i+1]}")

def main():
    """Main debug function"""
    try:
        debug_separator_positioning()
        return 0
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
