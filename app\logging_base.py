"""
Shared Logging Infrastructure

This module provides common logging components used by both app and crawler modules:
- Common formatters (UTF8JsonFormatter)
- Common filters (SensitiveDataFilter, RequestIdFilter, ExceptionContextFilter)
- Common context variables and management functions
- Shared utility functions

This prevents code duplication while maintaining separation between app and crawler logging.
"""

import logging
import json
import re
from typing import Optional
from contextvars import ContextVar

# Shared context variables for enhanced logging
request_id_var: ContextVar[str] = ContextVar('request_id', default=None)
layer_context_var: ContextVar[str] = ContextVar('layer_context', default=None)
operation_context_var: ContextVar[str] = ContextVar('operation_context', default=None)


class UTF8JsonFormatter(logging.Formatter):
    """Custom JSON formatter that ensures UTF-8 encoding and includes error handling context."""

    def format(self, record):
        """Format the log record as JSON."""
        log_record = {
            'asctime': self.formatTime(record),
            'level': record.levelname,
            'name': record.name,
            'process': record.process,
            'thread': record.thread,
            'module': record.module,
            'funcName': record.funcName,
            'pathname': record.pathname,
            'lineno': record.lineno,
            'message': record.getMessage(),
        }

        # Add request ID and context if available
        if hasattr(record, 'request_id') and record.request_id:
            log_record['request_id'] = record.request_id
        if hasattr(record, 'layer_context') and record.layer_context:
            log_record['layer'] = record.layer_context
        if hasattr(record, 'operation_context') and record.operation_context:
            log_record['operation'] = record.operation_context

        # Add exception context if available
        if hasattr(record, 'error_code') and record.error_code:
            log_record['error_code'] = record.error_code
        if hasattr(record, 'error_layer') and record.error_layer:
            log_record['error_layer'] = record.error_layer
        if hasattr(record, 'error_severity') and record.error_severity:
            log_record['error_severity'] = record.error_severity
        if hasattr(record, 'error_context') and record.error_context:
            log_record['error_context'] = record.error_context
        if hasattr(record, 'suggested_action') and record.suggested_action:
            log_record['suggested_action'] = record.suggested_action
        if hasattr(record, 'original_exception_type') and record.original_exception_type:
            log_record['original_exception_type'] = record.original_exception_type

        # Add exception info if present
        if record.exc_info:
            log_record['exc_info'] = self.formatException(record.exc_info)

        return json.dumps(log_record, ensure_ascii=False, default=str)


class RequestIdFilter(logging.Filter):
    """Injects the request_id and layer context into the log record."""
    def filter(self, record: logging.LogRecord) -> bool:
        record.request_id = request_id_var.get()
        record.layer_context = layer_context_var.get()
        record.operation_context = operation_context_var.get()
        return True


class ExceptionContextFilter(logging.Filter):
    """Enhances log records with exception context from unified error handling."""

    def filter(self, record: logging.LogRecord) -> bool:
        # Check if this is an exception record with our custom exception
        if hasattr(record, 'exc_info') and record.exc_info:
            exc_type, exc_value, exc_traceback = record.exc_info

            # If it's one of our custom exceptions, extract additional context
            if hasattr(exc_value, 'get_full_context'):
                try:
                    context = exc_value.get_full_context()
                    # Add exception context to log record
                    record.error_code = context.get('error_code')
                    record.error_layer = context.get('layer')
                    record.error_severity = context.get('severity')
                    record.error_context = context.get('context', {})
                    record.suggested_action = context.get('suggested_action')
                    record.original_exception_type = context.get('original_exception_type')
                except Exception:
                    # Don't let filter errors break logging
                    pass

        return True


class SensitiveDataFilter(logging.Filter):
    """Filter to mask sensitive data in logs."""
    def __init__(self):
        super().__init__()
        self.patterns = [
            re.compile(r'\b(sk|pk|rk)-[a-zA-Z0-9-]{20,}\b', re.IGNORECASE),  # More robust API key pattern
        ]

    def mask(self, message: str) -> str:
        # Ensure message is a string before masking
        message = str(message)
        for pattern in self.patterns:
            message = pattern.sub('***MASKED***', message)
        return message

    def filter(self, record: logging.LogRecord) -> bool:
        if isinstance(record.msg, str):
            record.msg = self.mask(record.msg)
        if record.args:
            new_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    new_args.append(self.mask(arg))
                else:
                    new_args.append(arg)
            record.args = tuple(new_args)
        return True


# Utility functions for setting logging context
def set_layer_context(layer: str):
    """Set the current layer context for logging."""
    layer_context_var.set(layer)


def set_operation_context(operation: str):
    """Set the current operation context for logging."""
    operation_context_var.set(operation)


def get_layer_context() -> Optional[str]:
    """Get the current layer context for logging."""
    return layer_context_var.get()


def get_operation_context() -> Optional[str]:
    """Get the current operation context for logging."""
    return operation_context_var.get()


def clear_logging_context():
    """Clear all logging context variables."""
    layer_context_var.set(None)
    operation_context_var.set(None)


def set_request_id(request_id: str):
    """Set the current request ID for logging."""
    request_id_var.set(request_id)


def get_request_id() -> Optional[str]:
    """Get the current request ID for logging."""
    return request_id_var.get()


def clear_request_context():
    """Clear request-specific context variables."""
    request_id_var.set(None)
