你是数据工程师，请阅读和分析给定的markdown内容，清理markdown格式表格的内容：
        1. 注意仅清理markdown表格中的内容，务必远洋保留markdown标题等表格外内容：
            - 保留以"#"、“##”、"###"、"####"、"#####"、"######"等标题字符开头的行
            - 保留“*"、"**"等类似标题字符开头的行
            - 保留为表名的行，如“资产负债表”，“利润表”，“现金流量表”等
        2. 观察markdown表格的表头，务必保留数据列，不得删除有意义的数据列，尤其是表头的数据列，如“日期”，“科目”，“数量”，“单价”，“金额”等。
        3. 识别任何形式的公司名称声明（如"公司：XXX"或"单位:XXX"），用''替换，注意小心区分“单位”的含义，做为计量单位应该保留。
        4. 对markdown表格内容：
            a. 完全保留所有表格及其结构
            b. 绝不修改或删除表头和数据列
            c. 绝不截断markdown table中的数据行或删除明显是连续数据的表格行
            - 若存在“行次”，关注行数，必须保证行的数量完整
            d. 仅移除表格中以下类型的水印：
            - 随机分散的字符（仅、效、无、印、贷，复等）
            - 数据前后明显不正确的字符
            - HTML标签（<br>等）
            - 明显不属于表格内容的文本
            - 明显无意义的文本
            e. 保留所有有意义的表格数据，包括：
            - 量纲（如“单位： 元/个/万/亿”）
            - 所有列标题和数据
            特别注意保留表格内数据列下的公司名称（这些是数据而非声明）
            f. 保持内容自然且可读
        5. 仅返回清理后的原始markdown内容：
           - 绝对不要添加```markdown```等代码块标记
           - 不要添加任何解释或说明文本
           - 直接返回清理后的纯净markdown内容

        待清理内容：
        {content}