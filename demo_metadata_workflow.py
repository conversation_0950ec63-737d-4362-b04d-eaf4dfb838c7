#!/usr/bin/env python3
"""
Demonstration of the complete metadata workflow:
1. Process markdown files with reform.py to extract metadata
2. Upload files to RAGFlow with automatic metadata updating
"""

import os
import tempfile
import shutil
from pathlib import Path
from app.utils.reform import MarkdownTableReformer
from app.utils.md_tools import MarkdownTools

def demo_complete_workflow():
    """Demonstrate the complete workflow from reform to RAGFlow upload"""
    
    # Create a temporary directory for the demo
    demo_dir = tempfile.mkdtemp()
    print(f"Demo directory: {demo_dir}")
    
    try:
        # Step 1: Create a sample markdown file with metadata comment
        sample_content = '''<!-- METADATA: {"file": "financial_report.md", "table_name": "资产负债表", "date": "2024年9月30日", "metadata": ["公司: 示例公司", "编制: 财务部", "单位: 万元"]} -->

# 资产负债表

## 2024年9月30日

| 项目 | 期末余额 | 期初余额 |
|------|----------|----------|
| 流动资产 | 1000 | 800 |
| 固定资产 | 2000 | 1800 |
| 总资产 | 3000 | 2600 |
| 流动负债 | 500 | 400 |
| 长期负债 | 800 | 700 |
| 总负债 | 1300 | 1100 |
| 所有者权益 | 1700 | 1500 |
'''
        
        input_file = os.path.join(demo_dir, "financial_report.md")
        with open(input_file, 'w', encoding='utf-8') as f:
            f.write(sample_content)
        
        print("✓ Created sample markdown file with metadata")
        
        # Step 2: Process the file with reform.py to extract metadata
        reformer = MarkdownTableReformer()
        output_file = os.path.join(demo_dir, "financial_report_reformed.md")
        reformer.process_file(input_file, output_file)
        
        print("✓ Processed file with reform.py - metadata extracted to separate JSON file")
        
        # Verify the files were created
        json_file = os.path.join(demo_dir, "financial_report_reformed.json")
        
        if os.path.exists(output_file):
            print(f"  - Reformed markdown file: {output_file}")
            with open(output_file, 'r', encoding='utf-8') as f:
                reformed_content = f.read()
            print(f"  - Reformed content preview (first 200 chars): {reformed_content[:200]}...")
        
        if os.path.exists(json_file):
            print(f"  - Metadata JSON file: {json_file}")
            with open(json_file, 'r', encoding='utf-8') as f:
                metadata_content = f.read()
            print(f"  - Metadata content: {metadata_content}")
        
        # Step 3: Demonstrate how RAGFlow upload would work
        print("\n✓ Files ready for RAGFlow upload with metadata")
        print("  When uploading to RAGFlow:")
        print(f"  1. Upload {output_file} (markdown file)")
        print(f"  2. Automatically detect {json_file} (metadata file)")
        print("  3. Parse JSON metadata and update document in RAGFlow")
        print("  4. Document in RAGFlow will have rich metadata attached")
        
        # Step 4: Show what the RAGFlow upload command would look like
        print("\n✓ Example RAGFlow upload command:")
        print("  tools = MarkdownTools()")
        print(f"  tools.command_ragflow_upload(files=['{output_file}'])")
        print("  # This would automatically:")
        print("  #   - Upload the markdown file")
        print("  #   - Detect the corresponding JSON metadata file")
        print("  #   - Update the document with metadata from JSON")
        
        # Step 5: Show the integration benefits
        print("\n✓ Integration Benefits:")
        print("  - Metadata is preserved from processing to upload")
        print("  - No manual metadata entry required")
        print("  - Consistent metadata format across all documents")
        print("  - Automatic detection and updating")
        print("  - Error handling for malformed or missing metadata")
        
        return demo_dir
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        shutil.rmtree(demo_dir)
        raise

def demo_batch_processing():
    """Demonstrate batch processing of multiple files"""
    
    demo_dir = tempfile.mkdtemp()
    print(f"\nBatch processing demo directory: {demo_dir}")
    
    try:
        # Create multiple sample files
        files_data = [
            ("balance_sheet.md", '''<!-- METADATA: {"table_name": "资产负债表", "date": "2024年Q3"} -->
# 资产负债表
| 资产 | 金额 |
|------|------|
| 现金 | 1000 |
'''),
            ("income_statement.md", '''<!-- METADATA: {"table_name": "利润表", "date": "2024年Q3"} -->
# 利润表
| 项目 | 金额 |
|------|------|
| 收入 | 5000 |
'''),
            ("cash_flow.md", '''<!-- METADATA: {"table_name": "现金流量表", "date": "2024年Q3"} -->
# 现金流量表
| 项目 | 金额 |
|------|------|
| 经营活动 | 2000 |
''')
        ]
        
        input_files = []
        for filename, content in files_data:
            file_path = os.path.join(demo_dir, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            input_files.append(file_path)
        
        print(f"✓ Created {len(input_files)} sample files")
        
        # Process all files with reform.py
        reformer = MarkdownTableReformer()
        output_files = reformer.process_files(input_files, demo_dir)
        
        print(f"✓ Processed {len(output_files)} files with reform.py")
        
        # Check that JSON metadata files were created
        json_files = []
        for output_file in output_files:
            json_file = os.path.splitext(output_file)[0] + '.json'
            if os.path.exists(json_file):
                json_files.append(json_file)
        
        print(f"✓ Created {len(json_files)} metadata JSON files")
        
        # Show what batch RAGFlow upload would look like
        print("\n✓ Batch RAGFlow upload would process:")
        for i, (md_file, json_file) in enumerate(zip(output_files, json_files), 1):
            print(f"  {i}. {Path(md_file).name} + {Path(json_file).name}")
        
        print("\n✓ All files would be uploaded with their metadata automatically applied")
        
        return demo_dir
        
    except Exception as e:
        print(f"❌ Batch demo failed: {e}")
        shutil.rmtree(demo_dir)
        raise

if __name__ == "__main__":
    print("🚀 Metadata Workflow Demonstration")
    print("=" * 50)
    
    # Run single file demo
    print("\n📄 Single File Processing Demo:")
    demo_dir1 = demo_complete_workflow()
    
    # Run batch processing demo  
    print("\n📁 Batch Processing Demo:")
    demo_dir2 = demo_batch_processing()
    
    print("\n" + "=" * 50)
    print("✅ Demonstration completed successfully!")
    print("\nKey Features Demonstrated:")
    print("- Metadata extraction from processed markdown files")
    print("- Automatic JSON metadata file creation")
    print("- Integration with RAGFlow upload functionality")
    print("- Batch processing capabilities")
    print("- Error handling and validation")
    
    # Clean up demo directories
    try:
        shutil.rmtree(demo_dir1)
        shutil.rmtree(demo_dir2)
        print("\n🧹 Demo directories cleaned up")
    except:
        pass
