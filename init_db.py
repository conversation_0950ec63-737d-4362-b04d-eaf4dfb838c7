#!/usr/bin/env python3
"""
Database initialization script for the auto-report system.
Creates sample users and sets up the database structure.
"""

import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from passlib.context import CryptContext

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.api.server import Base, User, Template

def init_database():
    """Initialize the database with sample data"""
    
    # Configuration
    DB_PATH = os.getenv("DB_PATH", "data/users.db")
    
    # Ensure data directory exists
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    
    # Create database engine
    engine = create_engine(f"sqlite:///{DB_PATH}")
    
    # Create all tables
    Base.metadata.create_all(engine)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    # Password hashing
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    # Sample users
    sample_users = [
        {
            "username": "admin",
            "password": "admin123",
            "workspace_id": "admin_workspace"
        },
        {
            "username": "user1",
            "password": "user123",
            "workspace_id": "dept_finance"
        },
        {
            "username": "user2", 
            "password": "user123",
            "workspace_id": "dept_risk"
        },
        {
            "username": "test",
            "password": "test123",
            "workspace_id": "test_workspace"
        }
    ]
    
    # Create users if they don't exist
    for user_data in sample_users:
        existing_user = db.query(User).filter(User.username == user_data["username"]).first()
        if not existing_user:
            hashed_password = pwd_context.hash(user_data["password"])
            user = User(
                username=user_data["username"],
                password_hash=hashed_password,
                workspace_id=user_data["workspace_id"]
            )
            db.add(user)
            print(f"Created user: {user_data['username']} (workspace: {user_data['workspace_id']})")
        else:
            print(f"User {user_data['username']} already exists")
    
    # Create workspace template directories
    for user_data in sample_users:
        template_dir = os.path.join("templates", user_data["workspace_id"])
        os.makedirs(template_dir, exist_ok=True)
        print(f"Created template directory: {template_dir}")
        
        # Create a sample template file if it doesn't exist
        sample_template_path = os.path.join(template_dir, "sample_template.md")
        if not os.path.exists(sample_template_path):
            with open(sample_template_path, 'w', encoding='utf-8') as f:
                f.write("""# 样本报告模板

## 公司概况
{{company_name}} 是一家专业的金融服务公司。

## 财务状况
### 资产负债表
- 总资产：{{total_assets}}
- 总负债：{{total_liabilities}}
- 净资产：{{net_assets}}

### 利润表
- 营业收入：{{revenue}}
- 净利润：{{net_profit}}

## 业务数据
### 放贷规模
- 累计放贷：{{total_loans}}
- 在贷余额：{{outstanding_balance}}

### 风险指标
- 不良率：{{npl_rate}}
- 逾期率：{{overdue_rate}}

## 总结
{{company_name}} 在 {{year}} 年度表现良好。
""")
            
            # Add template to database
            template = Template(
                name="sample_template",
                path=sample_template_path,
                workspace_id=user_data["workspace_id"]
            )
            db.add(template)
            print(f"Created sample template for workspace: {user_data['workspace_id']}")
    
    # Commit changes
    db.commit()
    db.close()
    
    print("\nDatabase initialization completed!")
    print("\nSample users created:")
    for user_data in sample_users:
        print(f"  Username: {user_data['username']}, Password: {user_data['password']}, Workspace: {user_data['workspace_id']}")
    
    print(f"\nDatabase location: {DB_PATH}")
    print("You can now start the server and login with any of the sample users.")

if __name__ == "__main__":
    init_database()
