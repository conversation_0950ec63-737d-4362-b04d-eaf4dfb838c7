# Unified Error Handling Strategy Guide

## Overview

This document describes the unified error handling strategy implemented across the Auto-Report application. The strategy enforces proper error handling across three application layers with specific rules for exception management, logging, and boundary handling.

## Architecture

### Layer Structure

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
│                /app/main.py, /app/api, /app/cli            │
│  - User interfaces and API endpoints                       │
│  - Input validation and response formatting                │
│  - Authentication and authorization                        │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                  BUSINESS LOGIC LAYER                       │
│                /app/agents, /app/workflows                 │
│  - Core business operations and workflows                  │
│  - Agent processing and decision making                    │
│  - Data transformation and validation                      │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                     UTILITY LAYER                          │
│                      /app/utils                            │
│  - File processing and I/O operations                     │
│  - Data manipulation and formatting                       │
│  - External service integrations                          │
└─────────────────────────────────────────────────────────────┘
```

### Core Principles

1. **Boundary Exception Management**: All exceptions crossing layer boundaries must be caught and handled with comprehensive logging and context wrapping
2. **Exception Flow Control Prohibition**: Never use exceptions for normal program flow control
3. **Intra-Layer Exception Propagation**: Exceptions within the same layer propagate transparently to callers
4. **Exception Wrapping Requirements**: Maintain full stack trace chain with meaningful context
5. **Try-Catch Minimization**: Only use try-catch when adding value (logging, context, recovery)

## Exception Hierarchy

### Base Exception Classes

```python
from app.exceptions import (
    BaseAppException,           # Base for all application exceptions
    LayerType,                 # Enum: PRESENTATION, BUSINESS_LOGIC, UTILITY
    ErrorSeverity             # Enum: LOW, MEDIUM, HIGH, CRITICAL
)
```

### Layer-Specific Exceptions

#### Presentation Layer
```python
from app.exceptions import (
    PresentationLayerException,  # Base presentation layer exception
    APIException,               # API-specific with HTTP status codes
    AuthenticationException,    # Authentication/authorization failures
    ValidationException,        # Input validation errors
    UIException,               # User interface errors
    CLIException              # Command line interface errors
)
```

#### Business Logic Layer
```python
from app.exceptions import (
    BusinessLogicException,     # Base business logic exception
    WorkflowException,         # Workflow execution errors
    AgentException,           # Agent processing errors
    RAGWorkflowException,     # RAG workflow specific errors
    ReportWorkflowException,  # Report workflow specific errors
    ModelException           # LLM and model related errors
)
```

#### Utility Layer
```python
from app.exceptions import (
    UtilityException,          # Base utility layer exception
    FileProcessingException,   # File I/O and processing errors
    DataProcessingException,   # Data transformation errors
    ConfigurationException,    # Configuration and setup errors
    ExternalServiceException  # External service integration errors
)
```

## Usage Patterns

### 1. Layer Boundary Handling

Use the `@handle_layer_boundary` decorator for functions that cross layer boundaries:

```python
from app.error_handling import handle_layer_boundary
from app.exceptions import LayerType

# API endpoint calling business logic
@handle_layer_boundary(LayerType.PRESENTATION, "user authentication")
def authenticate_user(username: str, password: str):
    # This calls business logic layer
    return auth_service.authenticate(username, password)

# Business logic calling utility layer
@handle_layer_boundary(LayerType.BUSINESS_LOGIC, "document processing")
def process_document(file_path: str):
    # This calls utility layer
    return file_processor.convert_to_markdown(file_path)
```

### 2. Within-Layer Error Handling

Use `@log_and_reraise` for adding logging context without wrapping exceptions:

```python
from app.error_handling import log_and_reraise

@log_and_reraise(logger, "user data validation")
def validate_user_data(data: dict):
    # Function within the same layer
    if not data.get('email'):
        raise ValidationException("Email is required")
    return validated_data
```

### 3. Error Boundaries

Use `error_boundary` context manager for specific code blocks:

```python
from app.error_handling import error_boundary

def process_files(file_list):
    with error_boundary("file processing batch", LayerType.UTILITY):
        results = []
        for file_path in file_list:
            result = process_single_file(file_path)
            results.append(result)
        return results
```

### 4. Safe Execution

Use `safe_execute` for operations that should not fail the entire process:

```python
from app.error_handling import safe_execute

def optional_cleanup():
    # This won't fail the main process if it errors
    safe_execute(
        cleanup_temp_files,
        operation_context="temporary file cleanup",
        layer=LayerType.UTILITY,
        default_return=None
    )
```

## Exception Creation Best Practices

### Creating Custom Exceptions

```python
# Good: Specific, informative exception
raise FileProcessingException(
    message="Failed to convert PDF to markdown",
    details=f"PDF file corrupted or unsupported format: {file_path}",
    file_path=file_path,
    context={'file_size': os.path.getsize(file_path)},
    suggested_action="Check file integrity and try with a different PDF"
)

# Bad: Generic, uninformative exception
raise Exception("File error")
```

### Exception Context

Always provide meaningful context:

```python
raise ValidationException(
    message="Invalid report parameters",
    details="Company name contains invalid characters",
    context={
        'company_name': company_name,
        'invalid_chars': invalid_chars_found,
        'user_id': current_user.id
    },
    field_name="company_name",
    suggested_action="Use only alphanumeric characters and spaces"
)
```

## Logging Integration

### Enhanced Logging Context

The unified error handling integrates with enhanced logging that captures:

- Layer context (presentation, business_logic, utility)
- Operation context (specific operation being performed)
- Exception details (error codes, severity, suggested actions)
- Request tracing (request IDs, user context)

### Setting Logging Context

```python
from app.logging_config import set_layer_context, set_operation_context

# Set context for better logging
set_layer_context("business_logic")
set_operation_context("report generation")

# Your code here - all logs will include this context
logger.info("Starting report generation process")
```

### Layer-Specific Loggers

```python
from app.logging_config import get_layer_logger

# Get a logger configured for specific layer
logger = get_layer_logger("utility", "file_processor")
```

## Migration from Legacy Code

### Replacing Old Exception Classes

```python
# Old code
try:
    result = risky_operation()
except OldCustomException as e:
    logger.error(f"Operation failed: {e}")
    return {"error": str(e)}

# New code
@handle_layer_boundary(LayerType.BUSINESS_LOGIC, "risky operation")
def safe_risky_operation():
    return risky_operation()

# The decorator handles exception wrapping and logging automatically
```

### Removing Defensive Try-Catch

```python
# Old code - defensive try-catch that adds no value
def process_data(data):
    try:
        return transform_data(data)
    except Exception as e:
        logger.error(f"Error: {e}")
        raise  # Just re-raising without adding value

# New code - let exceptions propagate naturally
def process_data(data):
    # No try-catch needed - let exceptions propagate
    # Use decorators only when adding meaningful context
    return transform_data(data)
```

## Testing Error Handling

### Testing Exception Wrapping

```python
import pytest
from app.exceptions import ValidationException

def test_validation_error_handling():
    with pytest.raises(ValidationException) as exc_info:
        validate_user_input(invalid_data)
    
    assert exc_info.value.error_code == "VAL_001"
    assert "email" in exc_info.value.context
    assert exc_info.value.suggested_action is not None
```

### Testing Layer Boundaries

```python
def test_layer_boundary_exception_wrapping():
    # Mock utility layer to raise an exception
    with patch('app.utils.file_processor.process') as mock_process:
        mock_process.side_effect = FileNotFoundError("File missing")
        
        # Business logic should wrap the utility exception
        with pytest.raises(BusinessLogicException) as exc_info:
            business_logic_function()
        
        # Verify wrapping preserved original exception
        assert exc_info.value.original_exception is not None
        assert isinstance(exc_info.value.original_exception, FileNotFoundError)
```

## Performance Considerations

1. **Exception Creation Cost**: Custom exceptions with rich context have minimal overhead
2. **Logging Performance**: Enhanced logging uses efficient context variables
3. **Stack Trace Preservation**: Full stack traces are maintained without duplication
4. **Memory Usage**: Exception context is cleaned up automatically

## Monitoring and Alerting

### Error Codes for Monitoring

All exceptions include standardized error codes for monitoring:

- `AUTH_001`: Authentication failures
- `VAL_001`: Validation errors  
- `FILE_001`: File processing errors
- `RAG_001`: RAG workflow errors
- `API_001`: API-specific errors

### Log Analysis

Enhanced logs support structured analysis:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "ERROR",
  "layer": "business_logic",
  "operation": "document_processing",
  "error_code": "FILE_001",
  "error_severity": "high",
  "suggested_action": "Check file permissions",
  "request_id": "req_123456"
}
```

## Common Patterns and Anti-Patterns

### ✅ Good Patterns

1. **Meaningful Exception Messages**
2. **Rich Context Information**
3. **Appropriate Error Codes**
4. **Clear Suggested Actions**
5. **Proper Layer Boundary Handling**

### ❌ Anti-Patterns

1. **Generic Exception Messages**
2. **Swallowing Exceptions**
3. **Using Exceptions for Control Flow**
4. **Defensive Try-Catch Without Value**
5. **Breaking Exception Chains**

## Troubleshooting

### Common Issues

1. **Missing Context**: Ensure all custom exceptions include relevant context
2. **Broken Stack Traces**: Use `original_exception` parameter when wrapping
3. **Performance Issues**: Avoid creating exceptions in hot paths
4. **Log Noise**: Use appropriate severity levels

### Debug Tools

1. **Exception Context Inspection**: Use `get_full_context()` method
2. **Layer Tracing**: Check `layer_context` in logs
3. **Request Tracing**: Follow `request_id` through logs
4. **Stack Trace Analysis**: Use `original_stack_trace` for debugging
