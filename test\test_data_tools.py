import unittest
import pandas as pd
from app.utils.data_tools import get_aggregated_data, load_to_sqlite, query_data_from_sqlite, calculate_yoy, calculate_mom, process_data, extract_braces_content
from app.utils.common import GroupType
import os
import sqlite3
import shutil
from io import StringIO

class TestDataTools(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        cls.test_dir = 'test_temp_data'
        os.makedirs(cls.test_dir, exist_ok=True)
        cls.db_path = os.path.join(cls.test_dir, 'test_report.db')
        cls.sample_csv_path = os.path.join(cls.test_dir, 'sample.csv')

        cls.sample_csv_content = '''日期,新增放款金额,新增放款笔数,新增放款客户数,期末在贷金额,期末在贷笔数,期末在贷客户数,计量单位
2023年3月,4408856860,1324798,916315,17313281910,7880058,2182780,元
2023年6月,4777944092,1258406,916527,19817354363.96,8485847,2376058,元
2023年9月,5129938531,1252392,912576,22415188688.12,8908922,2596088,元
2023年12月,4815118876,1146497,802520,22752177295.19,8913018,2637133,元
2024年3月,4738033673,1087427,764014,21767286786.26,8335139,2551766,元
2024年6月,5360400598,1183396,841342,22486280973.36,8165781,2542306,元
2024年9月,6095069195,1283674,897382,24548347478.15,8319565,2608616,元
2024年12月,7013991084,1475496,1029367,27142894901.95,8885911,2829231,元'''
        
        with open(cls.sample_csv_path, 'w', encoding='utf-8') as f:
            f.write(cls.sample_csv_content)

        # Monkey patch sqlite3.connect
        cls.original_connect = sqlite3.connect
        def connect_test_db(path):
            # Ignore the path argument and connect to our test DB
            return cls.original_connect(cls.db_path)
        sqlite3.connect = connect_test_db
        
        # Load data once for all db tests
        load_to_sqlite(cls.sample_csv_path, table_name='biz_table')

    @classmethod
    def tearDownClass(cls):
        # Restore original sqlite3.connect
        sqlite3.connect = cls.original_connect
        # Give a moment for the file lock to be released
        import time
        time.sleep(0.1)
        if os.path.exists(cls.test_dir):
            shutil.rmtree(cls.test_dir, ignore_errors=True)

    def test_get_aggregated_data_defaults(self):
        result = get_aggregated_data(self.sample_csv_content)
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 8)
        self.assertIn('日期', result[0])
        self.assertIn('新增放款金额', result[0])
        self.assertEqual(result[0]['日期'], '2023年3月')

    def test_get_aggregated_data_desc_order(self):
        result = get_aggregated_data(self.sample_csv_content, order='desc')
        self.assertEqual(len(result), 8)
        self.assertEqual(result[0]['日期'], '2024年12月')

    def test_get_aggregated_data_group_by_year(self):
        result = get_aggregated_data(self.sample_csv_content, group_type=GroupType.YEAR)
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]['日期'], '2023年')
        self.assertEqual(result[1]['日期'], '2024年')
        self.assertAlmostEqual(result[0]['新增放款金额'], 19131858359, delta=1)
        self.assertAlmostEqual(result[1]['新增放款金额'], 23207494550, delta=1)

    def test_query_data_from_sqlite(self):
        df = query_data_from_sqlite(table='biz_table')
        self.assertIsInstance(df, pd.DataFrame)
        self.assertEqual(len(df), 8)

    def test_query_with_filter(self):
        df_filtered = query_data_from_sqlite(table='biz_table', filters=["日期=2023-03-01"])
        self.assertEqual(len(df_filtered), 1)
        self.assertEqual(df_filtered.iloc[0]['新增放款金额'], 4408856860)

    def test_query_with_grouping(self):
        df_grouped = query_data_from_sqlite(table='biz_table', group_by='year')
        self.assertEqual(len(df_grouped), 2)
        self.assertEqual(df_grouped.iloc[0]['日期'], '2023年')
        self.assertAlmostEqual(df_grouped.iloc[0]['新增放款金额'], 19131858359, delta=1)

    def test_calculate_yoy(self):
        result = calculate_yoy(table='biz_table', column='新增放款金额', group_by='quarter', output_format='csv')
        self.assertIsInstance(result, str)
        lines = result.strip().split('\n')
        self.assertIn('同比', lines[0])
        df = pd.read_csv(StringIO(result))
        self.assertEqual(len(df), 8)
        self.assertEqual(df['同比'].notna().sum(), 4)


    def test_calculate_mom(self):
        result = calculate_mom(table='biz_table', column='新增放款金额', group_by='quarter', output_format='csv')
        self.assertIsInstance(result, str)
        lines = result.strip().split('\n')
        self.assertIn('环比', lines[0])
        df = pd.read_csv(StringIO(result))
        self.assertEqual(len(df), 8)
        self.assertEqual(df['环比'].notna().sum(), 7)

    def test_extract_braces_content(self):
        file_path = os.path.join(self.test_dir, 'test_braces.txt')
        with open(file_path, 'w') as f:
            f.write("some text {content1} and {content2}")
        
        result = extract_braces_content(file_path)
        self.assertEqual(result, ['content1', 'content2'])

if __name__ == '__main__':
    unittest.main()