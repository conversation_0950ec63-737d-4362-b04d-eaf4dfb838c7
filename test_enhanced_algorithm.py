#!/usr/bin/env python3
"""
Test the enhanced position-weighted scoring algorithm
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_enhanced_scoring():
    """Test the enhanced scoring without calling the full algorithm"""
    print("=== Testing Enhanced Position-Weighted Scoring ===")
    
    # Read the target file
    with open('test3/财务报表2024.09_利润表 _A1C34_merged_adjusted.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Original content (lines 4-8):")
    lines = content.split('\n')
    for i, line in enumerate(lines[3:8], 4):
        marker = ' <-- CURRENT SEPARATOR' if '---' in line else (' <-- TARGET HEADER' if '项目' in line else '')
        print(f'{i:2d}: {line}{marker}')
    
    # Manual parsing to avoid potential issues
    table_lines = []
    for line in lines:
        if line.strip().startswith('|') and line.strip().endswith('|'):
            # Parse table row
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            table_lines.append(cells)
    
    print(f"\nParsed {len(table_lines)} table rows:")
    for i, row in enumerate(table_lines[:8]):
        print(f'Row {i}: {row}')
    
    # Test position-weighted scoring manually
    print("\n=== Manual Position-Weighted Scoring ===")
    
    high_priority_keywords = ['项目', '科目', '行次', '序号']
    
    # Test different separator positions
    test_positions = [2, 3, 4, 5]  # Rows 2, 3, 4, 5
    
    for pos in test_positions:
        if pos <= 0 or pos >= len(table_lines):
            continue
            
        # The header row is the one just before the separator
        header_row_position = pos - 1
        
        if 0 <= header_row_position < len(table_lines):
            header_row = table_lines[header_row_position]
            
            # Check for high-priority keywords
            row_text = ' '.join(header_row)
            has_high_priority_keywords = any(keyword in row_text for keyword in high_priority_keywords)
            
            # Calculate keyword score
            keyword_score = 1.0 if has_high_priority_keywords else 0.0
            
            # Calculate position-based weights (1-based row numbers)
            row_number = header_row_position + 1
            
            if row_number <= 3:
                keyword_weight = 1.2
                density_weight = 0.8
            elif row_number <= 6:
                keyword_weight = 1.0
                density_weight = 1.0
            elif row_number <= 10:
                weight_factor = (10 - row_number) / 4.0
                keyword_weight = weight_factor
                density_weight = 1.0
            else:
                keyword_weight = 0.0
                density_weight = 1.0
            
            # If no high-priority keywords found, set keyword weight to 0
            if not has_high_priority_keywords:
                keyword_weight = 0.0
                density_weight = 1.0
            
            print(f'\nSeparator at position {pos} (line {pos+4}):')
            print(f'  Header row {header_row_position} (line {header_row_position+4}): {header_row}')
            print(f'  Has high-priority keywords: {has_high_priority_keywords}')
            print(f'  Keyword score: {keyword_score:.4f}')
            print(f'  Keyword weight: {keyword_weight:.4f}')
            print(f'  Density weight: {density_weight:.4f}')
            
            # Simulate combined scoring (assuming density score of 0.9)
            density_score = 0.9
            combined_score = (keyword_score * keyword_weight + density_score * density_weight)
            total_weight = keyword_weight + density_weight
            if total_weight > 0:
                combined_score = combined_score / total_weight
            
            print(f'  Combined score (with density=0.9): {combined_score:.4f}')
    
    print("\n✅ Enhanced scoring test completed")

def test_expected_behavior():
    """Test the expected behavior based on specifications"""
    print("\n=== Testing Expected Behavior ===")
    
    # Test cases based on specifications
    test_cases = [
        (1, True, "Row 1 with keywords - should be strongly favored"),
        (3, True, "Row 3 with keywords - should be strongly favored"),
        (4, True, "Row 4 with keywords - should balance with density"),
        (6, True, "Row 6 with keywords - should balance with density"),
        (7, True, "Row 7 with keywords - should gradually de-emphasize"),
        (9, True, "Row 9 with keywords - should de-emphasize more"),
        (11, True, "Row 11 with keywords - should ignore keywords"),
        (4, False, "Row 4 without keywords - should rely on density"),
    ]
    
    for row_number, has_keywords, description in test_cases:
        print(f"\n{description}")
        
        # Calculate weights
        if row_number <= 3:
            keyword_weight = 1.2 if has_keywords else 0.0
            density_weight = 0.8 if has_keywords else 1.0
        elif row_number <= 6:
            keyword_weight = 1.0 if has_keywords else 0.0
            density_weight = 1.0
        elif row_number <= 10:
            weight_factor = (10 - row_number) / 4.0
            keyword_weight = weight_factor if has_keywords else 0.0
            density_weight = 1.0
        else:
            keyword_weight = 0.0
            density_weight = 1.0
        
        keyword_score = 1.0 if has_keywords else 0.0
        density_score = 0.9  # Assume good density
        
        combined_score = (keyword_score * keyword_weight + density_score * density_weight)
        total_weight = keyword_weight + density_weight
        if total_weight > 0:
            combined_score = combined_score / total_weight
        
        print(f"  Row {row_number}: keyword_weight={keyword_weight:.2f}, density_weight={density_weight:.2f}")
        print(f"  Combined score: {combined_score:.4f}")

if __name__ == "__main__":
    test_enhanced_scoring()
    test_expected_behavior()
    print("\n🎉 All enhanced algorithm tests completed!")
