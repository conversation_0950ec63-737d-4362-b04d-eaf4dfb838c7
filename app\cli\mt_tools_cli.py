import logging
import sys
from pathlib import Path
import os

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

# Import unified error handling
from app.exceptions import CLIException
from app.error_handling import log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for CLI operations
set_layer_context("cli")

from app.cli.engine import CLIEngine, BaseCommand
from app.utils.mt_tools import (
    parse_filename, group_files, sort_files, merge_files, pipe, clean_titles, group_merge
)
from app.utils.reform import MarkdownTableReformer
from app.utils.reheader import MarkdownTableHeaderAdjuster # Import the reheader utility

class GroupCommand(BaseCommand):
    def __init__(self):
        super().__init__(description="Group files based on predefined rules.")
        self.add_argument("directory", help="Path to the directory to scan.")
        self.add_argument("-d", "--delete", action="store_true", help="Delete original files after processing.")

    def execute(self, args):
        logger.debug(f"Executing group command on directory: {args.directory}")
        if not os.path.isdir(args.directory):
            logger.error(f"Error: Directory does not exist - {args.directory}")
            return

        file_list = os.listdir(args.directory)
        logger.debug(f"Found {len(file_list)} files in directory")
        groups = group_files(file_list)
        logger.debug(f"Grouping completed with {len(groups)} groups")

        for prefix, files in groups.items():
            logger.info(f"{prefix}:")
            for file in files:
                logger.info(f"  - {file}")
        logger.info(f"Group command completed successfully")

class MergeCommand(BaseCommand):
    def __init__(self):
        super().__init__(description="Merge two files.")
        self.add_argument("file1", help="Path to the first file.")
        self.add_argument("file2", help="Path to the second file.")
        self.add_argument("output", help="Path to the output merged file.")
        self.add_argument("-d", "--delete", action="store_true", help="Delete original files after processing.")

    def execute(self, args):
        logger.debug(f"Executing merge command: file1={args.file1}, file2={args.file2}, output={args.output}")
        merge_files(args.file1, args.file2, args.output, args.delete)
        logger.info(f"Files merged to: {args.output}")
        logger.debug("Merge command completed successfully")

class PipeCommand(BaseCommand):
    def __init__(self):
        super().__init__(description="Perform a pipe operation: group, merge, reheader, and reform files.")
        self.add_argument("directory", help="Path to the directory to scan.")
        self.add_argument("--no-reheader", action="store_true", help="Do not execute reheader.")
        self.add_argument("--no-reform", action="store_true", help="Do not execute reform.")

    def execute(self, args):
        logger.debug(f"Executing pipe command on directory: {args.directory}")
        logger.debug(f"Reheader disabled: {args.no_reheader}, Reform disabled: {args.no_reform}")
        pipe(args.directory, args.directory, True, args.no_reheader, args.no_reform)
        logger.debug("Pipe command completed successfully")

class CleanTitlesCommand(BaseCommand):
    def __init__(self):
        super().__init__(description="Clean titles in markdown files by removing 'Sheet1' headers.")
        self.add_argument("directory", help="Path to the directory containing markdown files.")

    def execute(self, args):
        logger.debug(f"Executing clean_titles command on directory: {args.directory}")
        clean_titles(args.directory)
        logger.debug("Clean titles command completed successfully")

class GroupMergeCommand(BaseCommand):
    def __init__(self):
        super().__init__(description="Group and merge files within a directory.")
        self.add_argument("directory", help="Path to the directory containing files to group and merge.")
        self.add_argument("output_dir", help="Path to the output directory for merged files.")
        self.add_argument("-d", "--delete", action="store_true", help="Delete original files after processing.")

    def execute(self, args):
        logger.debug(f"Executing group_merge command on directory: {args.directory}")
        file_list = os.listdir(args.directory)
        groups = group_files(file_list)
        group_merge(args.directory, args.output_dir, args.delete, groups)
        logger.debug("Group merge command completed successfully")

class ReformCommand(BaseCommand):
    def __init__(self):
        super().__init__(description="Markdown table reformer tool.")
        self.add_argument('input', help='Input file or directory path')
        self.add_argument('output', nargs='?', help='Output file or directory path (optional)')
        self.add_argument('-d', '--delete', action='store_true', help='Delete original files after processing')

    def execute(self, args):
        logger.debug(f"Executing reform command on input: {args.input}")
        reformer = MarkdownTableReformer()

        if os.path.isfile(args.input):
            reformer.process_file(args.input, args.output)
            if args.delete and args.output:
                os.remove(args.input)
                logger.info(f"Original file deleted: {args.input}")
        elif os.path.isdir(args.input):
            reformer.process_directory(args.input, args.output, args.delete)
        else:
            logger.error(f"Input path does not exist: {args.input}")
        logger.debug("Reform command completed successfully")

class ReheaderCommand(BaseCommand):
    def __init__(self):
        super().__init__(description="Adjust markdown table header separator rows.")
        self.add_argument('input', help='Input file or directory path')
        self.add_argument('output', nargs='?', help='Output file or directory path (optional)')
        self.add_argument('-d', '--delete', action='store_true', help='Delete original files after processing.')

    def execute(self, args):
        logger.debug(f"Executing reheader command on input: {args.input}")
        adjuster = MarkdownTableHeaderAdjuster()

        if os.path.isfile(args.input):
            adjuster.process_file(args.input, args.output, args.delete)
        elif os.path.isdir(args.input):
            adjuster.process_directory(args.input, args.output, args.delete)
        else:
            logger.error(f"Input path does not exist: {args.input}")
        logger.debug("Reheader command completed successfully")

@log_and_reraise(logger, "mt tools CLI main")
def main():
    """Main function for mt tools CLI with unified error handling"""
    set_operation_context("mt_tools_cli_main")

    try:
        engine = CLIEngine(prog="mt_tools")

        engine.register_command("group", GroupCommand)
        engine.register_command("merge", MergeCommand)
        engine.register_command("pipe", PipeCommand)
        engine.register_command("clean_titles", CleanTitlesCommand)
        engine.register_command("group_merge", GroupMergeCommand)
        engine.register_command("reform", ReformCommand)
        engine.register_command("reheader", ReheaderCommand) # Register the new reheader command

        engine.run()

    except CLIException as e:
        logger.error(f"CLI error: {e.message}")
        if e.details:
            logger.error(f"Details: {e.details}")
        if e.suggested_action:
            logger.info(f"Suggested action: {e.suggested_action}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()