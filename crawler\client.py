import json
import os
import typer
import httpx
import logging
import base64
from dotenv import load_dotenv
from typing import Optional
from pathlib import Path
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# Import unified error handling framework
from app.exceptions import LayerType, ErrorSeverity
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context
from .exceptions import (
    CrawlerServerException, NetworkException, TimeoutException,
    CrawlerLayerType
)

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for client operations
set_layer_context("client")

app = typer.Typer(help="QCC Crawler Client")

load_dotenv(override=True)
SERVER_URL = os.getenv("CRAWLER_SERVER_URL", "http://localhost:8000")

class CrawlerClient:
    def __init__(self, server_url: str = SERVER_URL):
        self.server_url = server_url
        self.client = httpx.AsyncClient(
            base_url=server_url,
            timeout=httpx.Timeout(
                float(os.getenv("CRAWLER_HTTPX_TIMEOUT", "60.0")),
                connect=float(os.getenv("CRAWLER_HTTPX_CONNECT_TIMEOUT", "10.0"))
            ),
            limits=httpx.Limits(max_connections=100)
        )

    @log_and_reraise(logger, "status check")
    async def get_status(self):
        """Get crawler status from server"""
        set_operation_context("status_check")

        with error_boundary("status API request", CrawlerLayerType.SERVER):
            try:
                response = await self.client.get("/status")
                response.raise_for_status()
                return response.json()
            except httpx.TimeoutException:
                raise TimeoutException(
                    message="Status check timeout",
                    operation="status_request",
                    details="Server did not respond within timeout period",
                    suggested_action="Check server status and network connectivity"
                )
            except httpx.ConnectError:
                raise NetworkException(
                    message="Connection failed",
                    details=f"Could not connect to server at {self.server_url}",
                    suggested_action="Check if the crawler server is running"
                )
            except httpx.HTTPStatusError as e:
                raise CrawlerServerException(
                    message="Status check failed",
                    error_code="CRAWLER_STATUS_001",
                    details=f"Server returned {e.response.status_code}: {e.response.text}",
                    context={'status_code': e.response.status_code, 'url': str(e.request.url)},
                    suggested_action="Check server logs for details"
                )

    @log_and_reraise(logger, "tab info retrieval")
    async def get_tab_info(self):
        """Get browser tab information from server"""
        set_operation_context("tab_info_retrieval")

        with error_boundary("tab info API request", CrawlerLayerType.SERVER):
            try:
                response = await self.client.get("/tabs")
                response.raise_for_status()
                return response.json()
            except httpx.TimeoutException:
                raise TimeoutException(
                    message="Tab info request timeout",
                    operation="tab_info_request",
                    details="Server did not respond within timeout period",
                    suggested_action="Check server status and network connectivity"
                )
            except httpx.ConnectError:
                raise NetworkException(
                    message="Connection failed during tab info request",
                    details=f"Could not connect to server at {self.server_url}",
                    suggested_action="Check if the crawler server is running"
                )
            except httpx.HTTPStatusError as e:
                raise CrawlerServerException(
                    message="Tab info request failed",
                    error_code="CRAWLER_TAB_INFO_001",
                    details=f"Server returned {e.response.status_code}: {e.response.text}",
                    context={'status_code': e.response.status_code, 'url': str(e.request.url)},
                    suggested_action="Check server logs for details"
                )

    @log_and_reraise(logger, "login operation")
    async def login(self, timeout: float = 120.0):
        """Login to QCC via server

        Args:
            timeout: Timeout in seconds for login operation (default: 120)
        """
        set_operation_context("login_operation")

        with error_boundary("login API request", CrawlerLayerType.SERVER):
            try:
                response = await self.client.post("/login", timeout=timeout)
                response.raise_for_status()
                result = response.json()
                return result
            except httpx.TimeoutException:
                raise TimeoutException(
                    message="Login request timeout",
                    operation="login_request",
                    timeout_duration=timeout,
                    details=f"Login did not complete within {timeout} seconds",
                    suggested_action="Login process may take time, try increasing timeout"
                )
            except httpx.ConnectError:
                raise NetworkException(
                    message="Connection failed during login",
                    details=f"Could not connect to server at {self.server_url}",
                    suggested_action="Check if the crawler server is running"
                )
            except httpx.HTTPStatusError as e:
                raise CrawlerServerException(
                    message="Login request failed",
                    error_code="CRAWLER_LOGIN_001",
                    details=f"Server returned {e.response.status_code}: {e.response.text[:100]}",
                    context={'status_code': e.response.status_code, 'timeout': timeout},
                    suggested_action="Check login credentials and server logs"
                )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type((httpx.ReadTimeout, httpx.ConnectError)),
        reraise=True
    )
    async def _retryable_request(self, method: str, url: str, **kwargs):
        try:
            response = await self.client.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except (httpx.ReadTimeout, httpx.ConnectError) as e:
            logger.warning(f"Retryable error occurred: {str(e)}")
            raise
        except Exception as e:
            # Non-retryable errors bypass the retry mechanism
            raise

    async def start_crawl(self, home_url: str, keyword: str):
        """Start crawling via server"""
        try:
            response = await self._retryable_request(
                "POST", "/start",
                json={
                    "home_url": home_url,
                    "keyword": keyword
                }
            )
            result = response.json()
            
            if result.get("status") == "qr_code_required":
                typer.echo("QR code login required")
                if result.get("qr_code_base64"):
                    typer.echo("Please scan the QR code to login within 30 seconds")
            elif result.get("status") == "completed":
                # Display companies found
                if result.get("companies"):
                    typer.echo("\nCompanies found:")
                    for company in result["companies"]:
                        typer.echo(f"- {company}")
                
                # Display page content if available
                if result.get("page_content"):
                    typer.echo("\nPage content:")
                    typer.echo(result["page_content"])
                
                # Display auto-reset information if applicable
                if result.get("auto_reset"):
                    typer.echo(f"\n🔄 Auto-reset performed: {result.get('reset_reason', 'Unknown reason')}")
                elif result.get("auto_reset_failed"):
                    typer.echo(f"\n❌ Auto-reset failed: {result.get('reset_error', 'Unknown error')}")
                
                # Still provide raw data for reference
                typer.echo("\nRaw data:")
                typer.echo(json.dumps(result.get("raw_data", {}), indent=2, ensure_ascii=False))
            return result
        except httpx.HTTPStatusError as e:
            logger.error(f"Crawling failed: {e.response.text}")
            raise 
        except Exception as e:
            logger.error(f"Error during crawling: {str(e)}")
            raise 

    async def reset(self):
        """Reset browser instance on server"""
        try:
            response = await self.client.post("/reset")
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Reset failed: {e.response.text}")
            raise typer.Exit(code=1)
        except Exception as e:
            logger.error(f"Error during reset: {str(e)}")
            raise typer.Exit(code=1)

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    async def get_screenshot(self, output_path: str = "screenshot.png"):
        """Get screenshot from server and save to file"""
        try:
            response = await self.client.get("/screenshot")
            response.raise_for_status()
            
            # Ensure directory exists
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            logger.debug(f"创建Screenshot输出目录: {output_path}")
            
            content = response.content
            # Save screenshot
            with open(output_path, "wb") as f:
                f.write(content)
            
            logger.info(f"Screenshot saved to {output_path}")
            return base64.b64encode(content).decode('utf-8')
        except httpx.HTTPStatusError as e:
            logger.error(f"Failed to get screenshot: {e.response.text}")
            raise typer.Exit(code=1)
        except Exception as e:
            logger.error(f"Error getting screenshot: {str(e)}")
            raise typer.Exit(code=1)

@app.command()
def status():
    """Check crawler status"""
    async def run():
        client = CrawlerClient()
        try:
            status = await client.get_status()
            typer.echo(f"Crawler status: {status}")
        finally:
            await client.close()
    
    import asyncio
    asyncio.run(run())

@app.command()
def tabs():
    """Check browser tab information"""
    async def run():
        client = CrawlerClient()
        try:
            tab_info = await client.get_tab_info()
            typer.echo("📊 Browser Tab Information:")
            typer.echo(f"  Current tabs: {tab_info.get('current_tabs', 0)}")
            typer.echo(f"  Max tabs: {tab_info.get('max_tabs', 0)}")
            typer.echo(f"  Usage: {tab_info.get('usage_percentage', 0)}%")
            typer.echo(f"  Browser connected: {tab_info.get('browser_connected', False)}")
            
            if tab_info.get('needs_reset', False):
                typer.echo("⚠️  Warning: Tab count exceeds limit, reset recommended")
            else:
                typer.echo("✅ Tab usage within normal limits")
        finally:
            await client.close()
    
    import asyncio
    asyncio.run(run())

@app.command()
def login(
    timeout: float = typer.Option(
        60.0,
        "--timeout",
        "-t",
        help="Timeout in seconds for login operation (default: 60)"
    )
):
    """Login to QCC
    
    Args:
        timeout: Timeout in seconds for login operation
    """
    async def run():
        client = CrawlerClient()
        try:
            result = await client.login(timeout=timeout)
            if result.get("status") == "logged_in":
                typer.echo("Successfully logged in")
        finally:
            await client.close()
    
    import asyncio
    asyncio.run(run())

@app.command()
def start(
    home_url: str = typer.Option(
        "https://www.qcc.com",
        "--url",
        "-u",
        help="Base URL to crawl and return to (default: https://www.qcc.com)"
    ),
    keyword: str = typer.Argument(..., help="Search keyword")
):
    """Start crawling on specified home URL (defaults to www.qcc.com)"""
    async def run():
        client = CrawlerClient()
        try:
            result = await client.start_crawl(home_url, keyword)
        except Exception:
            raise typer.Exit(1)
        finally:
            await client.close()
    
    import asyncio
    asyncio.run(run())

@app.command()
def reset():
    """Reset browser instance"""
    async def run():
        client = CrawlerClient()
        try:
            result = await client.reset()
            typer.echo(f"Browser reset: {result['message']}")
        finally:
            await client.close()
    
    import asyncio
    asyncio.run(run())

@app.command()
def screenshot(
    output_path: str = typer.Option(
        "screenshot.png",
        "--output",
        "-o",
        help="Path to save screenshot (default: screenshot.png)"
    )
):
    """Get screenshot of current browser page"""
    async def run():
        client = CrawlerClient()
        try:
            await client.get_screenshot(output_path)
            typer.echo(f"Screenshot saved to {output_path}")
        finally:
            await client.close()
    
    import asyncio
    asyncio.run(run())

if __name__ == "__main__":
    app()
