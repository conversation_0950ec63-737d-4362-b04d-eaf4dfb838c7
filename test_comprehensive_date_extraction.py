#!/usr/bin/env python3
"""
Test the comprehensive date extraction system
"""

import sys
import os
from pathlib import Path
import json
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def test_date_format_recognition():
    """Test recognition of all required date formats"""
    print("="*80)
    print("TESTING DATE FORMAT RECOGNITION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # Year-Month formats
        ("2024年9月", "Year-Month Chinese"),
        ("2024年09月", "Year-Month Chinese padded"),
        ("2024.8", "Year-Month dot-separated"),
        ("2024.03", "Year-Month dot-separated padded"),
        ("2024.12", "Year-Month dot-separated December"),
        ("2024-08", "Year-Month dash-separated"),
        ("2024-12", "Year-Month dash-separated December"),
        ("2024/09", "Year-Month slash-separated"),
        ("2024/12", "Year-Month slash-separated December"),
        
        # Quarterly formats
        ("2024年Q3", "Chinese year + quarter"),
        ("2024年Q1", "Chinese year + quarter Q1"),
        ("Q1", "Standalone quarter Q1"),
        ("Q2", "Standalone quarter Q2"),
        ("Q3", "Standalone quarter Q3"),
        ("Q4", "Standalone quarter Q4"),
        ("三季度", "Chinese quarter"),
        ("一季度", "Chinese quarter Q1"),
        ("二季度", "Chinese quarter Q2"),
        ("四季度", "Chinese quarter Q4"),
        ("2024年三季度", "Chinese year + Chinese quarter"),
        ("2024年一季度", "Chinese year + Chinese quarter Q1"),
        
        # Annual formats
        ("2024年", "Chinese year"),
        ("2023年", "Chinese year 2023"),
        ("2024", "Standalone year"),
        ("2023", "Standalone year 2023"),
        
        # Specific date formats
        ("2024-09-30", "Full date dash-separated"),
        ("2024.09.30", "Full date dot-separated"),
        ("2024/09/30", "Full date slash-separated"),
    ]
    
    print("Date Format Recognition Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (date_text, description) in enumerate(test_cases):
        dates = reformer.extract_date_period_info(date_text)
        valid_date = reformer.validate_and_prioritize_dates(dates)
        
        print(f"Test {i+1}: {description}")
        print(f"  Input: '{date_text}'")
        print(f"  Extracted: {dates}")
        print(f"  Valid/Prioritized: '{valid_date}'")
        
        # Check if the original format is preserved
        format_preserved = valid_date == date_text if valid_date else False
        print(f"  Format preserved: {'✅ YES' if format_preserved else '❌ NO'}")
        
        if not valid_date:
            all_passed = False
        
        print()
    
    return all_passed

def test_priority_hierarchy():
    """Test the priority hierarchy: header lines > filename"""
    print("="*80)
    print("TESTING PRIORITY HIERARCHY")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Test case 1: Date in header lines (should have priority)
    print("Test 1: Date in header lines (highest priority)")
    print("-" * 50)
    
    header_rows = [
        ['财务报表', '2024年Q3', ''],
        ['科目', '金额', '备注'],
    ]
    filename = "财务数据_2024年Q1.md"
    
    extracted_date = reformer.comprehensive_date_extraction(header_rows, filename)
    
    print(f"Header rows: {header_rows}")
    print(f"Filename: {filename}")
    print(f"Extracted date: '{extracted_date}'")
    print(f"Expected: '2024年Q3' (from header, not filename)")
    
    test1_passed = extracted_date == "2024年Q3"
    print(f"Result: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    
    print()
    
    # Test case 2: Date only in filename (fallback)
    print("Test 2: Date only in filename (fallback priority)")
    print("-" * 50)
    
    header_rows = [
        ['科目', '金额', '备注'],
        ['收入', '1000', ''],
    ]
    filename = "财务数据_2024年Q2.md"
    
    extracted_date = reformer.comprehensive_date_extraction(header_rows, filename)
    
    print(f"Header rows: {header_rows}")
    print(f"Filename: {filename}")
    print(f"Extracted date: '{extracted_date}'")
    print(f"Expected: '2024年Q2' (from filename)")
    
    test2_passed = extracted_date == "2024年Q2"
    print(f"Result: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    
    print()
    
    # Test case 3: No date found
    print("Test 3: No date information available")
    print("-" * 50)
    
    header_rows = [
        ['科目', '金额', '备注'],
        ['收入', '1000', ''],
    ]
    filename = "财务数据.md"
    
    extracted_date = reformer.comprehensive_date_extraction(header_rows, filename)
    
    print(f"Header rows: {header_rows}")
    print(f"Filename: {filename}")
    print(f"Extracted date: '{extracted_date}'")
    print(f"Expected: None")
    
    test3_passed = extracted_date is None
    print(f"Result: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    
    return test1_passed and test2_passed and test3_passed

def test_preprocessing_integration():
    """Test integration with preprocessing for whitespace cleaning"""
    print("\n" + "="*80)
    print("TESTING PREPROCESSING INTEGRATION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Test with whitespace in header rows
    header_rows = [
        ['  财务报表  ', ' 2024年 Q3 ', '   '],
        ['  科 目  ', ' 金 额 ', ' 备 注 '],
    ]
    filename = "data.md"
    
    print("Test: Whitespace cleaning in header rows")
    print("-" * 50)
    print(f"Original header rows: {header_rows}")
    
    # Test preprocessing
    cleaned_row = reformer.preprocess_row_for_text_analysis(header_rows[0])
    print(f"Cleaned first row: {cleaned_row}")
    
    # Test date extraction
    extracted_date = reformer.comprehensive_date_extraction(header_rows, filename)
    print(f"Extracted date: '{extracted_date}'")
    print(f"Expected: '2024年Q3' (spaces removed)")
    
    passed = extracted_date == "2024年Q3"
    print(f"Result: {'✅ PASS' if passed else '❌ FAIL'}")
    
    return passed

def test_validation_logic():
    """Test date validation logic"""
    print("\n" + "="*80)
    print("TESTING DATE VALIDATION LOGIC")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # Valid dates
        ("2024年Q3", True, "Valid quarter"),
        ("2024年9月", True, "Valid month"),
        ("2024-09-30", True, "Valid full date"),
        ("Q2", True, "Valid standalone quarter"),
        ("三季度", True, "Valid Chinese quarter"),
        
        # Invalid dates
        ("2019年Q3", False, "Year too early"),
        ("2031年Q3", False, "Year too late"),
        ("2024年13月", False, "Invalid month"),
        ("2024年Q5", False, "Invalid quarter"),
        ("Q0", False, "Invalid quarter number"),
    ]
    
    print("Date Validation Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (date_text, should_be_valid, description) in enumerate(test_cases):
        is_valid = reformer.validate_date_format(date_text)
        
        print(f"Test {i+1}: {description}")
        print(f"  Date: '{date_text}'")
        print(f"  Expected valid: {should_be_valid}")
        print(f"  Actually valid: {is_valid}")
        
        passed = is_valid == should_be_valid
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_real_file_integration():
    """Test with real balance sheet file"""
    print("="*80)
    print("TESTING REAL FILE INTEGRATION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Test with the balance sheet file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_资产负债表.md"
    
    print(f"Processing real file: {filename}")
    
    # Process the table
    result = reformer.process_table(content, filename)
    
    # Extract metadata to check date extraction
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        extracted_date = metadata.get('date', None)
        
        print(f"Extracted metadata: {metadata}")
        print(f"Extracted date: '{extracted_date}'")
        
        # Check if date was extracted (should be from filename)
        expected_date = "2024年Q4"
        
        print(f"Expected date: '{expected_date}'")
        
        passed = extracted_date == expected_date
        print(f"Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        return passed
    else:
        print("❌ No metadata found in result")
        return False

def main():
    """Run all date extraction tests"""
    print("Testing Comprehensive Date Extraction System")
    print("="*80)
    
    try:
        test1 = test_date_format_recognition()
        test2 = test_priority_hierarchy()
        test3 = test_preprocessing_integration()
        test4 = test_validation_logic()
        test5 = test_real_file_integration()
        
        print("="*80)
        print("FINAL RESULTS")
        print("="*80)
        print(f"Date Format Recognition: {'✅ PASS' if test1 else '❌ FAIL'}")
        print(f"Priority Hierarchy: {'✅ PASS' if test2 else '❌ FAIL'}")
        print(f"Preprocessing Integration: {'✅ PASS' if test3 else '❌ FAIL'}")
        print(f"Validation Logic: {'✅ PASS' if test4 else '❌ FAIL'}")
        print(f"Real File Integration: {'✅ PASS' if test5 else '❌ FAIL'}")
        
        all_passed = test1 and test2 and test3 and test4 and test5
        
        if all_passed:
            print("\n🎉 All comprehensive date extraction tests passed!")
            return 0
        else:
            print("\n❌ Some date extraction tests failed")
            return 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
