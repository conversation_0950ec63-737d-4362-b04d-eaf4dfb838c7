#!/usr/bin/env python3
"""
Debug the header detection algorithm to understand why it's failing
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reheader import MarkdownTableHeaderAdjuster

def debug_header_detection():
    """Debug the header detection step by step"""
    print("="*80)
    print("DEBUGGING HEADER DETECTION")
    print("="*80)
    
    # Read the original input file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Original content:")
    print("-" * 40)
    print(content)
    
    # Create adjuster with debug enabled
    adjuster = MarkdownTableHeaderAdjuster()
    adjuster.debug_output = True
    
    # Parse the table to see the structure
    print("\n" + "="*60)
    print("PARSING TABLE STRUCTURE")
    print("="*60)
    
    rows = adjuster.parse_table(content)
    print(f"Total rows parsed: {len(rows)}")
    
    for i, row in enumerate(rows):
        print(f"Row {i+1}: {row}")
    
    # Test header candidate detection
    print("\n" + "="*60)
    print("HEADER CANDIDATE ANALYSIS")
    print("="*60)
    
    candidates = adjuster.find_all_header_candidates(rows)
    print(f"Found {len(candidates)} header candidates:")
    
    for i, (row_idx, score) in enumerate(candidates):
        row = rows[row_idx]
        print(f"  {i+1}. Row {row_idx+1}: Score {score:.2f} - {row}")
        
        # Analyze this row in detail
        keyword_count = adjuster.count_header_keywords(row)
        has_strong = adjuster.has_strong_header_keywords(row)
        row_density = adjuster.calculate_row_density(row)
        string_density = adjuster.calculate_string_density(row)
        
        print(f"      Keywords: {keyword_count}, Strong: {has_strong}, "
              f"Density: {row_density:.2f}, String: {string_density:.2f}")
    
    # Test the separator detection
    print("\n" + "="*60)
    print("SEPARATOR DETECTION ANALYSIS")
    print("="*60)
    
    # Find existing separators
    separator_indices = []
    for i, row in enumerate(rows):
        if adjuster.is_separator_row(row):
            separator_indices.append(i)
            print(f"Found separator at row {i+1}: {row}")
    
    if not separator_indices:
        print("No existing separators found")
    
    # Test the full algorithm
    print("\n" + "="*60)
    print("FULL ALGORITHM TEST")
    print("="*60)
    
    result = adjuster.adjust_table(content)
    print("Final result:")
    print("-" * 40)
    print(result)

def main():
    """Main debug function"""
    try:
        debug_header_detection()
        return 0
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
