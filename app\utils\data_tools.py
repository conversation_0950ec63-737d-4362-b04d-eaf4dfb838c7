import os
from pathlib import Path
from dotenv import load_dotenv
import re
import pandas as pd
import argparse
import sqlite3
import sys
import logging
import json
from enum import Enum
from typing import List, Dict, Union, Optional
from datetime import datetime
from io import StringIO
import time

from app.cli.engine import BaseCommand, CLIEngine
from app.error_handling import (
    error_boundary,
    handle_layer_boundary,
    log_and_reraise,
    set_operation_context
)
from app.exceptions import (
    ValidationException,
    DataProcessingException,
    FileProcessingException,
    ConfigurationException,
    LayerType
)

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent.parent))
load_dotenv(override=True)

# Initialize logger
logger = logging.getLogger(__name__)

# from .common import GroupType, extract_column_names_from_expression, evaluate_mathematical_expression, output_format
from .common import GroupType, extract_column_names_from_expression, evaluate_mathematical_expression
from .common import format_output as format_output_func
from .csv_processor import load_csv_data, preprocess_dates

# ===== DATA PROCESSING FUNCTIONS =====

@handle_layer_boundary(LayerType.UTILITY, "process_data")
def process_data(
    df: pd.DataFrame,
    groupby_col: str = '日期',
    agg_columns: List[str] = None,
    group_type: GroupType = GroupType.COLUMN,
    order: str = 'asc'
) -> List[Dict[str, Union[float, str]]]:
    """Process and aggregate data by specified column or time period.
    
    Args:
        df: Input DataFrame
        groupby_col: Column to group by (default: '日期')
        agg_columns: List of columns to aggregate (default: all numeric cols)
        group_type: Grouping type (GroupType enum value)
        
    Returns:
        List of dictionaries containing aggregated data
    """
    set_operation_context("process_data")
    
    with error_boundary("validate_input", LayerType.UTILITY):
        if not isinstance(df, pd.DataFrame):
            raise ValidationException(
                "Invalid input: df must be a pandas DataFrame",
                {"input_type": type(df).__name__},
                "Ensure input is a valid pandas DataFrame"
            )
        
        if not groupby_col or not isinstance(groupby_col, str):
            raise ValidationException(
                "Invalid groupby_col parameter",
                {"groupby_col": groupby_col, "type": type(groupby_col).__name__},
                "Provide a valid column name string"
            )
        
        if groupby_col not in df.columns:
            raise ValidationException(
                f"Groupby column '{groupby_col}' not found in DataFrame",
                {"available_columns": list(df.columns), "requested_column": groupby_col},
                f"Choose from available columns: {list(df.columns)}"
            )
        
        if order.lower() not in ['asc', 'desc']:
            raise ValidationException(
                "Invalid order parameter",
                {"order": order, "valid_values": ['asc', 'desc']},
                "Use 'asc' for ascending or 'desc' for descending order"
            )
    
    with error_boundary("preprocess_dates", LayerType.UTILITY):
        if group_type != GroupType.COLUMN:
            df = preprocess_dates(df, groupby_col, group_type)
            group_col = '_group'
        else:
            group_col = groupby_col
    
    with error_boundary("aggregate_data", LayerType.UTILITY):
        try:
            if agg_columns:
                grouped = df.groupby(group_col)[agg_columns].sum()
            else:
                grouped = df.groupby(group_col).sum(numeric_only=True)
        except Exception as e:
            raise DataProcessingException(
                "Failed to aggregate data",
                {
                    "group_col": group_col,
                    "agg_columns": agg_columns,
                    "error": str(e)
                },
                "Check that aggregation columns exist and contain numeric data",
                original_exception=e
            )
    
    with error_boundary("format_results", LayerType.UTILITY):
        result_df = grouped.reset_index()
        
        if group_type == GroupType.COLUMN:
            try:
                date_parts = result_df[groupby_col].str.extract(r'(\d{4})年(\d{1,2})月')
                if date_parts.isnull().any().any():
                    raise DataProcessingException(
                        "Failed to extract date components for sorting",
                        {"groupby_col": groupby_col, "sample_values": result_df[groupby_col].tolist()[:5]},
                        "Ensure date format follows 'YYYY年M月' pattern"
                    )
                
                result_df['_sort_year'] = date_parts[0].astype(int)
                result_df['_sort_month'] = date_parts[1].astype(int)
                
                ascending = order.lower() == 'asc'
                result_df = result_df.sort_values(
                    by=['_sort_year', '_sort_month'],
                    ascending=ascending
                ).drop(columns=['_sort_year', '_sort_month'])
                
            except Exception as e:
                raise DataProcessingException(
                    "Failed to sort data chronologically",
                    {"error": str(e), "groupby_col": groupby_col},
                    "Check date format and ensure it can be parsed",
                    original_exception=e
                )
        
        result = result_df.to_dict('records')
        
        if group_type != GroupType.COLUMN:
            for item in result:
                item[groupby_col] = item.pop('_group')
        
        return result

@handle_layer_boundary(LayerType.UTILITY, "get_aggregated_data")
def get_aggregated_data(
    csv_data: str,
    groupby_col: str = '日期',
    columns: List[str] = None,
    group_type: GroupType = GroupType.COLUMN,
    order: str = 'asc'
) -> List[Dict[str, Union[str, float]]]:
    """Main function to load CSV and return aggregated data.
    
    Args:
        csv_data: String containing CSV data
        groupby_col: Column to group by (default: '日期')
        columns: List of columns to return (default: all)
        group_type: Grouping type (GroupType enum value)
        
    Returns:
        List of dictionaries containing aggregated data
    """
    set_operation_context("get_aggregated_data")
    
    with error_boundary("validate_parameters", LayerType.UTILITY):
        if not csv_data or not isinstance(csv_data, str):
            raise ValidationException(
                "Invalid CSV data provided",
                {"csv_data_type": type(csv_data).__name__, "csv_data_length": len(csv_data) if csv_data else 0},
                "Provide valid CSV data as a string"
            )
        
        if order.lower() not in ['asc', 'desc']:
            raise ValidationException(
                "Invalid order parameter",
                {"order": order, "valid_values": ['asc', 'desc']},
                "Use 'asc' for ascending or 'desc' for descending order"
            )
    
    with error_boundary("load_csv_data", LayerType.UTILITY):
        df = load_csv_data(csv_data)
    
    with error_boundary("process_data", LayerType.UTILITY):
        result = process_data(df, groupby_col, columns, group_type, order)
    
    return result

# ===== COMMAND LINE INTERFACE FUNCTIONS =====


class ProcessCSVCommand(BaseCommand):
    """Process CSV data with grouping and aggregation"""
    def __init__(self):
        super().__init__(description="Process CSV data with grouping and column selection")
        self.add_argument('csv_file', help='Path to CSV file')
        self.add_argument('--groupby', '-g', default='日期', help='Column to group by (default: 日期)')
        self.add_argument('--columns', '-c', help='Columns to output (comma-separated)')
        self.add_argument('--group-type', '-t', default='Column',
                       help='Grouping type: Column (default), Year/y, HalfYear/hy, or Quarter/q')
        self.add_argument('--order', default='asc',
                       help='Sort order when group-type=Column: asc (default) or desc')
        self.add_argument('--output-format', '-f', default='table',
                       help='Output format: table (default), csv, -csv, or json')

    def execute(self, args):
        with open(args.csv_file, 'r', encoding='utf-8') as f:
            csv_data = f.read()
        
        columns = args.columns.split(',') if args.columns else None
        
        # Normalize group type
        group_type = args.group_type.lower()
        type_map = {
            'y': 'Year',
            'hy': 'HalfYear',
            'q': 'Quarter',
            'year': 'Year',
            'halfyear': 'HalfYear',
            'quarter': 'Quarter',
            'column': 'Column'
        }
        normalized_type = type_map.get(group_type, 'Column')
        
        result = get_aggregated_data(
            csv_data,
            groupby_col=args.groupby,
            columns=columns,
            group_type=GroupType(normalized_type),
            order=args.order
        )
        
        # Validate output format
        output_format = args.output_format.lower()
        if output_format not in ['table', 'csv', '-csv', 'json']:
            raise ValueError("output_format must be one of: table, csv, -csv, or json")
        
        print(format_output_func(pd.DataFrame(result), output_format))

# ===== SQLITE DATABASE FUNCTIONS =====

@handle_layer_boundary(LayerType.UTILITY, "load_to_sqlite")
def load_to_sqlite(data_file: str, table_name: str = 'biz_table', date_col: str = '日期',
                   date_format: str = None, keep: bool = False) -> str:
    """Load CSV data into SQLite database
    
    Args:
        data_file: Path to CSV file to load
        table_name: Table name (default: biz_table)
        date_col: Date column name (default: 日期)
        date_format: Date format string (e.g., "%Y-%m-%d")
        keep: Keep existing table (default: drop and recreate)
        
    Returns:
        Success message string
    """
    set_operation_context("load_to_sqlite")
    
    with error_boundary("validate_parameters", LayerType.UTILITY):
        if not data_file or not isinstance(data_file, str):
            raise ValidationException(
                "Invalid data file path provided",
                {"data_file": data_file, "type": type(data_file).__name__},
                "Provide a valid file path string"
            )
        
        if not os.path.exists(data_file):
            raise FileProcessingException(
                "Data file not found",
                {"data_file": data_file},
                "Ensure the file exists at the specified path"
            )
        
        if not table_name or not isinstance(table_name, str):
            raise ValidationException(
                "Invalid table name provided",
                {"table_name": table_name, "type": type(table_name).__name__},
                "Provide a valid table name string"
            )
        
        if not date_col or not isinstance(date_col, str):
            raise ValidationException(
                "Invalid date column name provided",
                {"date_col": date_col, "type": type(date_col).__name__},
                "Provide a valid column name string"
            )
    
    with error_boundary("load_csv_data", LayerType.UTILITY):
        try:
            df = pd.read_csv(data_file)
            if df.empty:
                raise DataProcessingException(
                    "CSV file is empty",
                    {"data_file": data_file},
                    "Ensure the CSV file contains data"
                )
        except pd.errors.EmptyDataError:
            raise DataProcessingException(
                "CSV file is empty or invalid",
                {"data_file": data_file},
                "Check that the CSV file contains valid data"
            )
        except pd.errors.ParserError as e:
            raise DataProcessingException(
                "Failed to parse CSV file",
                {"data_file": data_file, "error": str(e)},
                "Check CSV format and ensure it's properly formatted"
            )
        except Exception as e:
            raise FileProcessingException(
                "Failed to read CSV file",
                {"data_file": data_file, "error": str(e)},
                "Check file permissions and ensure it's a valid CSV file",
                original_exception=e
            )
    
    with error_boundary("process_date_column", LayerType.UTILITY):
        if date_col in df.columns:
            # Rename date column to standard name
            df.rename(columns={date_col: '日期'}, inplace=True)
            
            # Parse and format dates
            if date_format:
                try:
                    df['日期'] = pd.to_datetime(df['日期'], format=date_format).dt.strftime('%Y-%m-%d')
                except ValueError as e:
                    raise DataProcessingException(
                        "Invalid date format specified",
                        {"date_format": date_format, "sample_dates": df['日期'].tolist()[:5]},
                        f"Ensure the date format matches the data. Try formats like: %Y-%m-%d, %Y年%m月"
                    )
            else:
                # Try to infer format if not specified - attempt multiple formats
                formats = [
                    '%Y年%m月',        # e.g., 2023年9月
                    '%Y-%m',            # e.g., 2023-09
                    '%Y年%m月%d日',     # e.g., 2023年9月30日
                    '%Y-%m-%d',         # e.g., 2023-09-30
                    '%Y-%-m-%-d'        # e.g., 2023-9-30 (with single-digit month/day)
                ]
                
                parsed = None
                parse_errors = []
                for fmt in formats:
                    try:
                        temp = pd.to_datetime(df['日期'], format=fmt, errors='coerce')
                        # Check if all dates were parsed successfully
                        if not temp.isnull().any():
                            parsed = temp
                            break
                    except Exception as e:
                        parse_errors.append(f"{fmt}: {str(e)}")
                        continue
                
                # If no format worked, fall back to default parsing
                if parsed is None:
                    try:
                        parsed = pd.to_datetime(df['日期'], errors='coerce')
                        if parsed.isnull().all():
                            raise DataProcessingException(
                                "Failed to parse any dates",
                                {"sample_dates": df['日期'].tolist()[:5], "attempted_formats": formats},
                                "Check date format in the data"
                            )
                    except Exception as e:
                        raise DataProcessingException(
                            "Failed to parse dates",
                            {"error": str(e), "sample_dates": df['日期'].tolist()[:5]},
                            "Ensure dates are in a recognizable format",
                            original_exception=e
                        )
                
                df['日期'] = parsed.dt.strftime('%Y-%m-%d')
    
    with error_boundary("connect_to_database", LayerType.UTILITY):
        try:
            # Ensure data directory exists
            os.makedirs('data', exist_ok=True)
            conn = sqlite3.connect('data/report.db')
            cursor = conn.cursor()
        except sqlite3.Error as e:
            raise DataProcessingException(
                "Failed to connect to SQLite database",
                {"database_path": 'data/report.db', "error": str(e)},
                "Check database permissions and ensure the data directory exists"
            )
    
    try:
        with error_boundary("create_table", LayerType.UTILITY):
            # Conditionally drop the table
            if not keep:
                try:
                    cursor.execute(f"DROP TABLE IF EXISTS {table_name};")
                except sqlite3.Error as e:
                    raise DataProcessingException(
                        "Failed to drop existing table",
                        {"table_name": table_name, "error": str(e)},
                        "Check table name for SQL injection attempts"
                    )
            
            # Create table with dynamic columns
            columns = []
            for col in df.columns:
                # Infer column type
                if col == '日期':
                    col_type = "DATE"
                else:
                    # Attempt to convert to numeric to check if it's a numeric column
                    try:
                        numeric_col = pd.to_numeric(df[col], errors='coerce')
                        if not numeric_col.isna().all():  # Check if at least one value is numeric
                            col_type = "REAL"
                        else:
                            col_type = "TEXT"
                    except Exception:
                        col_type = "TEXT"
                columns.append(f'"{col}" {col_type}')
            
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {', '.join(columns)}
            );
            """
            try:
                cursor.execute(create_table_sql)
            except sqlite3.Error as e:
                raise DataProcessingException(
                    "Failed to create table",
                    {"table_name": table_name, "error": str(e)},
                    "Check table name and column definitions"
                )
        
        with error_boundary("insert_data", LayerType.UTILITY):
            # Insert data
            placeholders = ', '.join(['?'] * len(df.columns))
            insert_sql = f"INSERT INTO {table_name} VALUES ({placeholders})"
            inserted_count = 0
            
            for _, row in df.iterrows():
                try:
                    # Convert all values to string for SQLite insertion
                    cursor.execute(insert_sql, tuple(row.astype(str)))
                    inserted_count += 1
                except sqlite3.Error as e:
                    raise DataProcessingException(
                        "Failed to insert data row",
                        {"table_name": table_name, "row_data": row.to_dict(), "error": str(e)},
                        "Check data types and ensure they match table schema"
                    )
        
        with error_boundary("commit_transaction", LayerType.UTILITY):
            try:
                conn.commit()
            except sqlite3.Error as e:
                raise DataProcessingException(
                    "Failed to commit transaction",
                    {"error": str(e)},
                    "Check database state and retry operation"
                )
    
    finally:
        try:
            conn.close()
        except sqlite3.Error:
            # Log but don't raise - connection close errors are not critical
            logger.warning("Failed to close database connection properly")
    
    return f"Successfully loaded {inserted_count} rows into {table_name}"

class LoadToSQLiteCommand(BaseCommand):
    """Load CSV data into SQLite database"""
    def __init__(self):
        super().__init__(description="Load CSV data into SQLite database")
        self.add_argument('data_file', help='Path to CSV file to load')
        self.add_argument('-t', '--table-name', default='biz_table',
                        help='Table name (default: biz_table)')
        self.add_argument('-d', '--date-col', default='日期',
                        help='Date column name (default: 日期)')
        self.add_argument('-F', '--date-format',
                        help='Date format string (e.g., "%%Y-%%m-%%d")')
        self.add_argument('-k', '--keep', action='store_true',
                        help='Keep existing table (default: drop and recreate)')

    def execute(self, args):
        result = load_to_sqlite(
            data_file=args.data_file,
            table_name=args.table_name,
            date_col=args.date_col,
            date_format=args.date_format,
            keep=args.keep
        )
        print(result)


@handle_layer_boundary(LayerType.UTILITY, "query_data_from_sqlite")
def query_data_from_sqlite(
    table: str = 'biz_table',
    columns: str = None,
    filters: List[str] = None,
    group_by: str = None,
    strict: bool = False
) -> pd.DataFrame:
    """
    Query data from SQLite database and return DataFrame for further processing.
    
    Args:
        table: Table name (default: 'biz_table')
        columns: Comma-separated list of columns to select or mathematical expressions
        filters: List of filter conditions in "COLUMN=VALUE" format
        group_by: Grouping period (month/quarter/half_year/year)
        strict: When True, output only specified columns (default: False)
        
    Returns:
        pandas DataFrame with query results
    """
    set_operation_context("query_data_from_sqlite")
    
    with error_boundary("validate_parameters", LayerType.UTILITY):
        if not table or not isinstance(table, str):
            raise ValidationException(
                "Invalid table parameter",
                {"table": table, "type": type(table).__name__},
                "Provide a valid table name string"
            )
        
        if group_by and group_by.lower() not in ['month', 'quarter', 'half_year', 'year']:
            raise ValidationException(
                "Invalid group_by parameter",
                {"group_by": group_by, "valid_values": ['month', 'quarter', 'half_year', 'year']},
                "Use 'month', 'quarter', 'half_year', or 'year' for grouping"
            )
    
    # Connect to SQLite database
    with error_boundary("database_connection", LayerType.UTILITY):
        try:
            conn = sqlite3.connect('data/report.db')
        except Exception as e:
            raise DataProcessingException(
                "Failed to connect to SQLite database",
                {"database_path": "data/report.db", "error": str(e)},
                "Ensure the database file exists and is accessible",
                original_exception=e
            )
    
    # Parse columns for expressions and build column selection
    select_columns = "*"
    calculated_columns = []  # Store info about calculated columns
    
    if columns:
        column_parts = []
        for col_expr in columns.split(','):
            col_expr = col_expr.strip()
            
            # Check if this is an expression with "as" keyword
            if " as " in col_expr:
                parts = col_expr.split(" as ", 1)
                if len(parts) == 2:
                    expr, alias = parts[0].strip(), parts[1].strip()
                    
                    # Check if it's a mathematical expression (contains operators)
                    if any(op in expr for op in ['+', '-', '*', '/', '(', ')']):
                        # Extract all column names from the expression
                        referenced_columns = extract_column_names_from_expression(expr)
                        
                        # Add all referenced columns to selection
                        for col in referenced_columns:
                            if f'"{col}"' not in column_parts:
                                column_parts.append(f'"{col}"')
                        
                        # Store calculation info for later evaluation
                        calculated_columns.append({
                            'alias': alias,
                            'expression': expr,
                            'referenced_columns': referenced_columns
                        })
                        continue
                    
                    # For simple column expressions, treat as regular column with alias
                    column_parts.append(f'"{expr}" AS "{alias}"')
                else:
                    # Invalid "as" format, treat as regular column
                    column_parts.append(f'"{col_expr}"')
            else:
                # Regular column
                column_parts.append(f'"{col_expr}"')
        
        select_columns = ", ".join(column_parts)
    
    # Build WHERE clause for filtering
    conditions = []
    params = []
    
    # Handle generic filters
    if filters:
        for f in filters:
            parts = f.split('=', 1)
            if len(parts) < 2:
                raise ValueError(f"Invalid filter: {f}. Must be COLUMN=VALUE")
            col, value = parts
            
            # Handle comma-separated list
            if ',' in value:
                values = value.split(',')
                placeholders = ', '.join(['?'] * len(values))
                conditions.append(f'"{col}" IN ({placeholders})')
                params.extend(values)
                continue
            
            # Handle slice notation [start:step]
            if value.startswith('[') and value.endswith(']'):
                slice_parts = value[1:-1].split(':')
                if len(slice_parts) != 2:
                    raise ValueError(f"Invalid slice format: {value}. Use [start:step]")
                try:
                    start = int(slice_parts[0])
                    step = int(slice_parts[1])
                except ValueError:
                    raise ValueError(f"Slice values must be integers: {value}")
                
                # Use modulus operation to select every nth row
                conditions.append(f"(ROWID - ?) % ? = 0 AND ROWID >= ?")
                params.extend([start, step, start])
                continue
            
            # Handle date quarter format
            if col == '日期' and re.match(r'\d{4}年\d季度', value):
                match = re.match(r'(\d{4})年(\d)季度', value)
                if not match:
                    raise ValueError(f"Invalid quarter format: {value}. Use 'YYYY年Q季度'")
                year = int(match.group(1))
                quarter = int(match.group(2))
                if quarter < 1 or quarter > 4:
                    raise ValueError(f"Quarter must be between 1-4, got {quarter}")
                quarter_starts = [f"{year}-01-01", f"{year}-04-01", f"{year}-07-01", f"{year}-10-01"]
                quarter_ends = [f"{year}-03-31", f"{year}-06-30", f"{year}-09-30", f"{year}-12-31"]
                conditions.append("日期 >= ?")
                conditions.append("日期 <= ?")
                params.append(quarter_starts[quarter-1])
                params.append(quarter_ends[quarter-1])
            else:
                # For non-date columns
                conditions.append(f'"{col}" = ?')
                params.append(value)
    
    where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
    
    # Handle grouping if specified
    group_by_clause = ""
    if group_by:
        # Define grouping expressions using SQLite date functions
        group_exprs = {
            'month': "strftime('%Y年%m月', 日期)",
            'quarter': "strftime('%Y年', 日期) || (CASE " 
                       "WHEN CAST(strftime('%m', 日期) AS INTEGER) BETWEEN 1 AND 3 THEN '1季度' "
                       "WHEN CAST(strftime('%m', 日期) AS INTEGER) BETWEEN 4 AND 6 THEN '2季度' "
                       "WHEN CAST(strftime('%m', 日期) AS INTEGER) BETWEEN 7 AND 9 THEN '3季度' "
                       "ELSE '4季度' END)",
            'half_year': "strftime('%Y年', 日期) || (CASE " 
                         "WHEN CAST(strftime('%m', 日期) AS INTEGER) <= 6 THEN '上半年' ELSE '下半年' END)",
            'year': "strftime('%Y年', 日期)"
        }
        
        group_expr = group_exprs.get(group_by)
        if not group_expr:
            raise ValueError(f"Invalid group-by value: {group_by}")
        
        # Get columns to aggregate - need to handle calculated columns differently
        agg_columns = []
        if columns:
            # Use specified columns, but handle calculated columns
            for col_expr in columns.split(','):
                col_expr = col_expr.strip()
                
                # Check if this is a calculated column expression
                if " as " in col_expr:
                    parts = col_expr.split(" as ", 1)
                    if len(parts) == 2:
                        expr, alias = parts[0].strip(), parts[1].strip()
                        
                        # Check if it's a mathematical expression (contains operators)
                        if any(op in expr for op in ['+', '-', '*', '/', '(', ')']):
                            # For mathematical expressions, aggregate all referenced columns
                            referenced_columns = extract_column_names_from_expression(expr)
                            for col in referenced_columns:
                                agg_columns.append(f'SUM(CAST("{col}" AS REAL)) AS "{col}"')
                            continue
                        elif '/' in expr:
                            # Handle simple division expressions
                            expr_parts = expr.split('/')
                            if len(expr_parts) == 2:
                                num_col, den_col = expr_parts[0].strip(), expr_parts[1].strip()
                                # Add both columns for aggregation
                                agg_columns.extend([
                                    f'SUM(CAST("{num_col}" AS REAL)) AS "{num_col}"',
                                    f'SUM(CAST("{den_col}" AS REAL)) AS "{den_col}"'
                                ])
                                continue
                        
                        # For other expressions, treat as regular column with alias
                        agg_columns.append(f'SUM(CAST("{expr}" AS REAL)) AS "{alias}"')
                    else:
                        # Invalid "as" format, treat as regular column
                        agg_columns.append(f'SUM(CAST("{col_expr}" AS REAL)) AS "{col_expr}"')
                else:
                    # Regular column
                    agg_columns.append(f'SUM(CAST("{col_expr}" AS REAL)) AS "{col_expr}"')
        else:
            # Aggregate all numeric columns except date
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table})")
            columns_info = cursor.fetchall()
            for col_info in columns_info:
                col_name = col_info[1]
                if col_name != '日期' and col_info[2].upper() in ['REAL', 'INTEGER', 'NUMERIC']:
                    agg_columns.append(f'SUM(CAST("{col_name}" AS REAL)) AS "{col_name}"')
        
        if not agg_columns:
            raise ValueError("No numeric columns found to aggregate")
            
        select_columns = f"{group_expr} AS 日期, " + ", ".join(agg_columns)
        group_by_clause = f"GROUP BY {group_expr}"
    
    # Build and execute query
    query = f"SELECT {select_columns} FROM {table} {where_clause} {group_by_clause}"
    logger.debug(f"Executing SQL query: {query}")
    if params:
        logger.debug(f"Query parameters: {params}")
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    logger.info(f"Query returned {len(df)} rows.")
    
    # Calculate derived columns if any
    for calc_col in calculated_columns:
        alias = calc_col['alias']
        expression = calc_col['expression']
        referenced_columns = calc_col['referenced_columns']
        
        # Evaluate the mathematical expression for each row
        calculated_values = []
        for _, row in df.iterrows():
            # Create a dictionary of column values for this row
            data_dict = {}
            for col in referenced_columns:
                if col in df.columns:
                    data_dict[col] = pd.to_numeric(row[col], errors='coerce')
            
            # Evaluate the expression
            result = evaluate_mathematical_expression(expression, data_dict)
            calculated_values.append(result)
        
        # Add the calculated column to the DataFrame
        df[alias] = calculated_values
        
        # Remove the original columns if they were only needed for calculation
        if strict:
            for col in referenced_columns:
                if col in df.columns and col != alias:
                    df.drop(columns=[col], inplace=True)
    
    # Apply strict mode filtering if requested
    if strict and columns:
        # Keep only the specified columns (including calculated ones)
        keep_columns = []
        for col_expr in columns.split(','):
            col_expr = col_expr.strip()
            if " as " in col_expr:
                parts = col_expr.split(" as ", 1)
                if len(parts) == 2:
                    alias = parts[1].strip()
                    keep_columns.append(alias)
            else:
                keep_columns.append(col_expr)
        
        # Filter DataFrame to keep only specified columns
        available_columns = [col for col in keep_columns if col in df.columns]
        df = df[available_columns]
    
    return df

@handle_layer_boundary(LayerType.UTILITY, "query_sqlite")
def query_sqlite(
    table: str = 'biz_table',
    columns: str = None,
    filters: List[str] = None,
    group_by: str = None,
    output_format: str = 'table',
    strict: bool = False
) -> str:
    """Query data from SQLite database with column selection and time filtering.
    
    Args:
        table: Table name (default: 'biz_table')
        columns: Comma-separated list of columns to select or mathematical expressions like "col1+col2 as sum"
        filters: List of filter conditions in "COLUMN=VALUE" format
        group_by: Grouping period (month/quarter/half_year/year)
        output_format: Output format ('table', 'csv', '-csv', 'json')
        strict: When True, output only specified columns (default: False)
        
    Returns:
        Formatted query results as string
    """
    set_operation_context("query_sqlite")
    
    with error_boundary("validate_parameters", LayerType.UTILITY):
        if not table or not isinstance(table, str):
            raise ValidationException(
                "Invalid table name provided",
                {"table": table, "type": type(table).__name__},
                "Provide a valid table name string"
            )
        
        if output_format not in ['table', 'csv', '-csv', 'json']:
            raise ValidationException(
                "Invalid output format",
                {"output_format": output_format, "valid_values": ['table', 'csv', '-csv', 'json']},
                "Use one of: table, csv, -csv, or json"
            )
    
    with error_boundary("execute_query", LayerType.UTILITY):
        try:
            # Use the reusable query method
            df = query_data_from_sqlite(
                table=table,
                columns=columns,
                filters=filters,
                group_by=group_by,
                strict=strict
            )
            
            if df.empty and strict:
                raise DataProcessingException(
                    "Query returned no results",
                    {"table": table, "filters": filters},
                    "Check query parameters and ensure data exists"
                )
            
        except Exception as e:
            if isinstance(e, (ValidationException, DataProcessingException)):
                raise
            raise DataProcessingException(
                "Failed to query data from SQLite",
                {"table": table, "error": str(e)},
                "Check table name and query parameters",
                original_exception=e
            )
    
    with error_boundary("output_format", LayerType.UTILITY):
        try:
            # Format numeric columns to 2 decimal places
            for col in df.select_dtypes(include=['number']).columns:
                df[col] = df[col].round(2)
            
            # Format and return output
            return format_output_func(df, output_format)
            
        except Exception as e:
            raise DataProcessingException(
                "Failed to format query results",
                {"output_format": output_format, "error": str(e)},
                "Check output format and data types",
                original_exception=e
            )

class QuerySQLiteCommand(BaseCommand):
    """Query data from SQLite database with column selection and time filtering"""
    def __init__(self):
        super().__init__(description="Query data from SQLite database")
        self.add_argument('--table', '-t', default='biz_table', help='Table name (default: biz_table)')
        self.add_argument('--columns', '-c', help='Comma-separated list of columns to select or expressions like "col1+col2 as sum"')
        self.add_argument('--filter', '-F', action='append',
                           help='Filter condition in the form "COLUMN=VALUE". For date column, VALUE can be a quarter string (e.g., "2024年4季度")')
        self.add_argument('--output-format', '-f', default='table',
                           choices=['table', 'csv', '-csv', 'json'], help='Output format: table (default), csv, -csv, or json')
        self.add_argument('--group-by', '-g', default=None,
                           choices=['month', 'quarter', 'half_year', 'year'],
                           help="Group by time period: month, quarter, half_year, year")
        self.add_argument('--strict', action='store_true',
                           help='When True, output only specified columns (default: False)')

    def execute(self, args):
        result = query_sqlite(
            table=args.table,
            columns=args.columns,
            filters=args.filter,
            group_by=args.group_by,
            output_format=args.output_format,
            strict=args.strict
        )
        print(result)

def calculate_yoy(table: str = 'biz_table', column: str = None, group_by: str = 'year',
                 output_format: str = 'table', filters: List[str] = None, strict: bool = False) -> str:
    """Calculate year-over-year growth for a specified column or expression and time period
    
    Args:
        table: Table name (default: biz_table)
        column: Column name or expression for YoY calculation
        group_by: Group by time period (year/quarter/half_year/month)
        output_format: Output format (table/csv/json)
        filters: List of filter conditions applied to result data
        strict: When True, output only specified columns (default: False)
        
    Returns:
        Formatted output string
    """
    try:
        # Check if column is an expression (contains " as ")
        if " as " in column:
            # Parse expression (e.g., "xxx/yyy as zzz")
            parts = column.split(" as ", 1)
            if len(parts) != 2:
                raise ValueError("Invalid expression format. Use 'xxx/yyy as zzz'")
            expr, alias = parts[0].strip(), parts[1].strip()
            
            # Split the expression by '/' to get numerator and denominator
            expr_parts = expr.split('/')
            if len(expr_parts) != 2:
                raise ValueError("Invalid expression. Must be in the form 'xxx/yyy'")
            num_col, den_col = expr_parts[0].strip(), expr_parts[1].strip()
            
            # Query both columns (without filters)
            df = query_data_from_sqlite(table=table, columns=f"{num_col},{den_col}", group_by=group_by)
            
            # Calculate the new column
            df[alias] = df[num_col] / df[den_col]
            target_col = alias
        else:
            # Simple column case
            df = query_data_from_sqlite(table=table, columns=column, group_by=group_by)
            target_col = column
        
        if df.empty:
            return "No data found"
            
        # Ensure we have at least 2 periods of data
        if len(df) < 2:
            return "Insufficient data for YoY calculation (need at least 2 periods)"
            
        # Parse period labels to extract year and period number
        df['year'] = df['日期'].str.extract(r'(\d{4})').astype(int)
        
        if group_by == 'quarter':
            df['period'] = df['日期'].str.extract(r'(\d)季度').astype(int)
        elif group_by == 'half_year':
            df['period'] = df['日期'].apply(lambda x: 1 if '上' in x else 2)
        elif group_by == 'month':
            df['period'] = df['日期'].str.extract(r'(\d{1,2})月').astype(int)
        else:  # year
            df['period'] = 1
            
        # Sort by year and period
        df = df.sort_values(['year', 'period'])
        
        # Calculate YoY growth for the same period
        df['prev_value'] = df.groupby('period')[target_col].shift(1)
        df['yoy_growth'] = (df[target_col] - df['prev_value']) / df['prev_value'] * 100
        
        # Prepare result
        result_df = df[['日期', target_col, 'yoy_growth']].copy()
        
        # Rename columns based on whether it's an expression or simple column
        if " as " in column:
            # For expressions like "xxx/yyy as zzz", use the alias
            parts = column.split(" as ", 1)
            if len(parts) == 2:
                alias = parts[1].strip()
                result_df.rename(columns={target_col: alias, 'yoy_growth': '同比'}, inplace=True)
        else:
            # For simple columns, use the original column name
            result_df.rename(columns={target_col: column, 'yoy_growth': '同比'}, inplace=True)
        
        # Apply filters to the result data if provided
        if filters:
            for filter_cond in filters:
                col, value = filter_cond.split('=', 1)  # Split into column and value parts
                col = col.strip()
                
                # Handle exact match filtering
                if col in result_df.columns:
                    # For date columns, use exact match
                    result_df = result_df[result_df[col].astype(str) == value]
        
        # Apply strict mode filtering if enabled
        if strict and " as " in column:
            # For expressions like "xxx/yyy as zzz", only keep the 同比 column with alias name
            parts = column.split(" as ", 1)
            if len(parts) == 2:
                alias = parts[1].strip()
                result_df = result_df[['同比']].copy()
                result_df.rename(columns={'同比': alias}, inplace=True)
        elif strict:
            logger.debug(f"YoY result DataFrame before formatting:\n{result_df.to_string()}")
            # For simple columns, keep 同比 with original column name
            result_df = result_df[['同比']].copy()
            result_df.rename(columns={'同比': column}, inplace=True)
        
        # Format numeric columns to 2 decimal places
        for col in result_df.select_dtypes(include=['number']).columns:
            result_df[col] = result_df[col].round(2)
        
        # Format output
        return format_output_func(result_df, output_format)
        
    except Exception as e:
        raise DataProcessingException(
            f"Error calculating YoY: {str(e)}",
            context={"operation": "calculate_yoy", "parameters": {"table": table, "column": column, "group_by": group_by}}
        )

class CalculateYoYCommand(BaseCommand):
    """Calculate year-over-year growth for a specified column or expression and time period"""
    def __init__(self):
        super().__init__(description="Calculate year-over-year growth for a specified column or expression and time period")
        self.add_argument('--table', '-t', default='biz_table', help='Table name (default: biz_table)')
        self.add_argument('--column', '-c', required=True, help='Column name or expression for YoY calculation (e.g., "revenue/customers as arpc")')
        self.add_argument('--group-by', '-g', default='year',
                          choices=['year', 'quarter', 'half_year', 'month'],
                          help="Group by time period: year (default), quarter, half_year, month")
        self.add_argument('--output-format', '-f', default='table',
                          choices=['table', 'csv', '-csv', 'json'],
                          help='Output format: table (default), csv, -csv, or json')
        self.add_argument('--filter', '-F', action='append',
                          help='Filter condition applied to the result data in the form "COLUMN=VALUE"')
        self.add_argument('--strict', action='store_true',
                          help='When True, output only specified columns (default: False)')

    def execute(self, args):
        result = calculate_yoy(
            table=args.table,
            column=args.column,
            group_by=args.group_by,
            output_format=args.output_format,
            filters=args.filter,
            strict=args.strict
        )
        print(result)

@handle_layer_boundary(LayerType.UTILITY, "extract_braces_content")
def extract_braces_content(file_path: str) -> List[str]:
    """Extract content between curly braces from a file.
    
    Args:
        file_path: Path to the file to read
        
    Returns:
        List of strings found between curly braces
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all content between curly braces
    pattern = re.compile(r'\{([^{}]*)\}')
    matches = pattern.findall(content)
    
    return matches

class ExtractBracesCommand(BaseCommand):
    """Extract content between curly braces from a file"""
    def __init__(self):
        super().__init__(description="Extract content between curly braces from a file")
        self.add_argument('file_path', help='Path to the file to process')

    def execute(self, args):
        try:
            results = extract_braces_content(args.file_path)
            for i, content in enumerate(results, 1):
                print(f"{i}. {content}")
        except Exception as e:
            logger.error(f"Error processing file: {str(e)}")
            sys.exit(1)

@handle_layer_boundary(LayerType.UTILITY, "calculate_mom")
def calculate_mom(table: str = 'biz_table', column: str = None, group_by: str = 'month',
                 output_format: str = 'table', filters: List[str] = None, strict: bool = False) -> str:
    """Calculate month-over-month (or period-over-period) growth for a specified column or expression and time period
    
    Args:
        table: Table name (default: biz_table)
        column: Column name or expression for MoM calculation
        group_by: Group by time period (month/quarter/half_year/year)
        output_format: Output format (table/csv/json)
        filters: List of filter conditions applied to result data
        strict: When True, output only specified columns (default: False)
        
    Returns:
        Formatted output string
    """
    set_operation_context("calculate_mom")
    
    with error_boundary("validate_parameters", LayerType.UTILITY):
        if not table or not isinstance(table, str):
            raise ValidationException(
                "Invalid table parameter",
                {"table": table, "type": type(table).__name__},
                "Provide a valid table name string"
            )
        
        if not column or not isinstance(column, str):
            raise ValidationException(
                "Invalid column parameter",
                {"column": column, "type": type(column).__name__},
                "Provide a valid column name or expression string"
            )
        
        if group_by not in ['month', 'quarter', 'half_year', 'year']:
            raise ValidationException(
                "Invalid group_by parameter",
                {"group_by": group_by, "valid_values": ['month', 'quarter', 'half_year', 'year']},
                "Use one of: month, quarter, half_year, year"
            )
        
        if output_format not in ['table', 'csv', '-csv', 'json']:
            raise ValidationException(
                "Invalid output format",
                {"output_format": output_format, "valid_values": ['table', 'csv', '-csv', 'json']},
                "Use one of: table, csv, -csv, or json"
            )
    
    with error_boundary("execute_calculation", LayerType.UTILITY):
        # Check if column is an expression (contains " as ")
        if " as " in column:
            # Parse expression (e.g., "xxx/yyy as zzz")
            parts = column.split(" as ", 1)
            if len(parts) != 2:
                raise ValueError("Invalid expression format. Use 'xxx/yyy as zzz'")
            expr, alias = parts[0].strip(), parts[1].strip()
            
            # Split the expression by '/' to get numerator and denominator
            expr_parts = expr.split('/')
            if len(expr_parts) != 2:
                raise ValueError("Invalid expression. Must be in the form 'xxx/yyy'")
            num_col, den_col = expr_parts[0].strip(), expr_parts[1].strip()
            
            # Query both columns (without filters)
            df = query_data_from_sqlite(table=table, columns=f"{num_col},{den_col}", group_by=group_by)
            
            # Calculate the new column
            df[alias] = df[num_col] / df[den_col]
            target_col = alias
        else:
            # Simple column case
            df = query_data_from_sqlite(table=table, columns=column, group_by=group_by)
            target_col = column
        
        if df.empty:
            return "No data found"
            
        if len(df) < 2:
            return "Insufficient data for MoM calculation (need at least 2 periods)"
            
        # Parse the period string to extract year and period number
        if group_by == 'month':
            # Example: "2023年3月"
            df['year'] = df['日期'].str.extract(r'(\d{4})').astype(int)
            df['period_num'] = df['日期'].str.extract(r'(\d{1,2})月').astype(int)
        elif group_by == 'quarter':
            # Example: "2023年1季度"
            df['year'] = df['日期'].str.extract(r'(\d{4})').astype(int)
            df['period_num'] = df['日期'].str.extract(r'(\d)季度').astype(int)
        elif group_by == 'half_year':
            # Example: "2023年上半年"
            df['year'] = df['日期'].str.extract(r'(\d{4})').astype(int)
            # Map to 1 for 上半年, 2 for 下半年
            df['period_num'] = df['日期'].apply(lambda x: 1 if '上' in x else 2)
        else:  # year
            # Example: "2023年"
            df['year'] = df['日期'].str.extract(r'(\d{4})').astype(int)
            df['period_num'] = 1   # All years have period 1? But we don't have multiple periods per year in year grouping.
            
        # Now sort by year and period_num
        df = df.sort_values(['year', 'period_num'])
        
        # Calculate the previous value by shifting the column of interest
        df['prev_value'] = df[target_col].shift(1)
        df['mom_growth'] = (df[target_col] - df['prev_value']) / df['prev_value'] * 100
        
        # Prepare result
        result_df = df[['日期', target_col, 'mom_growth']].copy()
        
        # Apply filters to the result data if provided (before renaming columns)
        if filters:
            for filter_cond in filters:
                col, value = filter_cond.split('=', 1)  # Split into column and value parts
                col = col.strip()
                
                # Handle exact match filtering
                if col in result_df.columns:
                    # For date columns, use exact match
                    result_df = result_df[result_df[col].astype(str) == value]
        
        # Rename columns based on whether it's an expression or simple column
        if " as " in column:
            # For expressions like "xxx/yyy as zzz", use the alias
            parts = column.split(" as ", 1)
            if len(parts) == 2:
                alias = parts[1].strip()
                result_df.rename(columns={target_col: alias, 'mom_growth': '环比'}, inplace=True)
        else:
            # For simple columns, use the original column name
            result_df.rename(columns={target_col: column, 'mom_growth': '环比'}, inplace=True)
        
        # Apply strict mode filtering if enabled
        if strict and " as " in column:
            # For expressions like "xxx/yyy as zzz", only keep the 环比 column with alias name
            parts = column.split(" as ", 1)
            if len(parts) == 2:
                alias = parts[1].strip()
                result_df = result_df[['环比']].copy()
                result_df.rename(columns={'环比': alias}, inplace=True)
        elif strict:
            # For simple columns, keep 环比 with original column name
            result_df = result_df[['环比']].copy()
            result_df.rename(columns={'环比': column}, inplace=True)
        
        # Format numeric columns to 2 decimal places
        for col in result_df.select_dtypes(include=['number']).columns:
            result_df[col] = result_df[col].round(2)
        
        # Format output
        return format_output_func(result_df, output_format)

class CalculateMoMCommand(BaseCommand):
    """Calculate month-over-month (or period-over-period) growth for a specified column or expression and time period"""
    def __init__(self):
        super().__init__(description="Calculate month-over-month (or period-over-period) growth for a specified column or expression and time period")
        self.add_argument('--table', '-t', default='biz_table', help='Table name (default: biz_table)')
        self.add_argument('--column', '-c', required=True, help='Column name or expression for MoM calculation (e.g., "re revenue/customers as arpc")')
        self.add_argument('--group-by', '-g', default='month',
                          choices=['year', 'quarter', 'half_year', 'month'],
                          help="Group by time period: month (default), quarter, half_year, year")
        self.add_argument('--output-format', '-f', default='table',
                          choices=['table', 'csv', '-csv', 'json'],
                          help='Output format: table (default), csv, -csv, or json')
        self.add_argument('--filter', '-F', action='append',
                          help='Filter condition applied to the result data in the form "COLUMN=VALUE"')
        self.add_argument('--strict', action='store_true',
                          help='When True, output only specified columns (default: False)')

    def execute(self, args):
        result = calculate_mom(
            table=args.table,
            column=args.column,
            group_by=args.group_by,
            output_format=args.output_format,
            filters=args.filter,
            strict=args.strict
        )
        print(result)

# ===== TEMPLATE PROCESSING FUNCTIONS =====

@handle_layer_boundary(LayerType.UTILITY, "store_answer")
def store_answer(answer_content: str, output_file: str, output_format: str = 'md', colsqu: str = None) -> Optional[str]:
    """Process and store formatted content from answer text.
    
    Args:
        answer_content: String containing answer text with format tags
        output_file: Base filename to use for output files
        output_format: Desired output format ('md', 'csv', 'json', etc.)
        colsqu: Comma-separated column names (e.g., "日期,再贷余额") to transform data
               by taking unique values from first column and appending to second column name
        
    Returns:
        Formatted content string or None if no formatted content found
    """
    pattern = r'//(\w+)//(.*?)//\1//'
    matches = list(re.finditer(pattern, answer_content, re.DOTALL))
    
    json_data = []
    csv_file = None
    
    # First pass: collect all data
    for i, match in enumerate(matches):
        format_type = match.group(1)
        extracted_content = match.group(2).strip()
        
        if format_type in ['csv', '-csv']:
            # Use the same output file for all CSV content
            csv_file = output_file.replace('.json', '.csv')
            mode = 'a' if i > 0 else 'w'  # Append if not first match
            with open(csv_file, mode, encoding='utf-8', newline='') as csv_f:
                # Split content by space and write each part on new line
                parts = extracted_content.strip().split()
                if mode == 'w':
                    csv_f.write(os.linesep.join(parts))
                else:
                    csv_f.write(os.linesep*2 + os.linesep.join(parts))
            logger.debug(f"Appended {format_type} data to {csv_file}")
                    
        elif format_type == 'json':
            # Use the same output file for all JSON content
            json_file = output_file
            mode = 'a' if i > 0 else 'w'  # Append if not first match
            with open(json_file, mode, encoding='utf-8') as json_f:
                if mode == 'a':  # Add separator between JSON objects
                    json_f.write(os.linesep)
                json_f.write(extracted_content)
            logger.debug(f"Appended JSON data to {json_file}")
            json_data.append(extracted_content)
    
    # Second pass: format collected data
    results = []
    fix_cols = []
    
    if csv_file:
        # Process CSV data
        with open(csv_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Split content by newlines to handle multiple tables
        tables = content.split(os.linesep*2)
        if len(tables) == 1:
            tables = content.split("\n\n")
        logger.debug(f"Load csv tables:\n{tables}")
        table_count = 0
        
        for table_content in tables:
            if not table_content.strip():
                continue
                
            table_count += 1
            
            try:
                df = load_csv_data(table_content)
                
                # Apply column transformation if colsqu parameter is provided
                if colsqu:
                    cols = colsqu.split(',')
                    if len(cols) >= 2:
                        first_col = cols[0].strip()
                        second_col = cols[1].strip()
                        
                        # Check if both columns exist in the DataFrame
                        if first_col in df.columns and second_col in df.columns:
                            # Get unique value from this table (assuming one unique value per table)
                            if len(df[first_col].unique()) == 1:
                                unique_value = df[first_col].iloc[0]
                                
                                # Create a new column name with the unique value appended
                                new_col_name = f"{second_col}({unique_value})"
                                fix_cols.append(new_col_name)
                                
                                # Rename the second column to include the unique value
                                df = df.rename(columns={second_col: new_col_name})
                                
                                # Create a new DataFrame with the columns we want to keep (excluding first_col)
                                keep_cols = [col for col in df.columns if col != first_col]
                                result_df = df[keep_cols].copy()
                                
                                # Add a header for this table with the unique value
                                # table_header = f"### Table {table_count} - {unique_value}"
                                table_header = ""
                                
                                # Format as requested
                                if output_format == 'md':
                                    table_formatted = format_output_func(result_df, 'table')
                                    results.append(f"{table_header}\n\n{table_formatted}")
                                elif output_format == 'csv':
                                    results.append(result_df.to_csv(index=False))
                                else:
                                    results.append(f"{table_header}\n\n{format_output_func(result_df, output_format)}")
                                
                                logger.info(f"Processed table with unique value {unique_value}")
                                continue
                
                # If we didn't process with colsqu or it didn't apply, use the original DataFrame
                if output_format == 'md':
                    table_formatted = format_output_func(df, 'table')
                    results.append(f"{table_formatted}")
                elif output_format == 'csv':
                    results.append(df.to_csv(index=False))
                else:
                    results.append(f"{format_output_func(df, output_format)}")
                    
            except Exception as e:
                logger.error(f"Error processing table data: {str(e)}")
                results.append(f"### Table {table_count} (Error)\n\n{table_content}")
    
    # Process JSON data
    for content in json_data:
        if output_format == 'md':
            try:
                data = json.loads(content)
                if isinstance(data, list) and len(data) > 0:
                    df = pd.DataFrame(data)
                    results.append(format_output_func(df, 'table'))
                else:
                    results.append(content)
            except Exception as e:
                logger.error(f"Error converting JSON to markdown: {str(e)}")
                results.append(content)
        else:
            results.append(content)

    # Merge tables if colsqu parameter is provided and output format is markdown or csv
    if colsqu and results and output_format in ['csv', '-csv', 'md']:
        # Extract column name from colsqu (first part before comma)
        parts = colsqu.split(',')
        if len(parts) < 1:
            logger.warning("colsqu parameter does not contain enough parts for merge column")
        else:
            results = merge_tables(results, fix_cols, output_format)
    
    final_result = answer_content
    if results:
        # Use single newline to avoid empty lines in CSV
        final_result =  os.linesep.join(results) if len(results) > 1 else results[0]
    store_file = f"{Path(output_file).stem}.{output_format}"
    if csv_file:
        # Write directly without extra normalization to preserve original line structure
        with open(store_file, 'w', encoding='utf-8', newline='') as csv_f:
            csv_f.write(final_result)
    else:
        logger.debug(f"No formatted content tags found in answer: {final_result}")
        # Return raw answer if no formatted content found
    
    return final_result

# ===== TABLE MERGING FUNCTION =====

@handle_layer_boundary(LayerType.UTILITY, "merge_tables")
def merge_tables(results: list, cols: list, output_format: str) -> list:
    """
    Merge tables in results by combining columns and deduplicating rows.
    
    Args:
        results: List of result strings (some may be tables)
        column_name: Column to use for deduplication
        output_format: Output format ('md' or 'csv')
        
    Returns:
        New list with tables merged into a single table
    """
    tables = []
    other_results = []
    
    # # Parse tables based on output format
    # for res in results:
    #     try:
    #         if output_format == 'md' and is_markdown_table(res):
    #             df = markdown_to_dataframe(res)
    #         elif output_format == 'csv':
    #             df = load_csv_data(res)
                            
    #         tables.append(df)
                
    #     except Exception as e:
    #         logger.error(f"Error parsing table: {str(e)}")
    #         other_results.append(res)

    for res in results:
        try:
            df = None  # 初始化避免未赋值访问
            if output_format == 'md' and is_markdown_table(res):
                df = markdown_to_dataframe(res)
            elif output_format == 'csv':
                df = load_csv_data(res)

            if df is not None and not df.empty:
                tables.append(df)
            else:
                raise ValueError("Parsed table is empty or invalid")
            
        except Exception as e:
            logger.error(f"Error parsing table: {str(e)}")
            other_results.append(res)

    
    if len(tables) < 2:
        # Not enough tables to merge
        return results
    
    try:
        # Use columns not in 'cols' as the merge key
        key_columns = [col for col in tables[0].columns if col not in cols]
        logger.warning(f"Table key columns {key_columns} of {cols}")
        
        # If no key columns found, return original results
        if not key_columns:
            logger.warning("No key columns found for merging. Returning original results.")
            return results
        
        # Start with the first table
        merged_df = tables[0]
        
        # Process each subsequent table
        for df in tables[1:]:            
            # Merge on the key columns
            merged_df = pd.merge(merged_df, df, on=key_columns, how='outer')
        
        # Fill missing values
        merged_df = merged_df.fillna("-")
        
        # If cols are provided, calculate sum for each row and sort
        if cols:
            # Convert '-' to 0 and ensure numeric values
            for col in cols:
                if col in merged_df.columns:
                    merged_df[col] = pd.to_numeric(merged_df[col].replace('-', '0'), errors='coerce').fillna(0)
            
            # Calculate row sum for specified columns
            valid_cols = [col for col in cols if col in merged_df.columns]
            if valid_cols:
                merged_df['_row_sum'] = merged_df[valid_cols].sum(axis=1)
                
                # Separate special rows (其他, 合计) and normal rows
                special_mask = merged_df['资方名称'].isin(['其他', '合计'])
                special_df = merged_df[special_mask]
                normal_df = merged_df[~special_mask]
                
                # Sort normal rows by sum descending
                normal_df = normal_df.sort_values(by='_row_sum', ascending=False)
                
                # Sort special rows by sum ascending (smallest first) but keep 合计 last
                other_df = special_df[special_df['资方名称'] == '其他']
                other_df = other_df.sort_values(by='_row_sum', ascending=True)
                total_df = special_df[special_df['资方名称'] == '合计']
                
                # Combine: normal rows + sorted other rows + total row
                merged_df = pd.concat([normal_df, other_df, total_df])
                
                merged_df = merged_df.drop(columns=['_row_sum'])
        
        # Format according to output format
        if output_format == 'md':
            merged_table = merged_df.to_markdown(index=False)
        elif output_format == 'csv':
            merged_table = merged_df.to_csv(index=False)
        else:
            # Fallback to markdown
            merged_table = merged_df.to_markdown(index=False)
        
        return other_results + [merged_table]
    except Exception as e:
        logger.error(f"Error merging tables: {str(e)}")
        return results

# ===== MARKDOWN TABLE HELPERS =====

@log_and_reraise(logger, "is_markdown_table")
def is_markdown_table(s: str) -> bool:
    """Check if a string is a markdown table."""
    lines = s.strip().splitlines()
    if len(lines) < 3:
        return False
    if '|' not in lines[0] or '|' not in lines[1] or '---' not in lines[1]:
        return False
    return True

@log_and_reraise(logger, "markdown_to_dataframe")
def markdown_to_dataframe(s: str) -> pd.DataFrame:
    """Convert a markdown table string to a pandas DataFrame."""
    lines = s.strip().splitlines()
    headers = [h.strip() for h in lines[0].split('|') if h.strip()]
    data = []
    for line in lines[2:]:
        if not line.strip() or '|' not in line:
            continue
        values = [v.strip() for v in line.split('|') if v.strip()]
        if len(values) == len(headers):
            data.append(values)
    return pd.DataFrame(data, columns=headers)

@handle_layer_boundary(LayerType.UTILITY, "ragflow_for_chat")
def ragflow_for_chat(template: str,
                    ragflow_base_url: Optional[str] = None,
                    ragflow_api_key: Optional[str] = None,
                    assistant_id: Optional[str] = None,
                    reporter_id: Optional[str] = None) -> str:
    """Use RAGFlow to generate a chat response for the given template.
    
    Args:
        template: The template content to send to RAGFlow
        ragflow_base_url: Base URL for RAGFlow API (optional, can use env var)
        ragflow_api_key: API key for RAGFlow (optional, can use env var)
        assistant_id: Assistant ID for RAGFlow chat (optional, can use env var)
        reporter_id: Optional reporter ID to lookup specific assistant ID
        
    Returns:
        The response answer from RAGFlow or the original template if error occurs
    """
    from ragflow_api.client import RAGFlowClient
    import os
    import json
    
    # Extract tag name and question from the content
    # pattern = r'<\|chat,([^|]+)\|>(.*?)<\|chat,\1\|>' '<\|chat(@([^,]+))?(,context)?\|>(.*?)<\|chat(@\2)?(,context)?\|>'
    # match = re.search(pattern, template, re.DOTALL)
    # if not match:
    #     logger.error("Could not find the required tags in the provided template")
    #     return None
        
    # tag_name = match.group(1).strip().strip('/-')
    # output_file = f'chat_{tag_name}.json'

    # Get credentials from environment if not provided
    ragflow_base_url = ragflow_base_url or os.getenv('RAGFLOW_BASE_URL')
    ragflow_api_key = ragflow_api_key or os.getenv('RAGFLOW_API_KEY')
    
    # Get assistant ID - use reporter_id if provided, else fallback to default
    if not assistant_id:
        if reporter_id:
            assistant_id = os.getenv(f'RAGFLOW_{reporter_id.upper()}_ID') or os.getenv('RAGFLOW_REPORTER_ID')
        else:
            assistant_id = os.getenv('RAGFLOW_REPORTER_ID')
    
    if not ragflow_base_url or not ragflow_api_key or not assistant_id:
        logger.error("Missing required RAGFlow credentials in environment variables.")
        return template
    
    # Initialize client
    client = RAGFlowClient(base_url=ragflow_base_url, api_key=ragflow_api_key)
    
    # Create a chat session
    session = client.create_chat_session(
        chat_id=assistant_id,
        name=f"Template Chat {datetime.now().strftime('%Y%m%d%H%M%S')}"
    )
    
    # Log the request
    logger.info(f"Sending chat request to RAGFlow: {template}")
    
    try:                            
        # Call RAGFlow API with error handling and delay
        time.sleep(int(os.getenv('QUESTION_LEADIND_TIME', '5')))
        response = client.converse_with_chat(
            chat_id=assistant_id,
            question=template,
            session_id=session['id'],
            stream=False,
            user_id=None
        )
                
        # # Save the response to JSON file
        # with open(output_file, 'w', encoding='utf-8') as f:
        #     json.dump(response, f, ensure_ascii=False, indent=2)
        # logger.info(f"Successfully saved response to {output_file}")
        
        # Log the response
        logger.debug(f"Full response: {json.dumps(response, ensure_ascii=False, indent=2)}")
        
        # Log the full response
        logger.info(f"Received chat response from RAGFlow: {json.dumps(response, ensure_ascii=False)}")
        
        # Extract answer from response
        if 'answer' in response:
            return response['answer']
        else:
            logger.warning("No answer in RAGFlow response, using template as fallback")
            return template
            
    except Exception as e:
        logger.error(f"Error during RAGFlow chat: {str(e)}")
        logger.warning("Using template as fallback due to connection error")
        return template

@handle_layer_boundary(LayerType.UTILITY, "ragflow_for_data")
def ragflow_for_data(content: str, dryrun: bool,
                            ragflow_base_url: Optional[str] = None,
                            ragflow_api_key: Optional[str] = None,
                            assistant_id: Optional[str] = None,
                            output_format: Optional[str] = 'md') -> Optional[str]:
    """Use RAGFlow to generate data from provided content.
    
    Args:
        content: String containing the template content with data tags
        ragflow_base_url: Base URL for RAGFlow API (optional, can use env var)
        ragflow_api_key: API key for RAGFlow (optional, can use env var)
        assistant_id: Assistant ID for RAGFlow chat (optional, can use env var)
        output_format: Output format ('md' for markdown table, 'csv', 'json', etc.)
        
    Returns:
        Formatted data content (CSV as markdown table, JSON, etc.), or None if error occurred
    """
    from ragflow_api.client import RAGFlowClient
    import os
    import re
    import json

    # Extract tag name and question from the content
    # content: <|data,corp.date:cap|>some text<\|data,corp.date:cap|>
    pattern = r'<\|data,([^|]+)\|>(.*?)<\|data,\1\|>'
    match = re.search(pattern, content, re.DOTALL)
    if not match:
        logger.error("Could not find the required tags in the provided content")
        return None
    
    colsqu = None
    tag_name = match.group(1).strip().strip('/-')
    if '.' in tag_name:
        parts = tag_name.split('.', 1)  # Split into 2 parts max
        tag_name = parts[0]
        if len(parts) > 1:
            colsqu = parts[1].replace(':', ',')
        logger.debug(f"Tag name: {tag_name}, colsqu: {colsqu}")
    question = match.group(2).strip()
    output_file = f'data_{tag_name}.json'

    # Get credentials from environment if not provided
    ragflow_base_url = ragflow_base_url or os.getenv('RAGFLOW_BASE_URL')
    ragflow_api_key = ragflow_api_key or os.getenv('RAGFLOW_API_KEY')
    assistant_id = assistant_id or os.getenv('RAGFLOW_ASSISTANT_ID')

    if not ragflow_base_url or not ragflow_api_key or not assistant_id:
        logger.error("Missing required RAGFlow credentials. Please provide them as arguments or set environment variables.")
        return None

    if dryrun:
        logger.info(f"dryrun mode")
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
            response = json.loads(content)
        return store_answer(response['answer'], output_file, output_format, colsqu)
    # Initialize client
    client = RAGFlowClient(base_url=ragflow_base_url, api_key=ragflow_api_key)

    try:
        session = client.create_chat_session(
            chat_id=assistant_id,
            name=f"Report {tag_name}"
        )
        
        # Call RAGFlow API with delay
        time.sleep(int(os.getenv('QUESTION_LEADIND_TIME', '5')))
        response = client.converse_with_chat(
            chat_id=assistant_id,
            question=question,
            session_id=session['id'],
            stream=False
        )
                
        # Save the response to JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(response, f, ensure_ascii=False, indent=2)
        logger.info(f"Successfully saved response to {output_file}")
        
        # Log the response
        logger.debug(f"Full response: {json.dumps(response, ensure_ascii=False, indent=2)}")
        
        # Extract and process data if present in answer
        if 'answer' in response:
            return store_answer(response['answer'], output_file, output_format, colsqu)
        else:
            logger.debug("Response does not contain 'answer' field")
            return None
            
    except Exception as e:
        logger.error(f"Error during RAGFlow chat: {str(e)}")
        return None

@handle_layer_boundary(LayerType.UTILITY, "interpret_command")
def interpret_command(command_str: str) -> Optional[str]:
    """Interpret and execute a single command in format 'func:param1:param2:...' with pipeline support
    
    Args:
        command_str: Command string in format 'func:param1:param2:...|pipeline_op1|pipeline_op2'
        
    Returns:
        Result string from the executed function after pipeline operations, or None if no result
    """
    # Split by | to separate main command from pipeline operations
    pipeline_parts = command_str.split('|')
    main_command = pipeline_parts[0]
    pipeline_ops = pipeline_parts[1:] if len(pipeline_parts) > 1 else []
    
    parts = main_command.split(':')
    if not parts:
        return None
        
    command = parts[0]
    params = parts[1:] if len(parts) > 1 else []
    
    # Execute the main command first
    result = None
    
    # Map commands to functions and their parameter requirements
    if command == 'top':
        # Expected format: top:table:column:limit:strict
        table = params[0] if len(params) > 0 else 'biz_table'
        column = params[1] if len(params) > 1 else None
        limit = params[2] if len(params) > 2 else '10'
        strict = params[3].lower() in ['true', 'strict'] if len(params) > 3 else False
        
        if not column:
            logger.error("Missing required column parameter for top command")
            return None
            
        try:
            # Parse limit (with optional + or - sign)
            limit_num = int(limit)
            ascending = limit_num < 0
            limit_abs = abs(limit_num)
            
            # Query data with all columns
            df = query_data_from_sqlite(
                table=table,
                columns=None,
                group_by=None,
                strict=False
            )
            
            if df.empty:
                return "No data found"
                
            # Sort by specified column
            # Ensure numeric sorting by converting column to numeric if needed
            if not pd.api.types.is_numeric_dtype(df[column]):
                try:
                    df[column] = pd.to_numeric(df[column])
                except ValueError:
                    logger.warning(f"Could not convert column '{column}' to numeric for sorting")
            
            df = df.sort_values(column, ascending=ascending)
            logger.debug(f"Sorted by column '{column}' in {'ascending' if ascending else 'descending'} order")
            
            # Get top N rows
            if limit_abs > 0:
                df = df.head(limit_abs)
            
            # Apply strict mode if requested
            if strict:
                df = df[[column]]
                
            # Format as CSV
            return df.to_csv(index=False)
            
        except Exception as e:
            logger.error(f"Error executing top command: {str(e)}")
            return f"Error executing top command: {str(e)}"
            
    elif command == 'load_to_sqlite':
        # Expected format: load_to_sqlite:data_file:table_name:date_col:date_format
        data_file = params[0] if len(params) > 0 else None
        table_name = params[1] if len(params) > 1 else 'biz_table'
        date_col = params[2] if len(params) > 2 else '日期'
        date_format = params[3] if len(params) > 3 else None
        
        result = load_to_sqlite(
            data_file=data_file,
            table_name=table_name,
            date_col=date_col,
            date_format=date_format,
            keep=False
        )
        
    elif command == 'query':
        # Expected format: query:table:columns:group_by:filters:output_format
        table = params[0] if len(params) > 0 else 'biz_table'
        columns = params[1] if len(params) > 1 else None
        group_by = params[2] if len(params) > 2 else None
        filters = params[3].split(',') if len(params) > 3 and params[3] else None
        output_format = params[4] if len(params) > 4 else '-csv'
        
        result = query_sqlite(
            table=table,
            columns=columns,
            group_by=group_by,
            filters=filters,
            output_format=output_format,
            strict=True
        )
        
    elif command == 'yoy':
        # Expected format: yoy:table:column:group_by:filters:output_format
        table = params[0] if len(params) > 0 else 'biz_table'
        column = params[1] if len(params) > 1 else None
        group_by = params[2] if len(params) > 2 else 'quarter'
        filters = params[3].split(',') if len(params) > 3 and params[3] else None
        output_format = params[4] if len(params) > 4 else '-csv'
        
        result = calculate_yoy(
            table=table,
            column=column,
            group_by=group_by,
            output_format=output_format,
            filters=filters,
            strict=True
        )
        
    elif command == 'mom':
        # Expected format: mom:table:column:group_by:output_format:filters
        table = params[0] if len(params) > 0 else 'biz_table'
        column = params[1] if len(params) > 1 else None
        group_by = params[2] if len(params) > 2 else 'quarter'
        filters = params[3].split(',') if len(params) > 3 and params[3] else None
        output_format = params[4] if len(params) > 4 else '-csv'
        
        result = calculate_mom(
            table=table,
            column=column,
            group_by=group_by,
            output_format=output_format,
            filters=filters,
            strict=True
        )
        
    else:
        logger.warning(f"Unknown command: {command}")
        return None
    
    # Apply pipeline operations if any
    if result is not None and pipeline_ops:
        for op in pipeline_ops:
            op = op.strip()
            if op == '-':
                # Replace output with empty string
                result = ''
            elif op == 'int':
                # Convert output to integer
                try:
                    # Extract numeric value from result string
                    if isinstance(result, str):
                        # For CSV format results, extract just the numeric value
                        lines = result.strip().split('\n')
                        if lines:
                            # Take the last non-empty line which should contain the data
                            data_line = lines[-1].strip()
                            if data_line and not data_line.startswith('Error'):
                                # Try to convert to float first, then to int
                                numeric_value = float(data_line)
                                result = str(int(numeric_value))
                            else:
                                # Fallback: try to extract number from the whole result
                                import re
                                numbers = re.findall(r'-?\d+\.?\d*', result)
                                if numbers:
                                    result = str(int(float(numbers[-1])))
                        else:
                            result = str(int(float(result)))
                    else:
                        result = str(int(float(result)))
                except (ValueError, TypeError) as e:
                    logger.warning(f"Could not convert result to integer: {result}, error: {e}")
                    # Keep original result if conversion fails
                    pass
            else:
                logger.warning(f"Unknown pipeline operation: {op}")
    
    return result

@handle_layer_boundary(LayerType.UTILITY, "process_template_content")
def process_template_content(content: str, dryrun: bool = False, ragflow_base_url: Optional[str] = None,
                      ragflow_api_key: Optional[str] = None, assistant_id: Optional[str] = None) -> List[str]:
    """Process template content with data tags and pipeline commands
    
    Args:
        content: Template content string
        
    Returns:
        List of results from executed commands
    """
    results = []
    
    # First extract all content between curly braces
    braces_content = extract_braces_content_from_string(content)
    
    for item in braces_content:
        # Check for RAGFlow data pattern
        ragflow_match = re.search(r'<\|data,([^|]+)\|>(.*?)<\|data,\1\|>', item, re.DOTALL)
        if ragflow_match:
            response = ragflow_for_data(content=item,
                            dryrun=dryrun,
                            ragflow_base_url= ragflow_base_url, 
                            ragflow_api_key=ragflow_api_key, 
                            assistant_id=assistant_id,
                            output_format= 'md')
            logger.debug(f"ragflow gives: {response}")
            
            if ragflow_match.group(1).endswith('/-'):
                results.append('')
                continue
            if response:
                results.append(response or '')
            else:
                results.append('')
            continue
            
        # chat_match = re.search(r'<\|chat(@([^,]+))?(,context)?\|>(.*?)<\|chat(@\2)?(,context)?\|>', item, re.DOTALL)
        # if chat_match:
        #     reporter_id = chat_match.group(2) if chat_match.group(2) else None
        #     response = ragflow_for_chat(
        #         template=item,
        #         reporter_id=reporter_id
        #     )
        #     if chat_match.group(1).endswith('/-'):
        #         results.append('')
        #         continue
        #     if response:
        #         results.append(response or '')
        #     else:
        #         results.append('')
        #     continue

        result = interpret_command(item)
        logger.debug(f"comand {item} gives: {result}")
        if result:
            results.append(result)
        else:
            results.append("")
    
    return results
                
@handle_layer_boundary(LayerType.UTILITY, "extract_braces_content_from_string")
def extract_braces_content_from_string(content: str) -> List[str]:
    """Extract content between curly braces from a string."""
    pattern = re.compile(r'\{([^{}]*)\}')
    return pattern.findall(content)

class InterpretCommand(BaseCommand):
    """Use RAGFlow to generate data from a template file"""
    def __init__(self):
        super().__init__(description="Use RAGFlow to generate data from a template file")
        self.add_argument('template', help='Path to the template file')
        self.add_argument('--ragflow-base-url', help='Base URL for RAGFlow API')
        self.add_argument('--ragflow-api-key', help='API key for RAGFlow')
        self.add_argument('--assistant-id', help='Assistant ID for RAGFlow chat')

    def execute(self, args):
        with open(args.template, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        results = process_template_content(
            content=template_content,
            ragflow_base_url=args.ragflow_base_url,
            ragflow_api_key=args.ragflow_api_key,
            assistant_id=args.assistant_id
        )
        
        # Output results as a list
        print("Results:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result}")
        
        return results

@handle_layer_boundary(LayerType.UTILITY, "replace_template_placeholders")
def replace_template_placeholders(context: dict, placeholder_pattern: str, template_content: str, results: List[str]) -> str:
    """Replace placeholders in template content with execution results.
    
    Args:
        template_content: Original template content
        results: List of execution results
        
    Returns:
        Template content with placeholders replaced
    """
    import re
    
    # Extract all content between curly braces in order as they appear in the template
    # braces_pattern = re.compile()
    braces_pattern = re.compile(placeholder_pattern)
    matches = list(braces_pattern.finditer(template_content))
    
    # Create a list of (match, result_index) pairs to maintain 1:1 mapping
    match_result_pairs = []
    result_index = 0
    
    # First pass: identify all placeholders and their corresponding results
    for match in matches:
        item = match.group(1)
        logger.debug(f"Align replacing for {item}")
        
        # Check for bind instruction
        if item.startswith('bind'):
            # For bind instructions, we'll handle them separately
            match_result_pairs.append((match, -1))  # Use -1 to indicate bind instruction
            result_index += 1
            continue
            
        # Check for RAGFlow data pattern
        ragflow_match = re.search(r'<\|data,([^|]+)\|>(.*?)<\|data,\1\|>', item, re.DOTALL)
        if ragflow_match:
            if result_index < len(results):
                match_result_pairs.append((match, result_index))
                result_index += 1
            continue
            
        # Check for chat context pattern with optional reporter identifier
        chat_context_match = re.search(r'<\|chat(@([^,]+))?(,context)?\|>(.*?)<\|chat(@\2)?(,context)?\|>', item, re.DOTALL)
        if chat_context_match:
            match_result_pairs.append((match, -2))  # Use -2 to indicate chat context instruction
            continue
            
        # Check for command pipeline or single command
        if ('|' in item and ':' in item) or ':' in item:
            if result_index < len(results):
                match_result_pairs.append((match, result_index))
                result_index += 1
    
    
    # logger.debug(f"Replacing plan: {results}\n{match_result_pairs}")
    # Replace placeholders in order (not reversed)
    processed_content = template_content
    
    # Process matches in order
    offset = 0  # Track position offset due to replacements
    
    for match, result_idx in match_result_pairs:
        item = match.group(1)
        start_pos = match.start() + offset
        end_pos = match.end() + offset
        
        # Handle bind instruction
        if result_idx == -1 and item.startswith('bind'):
            # Parse bind instruction
            if item == 'bind':
                # Simple bind instruction, just replace with empty string
                replacement_value = ''
            elif item.startswith('bind:context[') and item.endswith(']'):
                # Extract key from bind:context[key]
                key = item[len('bind:context['):-1]
                
                # Extract content between </key/> tags
                key_pattern = re.compile(f'</{key}/>(.*?)</{key}/>', re.DOTALL)
                key_match = key_pattern.search(processed_content)
                
                if key_match:
                    # Store the content in context
                    context[key] = key_match.group(1)
                
                replacement_value = ''
            else:
                # Unknown bind format, replace with empty string
                replacement_value = ''
                
            # Replace the placeholder
            processed_content = processed_content[:start_pos] + replacement_value + processed_content[end_pos:]
            offset += len(replacement_value) - (end_pos - start_pos)
            
        # Handle chat context instruction
        elif result_idx == -2 and '<|chat' in item:
            chat_context_match = re.search(r'<\|chat(@([^,]+))?(,context)?\|>(.*?)<\|chat(@\2)?(,context)?\|>', item, re.DOTALL)
            if chat_context_match and context:
                template = chat_context_match.group(4)
                
                # Replace /key/ patterns with corresponding values from context
                for key, value in context.items():
                    # Replace all occurrences of /key/ with the value from context
                    template = template.replace(f'/{key}/', value)
                
                # Use the new ragflow_for_chat function
                try:
                    reporter_id = chat_context_match.group(2) if chat_context_match.group(2) else None
                    replacement_value = ragflow_for_chat(
                        template=template,
                        reporter_id=reporter_id
                    )
                except Exception as e:
                    logger.error(f"Error during RAGFlow chat: {str(e)}")
                    replacement_value = template
            else:
                replacement_value = ''
                
            # Replace the placeholder
            processed_content = processed_content[:start_pos] + replacement_value + processed_content[end_pos:]
            offset += len(replacement_value) - (end_pos - start_pos)
            
        # Handle normal placeholders
        elif result_idx >= 0 and result_idx < len(results):
            # Extract only the data value from the result, not status messages
            result = results[result_idx]
            
            # Handle empty string results (from |- operations) - this is a valid result
            if result == '':
                replacement_value = ''
            elif result is not None and result != '' and '\n' in result:
                lines = result.strip().split('\n')
                
                # Check if this is a RAGFlow data request (contains markdown table)
                ragflow_match = re.search(r'<\|data,([^|]+)\|>(.*?)<\|data,\1\|>', item, re.DOTALL)
                if ragflow_match:
                    # For RAGFlow results, preserve the entire result (markdown table)
                    replacement_value = result
                else:
                    # For database query results, check if it's a single value or multi-line
                    if len(lines) >= 1:
                        # For single value results from strict mode queries, take the last non-empty line
                        data_line = lines[-1].strip()
                        if data_line and not data_line.startswith('Error') and len(lines) == 1:
                            replacement_value = data_line
                        else:
                            # For multi-line results (like tables), preserve the entire result
                            replacement_value = result
                    else:
                        replacement_value = result
            else:
                # For non-empty single values or None results
                replacement_value = result if result is not None else ''
            
            # Replace the placeholder
            processed_content = processed_content[:start_pos] + str(replacement_value) + processed_content[end_pos:]
            offset += len(str(replacement_value)) - (end_pos - start_pos)
    
    # Replace '</xxx/>' patterns with empty strings
    processed_content = re.sub(r'</[^/]+/>', '', processed_content)
    
    return processed_content

class StoreCommand(BaseCommand):
    """Process and store formatted content from a response file"""
    def __init__(self):
        super().__init__(description="Process and store formatted content from a response file")
        self.add_argument('response_file', help='Path to the response file containing answer text')
        self.add_argument('output_file', help='Base filename to use for output files')
        self.add_argument('--format', default='md',
                          choices=['md', 'csv', 'json'],
                          help='Output format (default: md)')
        self.add_argument('--colsqu',
                          help='Comma-separated column names (e.g., "日期,再贷余额") to transform data')

    def execute(self, args):
        with open(args.response_file, 'r', encoding='utf-8') as f:
            answer_content = f.read()
        
        result = store_answer(answer_content, args.output_file, args.format, args.colsqu)
        if result:
            print(result)

class ReportCommand(BaseCommand):
    """Generate report by replacing template placeholders with execution results"""
    def __init__(self):
        super().__init__(description="Generate report by replacing template placeholders with execution results")
        self.add_argument('template', help='Path to the template file')
        self.add_argument('output_file', help='Output file name for the generated report')
        self.add_argument('--ragflow-base-url', help='Base URL for RAGFlow API')
        self.add_argument('--ragflow-api-key', help='API key for RAGFlow')
        self.add_argument('--assistant-id', help='Assistant ID for RAGFlow chat')
        self.add_argument('-d', '--dryrun', action='store_true', help='Enable dryrun mode')

    def execute(self, args):
        with open(args.template, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        results = process_template_content(
            content=template_content,
            dryrun=args.dryrun,
            ragflow_base_url=args.ragflow_base_url,
            ragflow_api_key=args.ragflow_api_key,
            assistant_id=args.assistant_id
        )
        
        context = {'root':'PLACEHOLDER'}
        processed_content = replace_template_placeholders(
            context, r'{([^({})]*)}',
            template_content, results
        )
        processed_content = replace_template_placeholders(
            context, r'⁅([^(⁅⁆)]*)⁆',
            processed_content, []
        )
        
        with open(args.output_file, 'w', encoding='utf-8') as f:
            f.write(processed_content)
        
        print(f"Report generated successfully: {args.output_file}")
        return args.output_file
    

# Example usage:
"""
sample_csv = '''日期,新增放款金额,新增放款笔数,新增放款客户数,期末在贷金额,期末在贷笔数,期末在贷客户数,计量单位
2023年3月,4408856860,1324798,916315,17313281910,7880058,2182780,元
2023年6月,4777944092,1258406,916527,19817354363.96,8485847,2376058,元
2023年9月,5129938531,1252392,912576,22415188688.12,8908922,2596088,元
2023年12月,4815118876,1146497,802520,22752177295.19,8913018,2637133,元
2024年3月,4738033673,1087427,764014,21767286786.26,8335139,2551766,元
2024年6月,5360400598,1183396,841342,22486280973.36,8165781,2542306,元
2024年9月,6095069195,1283674,897382,24548347478.15,8319565,2608616,元
2024年12月,7013991084,1475496,1029367,27142894901.95,8885911,2829231,元'''

result = get_aggregated_data(sample_csv)
print(result)
"""
