import os
import requests
from typing import Optional, List, Dict, Any, Union
import json
import logging

class RAGFlowClient:
    """Client for interacting with the RAGFlow API."""

    def __init__(self, base_url: str, api_key: str):
        """
        Initialize the RAGFlow client.
        
        Args:
            base_url: Base URL of the RAGFlow API (e.g., "http://*************")
            api_key: API key for authentication
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)

    def _request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                params: Optional[Dict] = None, files: Optional[Dict] = None) -> Dict:
        """
        Internal method to make API requests.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint path
            data: Request body data
            params: Query parameters
            files: Files for multipart upload
            
        Returns:
            Response JSON as dictionary
            
        Raises:
            ValueError: If the API returns an error
            requests.exceptions.RequestException: For request failures
        """
        url = f"{self.base_url}{endpoint}"
        headers = self.headers.copy()
        
        self.logger.debug(f"Making {method} request to {url}")
        if data:
            self.logger.debug(f"Request data: {json.dumps(data, indent=2, ensure_ascii=False)}")
        if params:
            self.logger.debug(f"Request params: {params}")
        if files:
            self.logger.debug(f"Uploading {len(files)} files")
        
        try:
            if files:
                headers.pop('Content-Type', None)  # Let requests set Content-Type for multipart
            
            timeout = (
                float(os.getenv('RAG_HTTPX_CONNECT_TIMEOUT', '60.0')),  # connect timeout
                float(os.getenv('RAG_HTTPX_TIMEOUT', '360.0'))  # total timeout
            )
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                json=data,
                params=params,
                files=files,
                timeout=timeout
            )
            self.logger.debug(f"Response status: {response.status_code}")
            
            result = response.json()
            self.logger.debug(f"Raw response: {result}")
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON decode error: {str(e)}\nResponse: {response.text}")
            raise
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {str(e)}")
            raise
            
        return result.get('data', {'status': response.status_code, 'error': response.text})

    # Dataset Management
    def create_dataset(self, name: str, embedding_model: str, chunk_method: str = "one",
                      description: Optional[str] = None, permission: str = "me",
                      pagerank: int = 0,
                      parser_config: Optional[Dict] = None) -> Dict:
        """
        Create a new dataset.
        
        Args:
            name: Unique name of the dataset
            embedding_model: Name of embedding model to use
            chunk_method: Chunking method (default: "naive")
            description: Optional description
            permission: Access permission (default: "me")
            parser_config: Configuration for dataset parser
            pagerank:  PageRank value (default: 0)
            
        Returns:
            Created dataset information
        """
        data = {
            "name": name,
            "embedding_model": embedding_model,
            "chunk_method": chunk_method,
            "pagerank": pagerank,
            "permission": permission
        }
        
        if description:
            data["description"] = description
        if parser_config:
            data["parser_config"] = parser_config
            
        return self._request('POST', '/api/v1/datasets', data=data)

    def delete_datasets(self, dataset_ids: List[str]) -> Dict:
        """
        Delete datasets by ID.
        
        Args:
            dataset_ids: List of dataset IDs to delete
            
        Returns:
            Operation result
        """
        return self._request('DELETE', '/api/v1/datasets', data={"ids": dataset_ids})

    def update_dataset(self, dataset_id: str, name: Optional[str] = None,
                      embedding_model: Optional[str] = None,
                      chunk_method: Optional[str] = None) -> Dict:
        """
        Update dataset configuration.
        
        Args:
            dataset_id: ID of dataset to update
            name: New name (optional)
            embedding_model: New embedding model (optional)
            chunk_method: New chunk method (optional)
            
        Returns:
            Updated dataset information
        """
        data = {}
        if name:
            data["name"] = name
        if embedding_model:
            data["embedding_model"] = embedding_model
        if chunk_method:
            data["chunk_method"] = chunk_method
            
        return self._request('PUT', f'/api/v1/datasets/{dataset_id}', data=data)

    def list_datasets(self, page: int = 1, page_size: int = 30, orderby: str = "create_time",
                     desc: bool = True, name: Optional[str] = None,
                     dataset_id: Optional[str] = None) -> Dict:
        """
        List datasets with optional filtering.
        
        Args:
            page: Page number (default: 1)
            page_size: Items per page (default: 30)
            orderby: Field to sort by (default: "create_time")
            desc: Sort descending (default: True)
            name: Filter by name (optional)
            dataset_id: Filter by ID (optional)
            
        Returns:
            List of datasets
        """
        params = {
            "page": page,
            "page_size": page_size,
            "orderby": orderby,
            "desc": desc
        }
        
        if name:
            params["name"] = name
        if dataset_id:
            params["id"] = dataset_id
            
        return self._request('GET', '/api/v1/datasets', params=params)

    # File Management within Dataset
    def upload_documents(self, dataset_id: str, file_paths: List[str]) -> Dict:
        """
        Upload documents to a dataset.
        
        Args:
            dataset_id: ID of target dataset
            file_paths: List of local file paths to upload
            
        Returns:
            Uploaded document information
        """
        files = []
        for path in file_paths:
            files.append(('file', (open(path, 'rb'))))
            
        return self._request('POST', f'/api/v1/datasets/{dataset_id}/documents', files=files)

    def update_document(self, dataset_id: str, document_id: str, name: Optional[str] = None,
                       meta_fields: Optional[Dict] = None, chunk_method: Optional[str] = None,
                       parser_config: Optional[Dict] = None) -> Dict:
        """
        Update document configuration.
        
        Args:
            dataset_id: Associated dataset ID
            document_id: ID of document to update
            name: New name (optional)
            meta_fields: Metadata fields (optional)
            chunk_method: New chunk method (optional)
            parser_config: New parser config (optional)
            
        Returns:
            Updated document information
        """
        data = {}
        if name:
            data["name"] = name
        if meta_fields:
            data["meta_fields"] = meta_fields
        if chunk_method:
            data["chunk_method"] = chunk_method
        if parser_config:
            data["parser_config"] = parser_config
            
        return self._request('PUT', f'/api/v1/datasets/{dataset_id}/documents/{document_id}', data=data)

    def download_document(self, dataset_id: str, document_id: str, output_path: str) -> None:
        """
        Download a document from a dataset.
        
        Args:
            dataset_id: Associated dataset ID
            document_id: ID of document to download
            output_path: Local path to save the file
        """
        url = f"{self.base_url}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        response = requests.get(url, headers=self.headers, stream=True)
        
        if response.status_code != 200:
            raise ValueError(f"Failed to download document: {response.status_code}")
            
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

    def list_documents(self, dataset_id: str, page: int = 1, page_size: int = 30,
                      orderby: str = "create_time", desc: bool = True,
                      keywords: Optional[str] = None, document_id: Optional[str] = None,
                      document_name: Optional[str] = None) -> Dict:
        """
        List documents in a dataset.
        
        Args:
            dataset_id: Associated dataset ID
            page: Page number (default: 1)
            page_size: Items per page (default: 30)
            orderby: Field to sort by (default: "create_time")
            desc: Sort descending (default: True)
            keywords: Filter by keywords (optional)
            document_id: Filter by ID (optional)
            document_name: Filter by name (optional)
            
        Returns:
            List of documents
        """
        params = {
            "page": page,
            "page_size": page_size,
            "orderby": orderby,
            "desc": desc
        }
        
        if keywords:
            params["keywords"] = keywords
        if document_id:
            params["id"] = document_id
        if document_name:
            params["name"] = document_name
            
        return self._request('GET', f'/api/v1/datasets/{dataset_id}/documents', params=params)

    def delete_documents(self, dataset_id: str, document_ids: List[str]) -> Dict:
        """
        Delete documents from a dataset.
        
        Args:
            dataset_id: Associated dataset ID
            document_ids: List of document IDs to delete
            
        Returns:
            Operation result
        """
        return self._request('DELETE', f'/api/v1/datasets/{dataset_id}/documents', data={"ids": document_ids})

    def parse_documents(self, dataset_id: str, document_ids: List[str]) -> Dict:
        """
        Parse documents in a dataset.
        
        Args:
            dataset_id: Dataset ID
            document_ids: List of document IDs to parse
            
        Returns:
            Parsing operation result
        """
        return self._request('POST', f'/api/v1/datasets/{dataset_id}/chunks', data={"document_ids": document_ids})

    def stop_parsing_documents(self, dataset_id: str, document_ids: List[str]) -> Dict:
        """
        Stop parsing documents.
        
        Args:
            dataset_id: Associated dataset ID
            document_ids: List of document IDs to stop parsing
            
        Returns:
            Operation result
        """
        return self._request('DELETE', f'/api/v1/datasets/{dataset_id}/chunks', data={"document_ids": document_ids})

    # Chunk Management within Dataset
    def add_chunk(self, dataset_id: str, document_id: str, content: str,
                 important_keywords: Optional[List[str]] = None) -> Dict:
        """
        Add a chunk to a document.
        
        Args:
            dataset_id: Associated dataset ID
            document_id: Associated document ID
            content: Text content of the chunk
            important_keywords: List of important keywords (optional)
            
        Returns:
            Added chunk information
        """
        data = {"content": content}
        if important_keywords:
            data["important_keywords"] = important_keywords
            
        return self._request('POST', f'/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks', data=data)

    def list_chunks(self, dataset_id: str, document_id: str, keywords: Optional[str] = None,
                   page: int = 1, page_size: int = 1024, chunk_id: Optional[str] = None) -> Dict:
        """
        List chunks in a document.
        
        Args:
            dataset_id: Associated dataset ID
            document_id: Associated document ID
            keywords: Filter by keywords (optional)
            page: Page number (default: 1)
            page_size: Items per page (default: 1024)
            chunk_id: Filter by chunk ID (optional)
            
        Returns:
            List of chunks
        """
        params = {
            "page": page,
            "page_size": page_size
        }
        
        if keywords:
            params["keywords"] = keywords
        if chunk_id:
            params["id"] = chunk_id
            
        return self._request('GET', f'/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks', params=params)

    def delete_chunks(self, dataset_id: str, document_id: str, chunk_ids: List[str]) -> Dict:
        """
        Delete chunks from a document.
        
        Args:
            dataset_id: Associated dataset ID
            document_id: Associated document ID
            chunk_ids: List of chunk IDs to delete
            
        Returns:
            Operation result
        """
        return self._request('DELETE', f'/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks', data={"chunk_ids": chunk_ids})

    def update_chunk(self, dataset_id: str, document_id: str, chunk_id: str,
                    content: Optional[str] = None, important_keywords: Optional[List[str]] = None,
                    available: Optional[bool] = None) -> Dict:
        """
        Update a chunk.
        
        Args:
            dataset_id: Associated dataset ID
            document_id: Associated document ID
            chunk_id: ID of chunk to update
            content: New content (optional)
            important_keywords: New important keywords (optional)
            available: Set availability (optional)
            
        Returns:
            Updated chunk information
        """
        data = {}
        if content:
            data["content"] = content
        if important_keywords:
            data["important_keywords"] = important_keywords
        if available is not None:
            data["available"] = available
            
        return self._request('PUT', f'/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}', data=data)

    def retrieve_chunks(self, question: str, dataset_ids: Optional[List[str]] = None,
                       document_ids: Optional[List[str]] = None, page: int = 1,
                       page_size: int = 30, similarity_threshold: float = 0.2,
                       vector_similarity_weight: float = 0.3, top_k: int = 1024,
                       rerank_id: Optional[str] = None, keyword: bool = False,
                       highlight: bool = False) -> Dict:
        """
        Retrieve chunks from datasets/documents.
        
        Args:
            question: User query/question
            dataset_ids: List of dataset IDs to search (optional)
            document_ids: List of document IDs to search (optional)
            page: Page number (default: 1)
            page_size: Items per page (default: 30)
            similarity_threshold: Minimum similarity score (default: 0.2)
            vector_similarity_weight: Weight for vector similarity (default: 0.3)
            top_k: Number of top chunks (default: 1024)
            rerank_id: Rerank model ID (optional)
            keyword: Enable keyword matching (default: False)
            highlight: Enable term highlighting (default: False)
            
        Returns:
            Retrieved chunks and metadata
        """
        data = {
            "question": question,
            "page": page,
            "page_size": page_size,
            "similarity_threshold": similarity_threshold,
            "vector_similarity_weight": vector_similarity_weight,
            "top_k": top_k,
            "keyword": keyword,
            "highlight": highlight
        }
        
        if dataset_ids:
            data["dataset_ids"] = dataset_ids
        if document_ids:
            data["document_ids"] = document_ids
        if rerank_id:
            data["rerank_id"] = rerank_id
            
        return self._request('POST', '/api/v1/retrieval', data=data)

    # Chat Assistant Management
    def create_chat_assistant(self, name: str, dataset_ids: List[str], llm: Optional[Dict] = None,
                             prompt: Optional[Dict] = None, avatar: Optional[str] = None) -> Dict:
        """
        Create a chat assistant.
        
        Args:
            name: Name of the chat assistant
            dataset_ids: List of associated dataset IDs
            llm: LLM settings (optional)
            prompt: Prompt instructions (optional)
            avatar: Base64 encoded avatar (optional)
            
        Returns:
            Created chat assistant information
        """
        data = {
            "name": name,
            "dataset_ids": dataset_ids
        }
        
        if llm:
            data["llm"] = llm
        if prompt:
            data["prompt"] = prompt
        if avatar:
            data["avatar"] = avatar
            
        return self._request('POST', '/api/v1/chats', data=data)

    def update_chat_assistant(self, chat_id: str, name: Optional[str] = None,
                             dataset_ids: Optional[List[str]] = None, llm: Optional[Dict] = None,
                             prompt: Optional[Dict] = None, avatar: Optional[str] = None) -> Dict:
        """
        Update chat assistant configuration.
        
        Args:
            chat_id: ID of chat assistant to update
            name: New name (optional)
            dataset_ids: New dataset IDs (optional)
            llm: Updated LLM settings (optional)
            prompt: Updated prompt instructions (optional)
            avatar: New avatar (optional)
            
        Returns:
            Updated chat assistant information
        """
        data = {}
        if name:
            data["name"] = name
        if dataset_ids:
            data["dataset_ids"] = dataset_ids
        if llm:
            data["llm"] = llm
        if prompt:
            data["prompt"] = prompt
        if avatar:
            data["avatar"] = avatar
            
        return self._request('PUT', f'/api/v1/chats/{chat_id}', data=data)

    def delete_chat_assistants(self, chat_ids: List[str]) -> Dict:
        """
        Delete chat assistants.
        
        Args:
            chat_ids: List of chat assistant IDs to delete
            
        Returns:
            Operation result
        """
        return self._request('DELETE', '/api/v1/chats', data={"ids": chat_ids})

    def list_chat_assistants(self, page: int = 1, page_size: int = 30,
                            orderby: str = "create_time", desc: bool = True,
                            name: Optional[str] = None, chat_id: Optional[str] = None) -> Dict:
        """
        List chat assistants.
        
        Args:
            page: Page number (default: 1)
            page_size: Items per page (default: 30)
            orderby: Field to sort by (default: "create_time")
            desc: Sort descending (default: True)
            name: Filter by name (optional)
            chat_id: Filter by chat ID (optional)
            
        Returns:
            List of chat assistants
        """
        params = {
            "page": page,
            "page_size": page_size,
            "orderby": orderby,
            "desc": desc
        }
        
        if name:
            params["name"] = name
        if chat_id:
            params["id"] = chat_id
            
        return self._request('GET', '/api/v1/chats', params=params)

    # Session Management
    def create_chat_session(self, chat_id: str, name: str, user_id: Optional[str] = None) -> Dict:
        """
        Create a session with a chat assistant.
        
        Args:
            chat_id: ID of associated chat assistant
            name: Name of the session
            user_id: Optional user-defined ID
            
        Returns:
            Created session information
        """
        data = {"name": name}
        if user_id:
            data["user_id"] = user_id
            
        return self._request('POST', f'/api/v1/chats/{chat_id}/sessions', data=data)

    def update_chat_session(self, chat_id: str, session_id: str, name: Optional[str] = None,
                           user_id: Optional[str] = None) -> Dict:
        """
        Update a chat session.
        
        Args:
            chat_id: ID of associated chat assistant
            session_id: ID of session to update
            name: New name (optional)
            user_id: New user ID (optional)
            
        Returns:
            Updated session information
        """
        data = {}
        if name:
            data["name"] = name
        if user_id:
            data["user_id"] = user_id
            
        return self._request('PUT', f'/api/v1/chats/{chat_id}/sessions/{session_id}', data=data)

    def list_chat_sessions(self, chat_id: str, page: int = 1, page_size: int = 30,
                          orderby: str = "create_time", desc: bool = True,
                          name: Optional[str] = None, session_id: Optional[str] = None,
                          user_id: Optional[str] = None) -> Dict:
        """
        List sessions associated with a chat assistant.
        
        Args:
            chat_id: ID of associated chat assistant
            page: Page number (default: 1)
            page_size: Items per page (default: 30)
            orderby: Field to sort by (default: "create_time")
            desc: Sort descending (default: True)
            name: Filter by name (optional)
            session_id: Filter by session ID (optional)
            user_id: Filter by user ID (optional)
            
        Returns:
            List of sessions
        """
        params = {
            "page": page,
            "page_size": page_size,
            "orderby": orderby,
            "desc": desc
        }
        
        if name:
            params["name"] = name
        if session_id:
            params["id"] = session_id
        if user_id:
            params["user_id"] = user_id
            
        return self._request('GET', f'/api/v1/chats/{chat_id}/sessions', params=params)

    def delete_chat_sessions(self, chat_id: str, session_ids: List[str]) -> Dict:
        """
        Delete chat sessions.
        
        Args:
            chat_id: ID of associated chat assistant
            session_ids: List of session IDs to delete
            
        Returns:
            Operation result
        """
        return self._request('DELETE', f'/api/v1/chats/{chat_id}/sessions', data={"ids": session_ids})

    def converse_with_chat(self, chat_id: str, question: str, stream: bool = False,
                          session_id: Optional[str] = None, user_id: Optional[str] = None) -> Dict:
        """
        Converse with a chat assistant.
        
        Args:
            chat_id: ID of chat assistant
            question: User question/input
            stream: Whether to stream responses (default: True)
            session_id: Existing session ID (optional)
            user_id: User ID (optional)
            
        Returns:
            Conversation response
        """
        data = {
            "question": question,
            "stream": stream
        }
        
        if session_id:
            data["session_id"] = session_id
        if user_id:
            data["user_id"] = user_id
            
        return self._request('POST', f'/api/v1/chats/{chat_id}/completions', data=data)

    # Agent Management
    def list_agents(self, page: int = 1, page_size: int = 30,
                   orderby: str = "create_time", desc: bool = True,
                   name: Optional[str] = None, agent_id: Optional[str] = None) -> Dict:
        """
        List agents.
        
        Args:
            page: Page number (default: 1)
            page_size: Items per page (default: 30)
            orderby: Field to sort by (default: "create_time")
            desc: Sort descending (default: True)
            name: Filter by name (optional)
            agent_id: Filter by agent ID (optional)
            
        Returns:
            List of agents
        """
        params = {
            "page": page,
            "page_size": page_size,
            "orderby": orderby,
            "desc": desc
        }
        
        if name:
            params["name"] = name
        if agent_id:
            params["id"] = agent_id
            
        return self._request('GET', '/api/v1/agents', params=params)

    def get_related_questions(self, question: str) -> List[str]:
        """
        Generate related questions for a query.
        
        Args:
            question: Original user question
            
        Returns:
            List of related questions
        """
        data = {"question": question}
        result = self._request('POST', '/api/v1/conversation/related_questions', data=data)
        return result.get('data', [])
