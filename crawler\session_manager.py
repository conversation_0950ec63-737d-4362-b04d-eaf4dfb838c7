import json
import logging
import os
import base64
from datetime import datetime
from pathlib import Path

# Import unified error handling framework
from app.exceptions import LayerType, ErrorSeverity
from app.error_handling import log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context
from .exceptions import (
    CrawlerSessionException, BrowserException, ElementNotFoundException,
    CrawlerLayerType
)

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for session management
set_layer_context("utility")

DEFAULT_VIEWPORT = {"width": 1280, "height": 720}

class SessionManager:
    def __init__(self, session_file_path="session.json"):
        self.session_file_path = session_file_path
    
    @log_and_reraise(logger, "session save")
    async def save_session(self, context):
        """Save session cookies with unified error handling"""
        set_operation_context("session_save")

        with error_boundary("cookie extraction", CrawlerLayerType.CORE):
            try:
                cookies = await context.cookies()
            except Exception as e:
                raise BrowserException(
                    message="Failed to extract cookies from browser context",
                    browser_action="cookie_extraction",
                    details=str(e),
                    original_exception=e,
                    suggested_action="Check browser context state"
                )

        with error_boundary("session file write", CrawlerLayerType.CORE):
            try:
                with open(self.session_file_path, "w") as f:
                    json.dump(cookies, f)
                logger.info("✅ 会话已保存")
            except (IOError, OSError) as e:
                raise CrawlerSessionException(
                    message="Failed to save session file",
                    details=f"Could not write to {self.session_file_path}: {str(e)}",
                    context={'session_file_path': self.session_file_path},
                    original_exception=e,
                    suggested_action="Check file permissions and disk space"
                )

    @log_and_reraise(logger, "session load")
    async def load_session(self, context):
        """Load session cookies with unified error handling"""
        set_operation_context("session_load")

        # Check if session file exists
        if not Path(self.session_file_path).exists():
            logger.debug(f"Session file {self.session_file_path} does not exist")
            return False

        with error_boundary("session file read", CrawlerLayerType.CORE):
            try:
                with open(self.session_file_path, "r") as f:
                    cookies = json.load(f)
            except (IOError, OSError) as e:
                raise CrawlerSessionException(
                    message="Failed to read session file",
                    details=f"Could not read from {self.session_file_path}: {str(e)}",
                    context={'session_file_path': self.session_file_path},
                    original_exception=e,
                    suggested_action="Check file permissions and integrity"
                )
            except json.JSONDecodeError as e:
                raise CrawlerSessionException(
                    message="Invalid session file format",
                    details=f"Session file contains invalid JSON: {str(e)}",
                    context={'session_file_path': self.session_file_path},
                    original_exception=e,
                    suggested_action="Delete corrupted session file and login again"
                )

        with error_boundary("cookie loading", CrawlerLayerType.CORE):
            try:
                await context.add_cookies(cookies)
                logger.info("🔍 检测到本地会话")
                return True
            except Exception as e:
                raise BrowserException(
                    message="Failed to load cookies into browser context",
                    browser_action="cookie_loading",
                    details=str(e),
                    original_exception=e,
                    suggested_action="Check cookie format and browser context state"
                )

    async def ensure_viewport(self, page):
        """Ensure consistent viewport in headless mode"""
        current_viewport = await page.evaluate("() => { return { width: window.innerWidth, height: window.innerHeight } }")
        if current_viewport["width"] < DEFAULT_VIEWPORT["width"]:
            await page.set_viewport_size(DEFAULT_VIEWPORT)
            logger.debug(f"Viewport set to {DEFAULT_VIEWPORT}")

    async def ensure_element_ready(self, page, selector, timeout=30000):
        """Ensure element is ready for interaction in headless mode"""
        try:
            await page.wait_for_selector(selector, state="attached", timeout=timeout)
            await page.wait_for_selector(selector, state="visible", timeout=timeout)
            return True
        except Exception as e:
            logger.error(f"Element {selector} not ready: {str(e)}")
            return False

    async def safe_fill(self, page, selector, text, max_retries=3):
        """Safe fill with retries for headless mode"""
        for attempt in range(max_retries):
            try:
                if await self.ensure_element_ready(page, selector):
                    await page.fill(selector, text)
                    return True
            except Exception as e:
                logger.warning(f"Fill attempt {attempt+1} failed: {str(e)}")
                await page.wait_for_timeout(2000)  # Wait before retry
        return False

    def save_status(self, status: dict):
        """保存登录状态"""
        os.makedirs("crawl", exist_ok=True)
        with open("crawl/session_status.json", "w") as f:
            json.dump(status, f)

    def read_status(self) -> dict:
        """读取登录状态"""
        if not Path("crawl/session_status.json").exists():
            return {"logged_in": False, "timestamp": None}
        with open("crawl/session_status.json") as f:
            return json.load(f)

    def save_qrcode(self, screen: str) -> str:
        """保存二维码图片"""
        os.makedirs("crawl", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        path = f"crawl/{timestamp}_screen.png"
        with open(path, "wb") as f:
            f.write(base64.b64decode(screen))
        return path
