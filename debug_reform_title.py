#!/usr/bin/env python3
"""
Debug the reform title extraction process
"""

import sys
import os
from pathlib import Path
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def debug_reform_title():
    """Debug the reform title extraction"""
    print("="*80)
    print("DEBUGGING REFORM TITLE EXTRACTION")
    print("="*80)
    
    # Read the header-adjusted content
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_资产负债表.md"
    
    print(f"Filename: {filename}")
    
    # Create reformer
    reformer = MarkdownTableReformer()
    
    # Parse the table
    table_data, separator_line, markdown_headers = reformer.parse_markdown_table(content)
    
    print(f"Markdown headers: {markdown_headers}")
    
    # Extract title from markdown headers
    main_title, metadata_from_headers = reformer.extract_metadata_from_markdown_headers(markdown_headers)
    print(f"Title from markdown headers: '{main_title}'")
    
    # Test the filename logic manually
    print(f"\n" + "="*60)
    print("TESTING FILENAME LOGIC MANUALLY")
    print("="*60)
    
    base_filename = filename.replace('.md', '').replace('_adjusted', '').replace('_reformed', '')
    print(f"Base filename: '{base_filename}'")
    
    has_chinese = re.search(r'[\u4e00-\u9fff]', base_filename)
    print(f"Contains Chinese: {bool(has_chinese)}")
    
    if has_chinese:
        condition1 = len(base_filename) > len(main_title or '')
        condition2 = '2024' in base_filename
        condition3 = 'Q' in base_filename
        
        print(f"Condition 1 (length): {len(base_filename)} > {len(main_title or '')} = {condition1}")
        print(f"Condition 2 (2024): {condition2}")
        print(f"Condition 3 (Q): {condition3}")
        
        should_override = condition1 or condition2 or condition3
        print(f"Should override: {should_override}")
        
        if should_override:
            print(f"Would set main_title to: '{base_filename}'")
        else:
            print(f"Would keep main_title as: '{main_title}'")
    
    # Now run the actual process and see what happens
    print(f"\n" + "="*60)
    print("RUNNING ACTUAL PROCESS")
    print("="*60)
    
    result = reformer.process_table(content, filename)
    
    # Extract the actual result
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
    if metadata_match:
        import json
        metadata = json.loads(metadata_match.group(1))
        actual_table_name = metadata.get('table_name', '')
        print(f"Actual table_name in result: '{actual_table_name}'")
    else:
        print("No metadata found in result")

def main():
    """Main debug function"""
    try:
        debug_reform_title()
        return 0
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
