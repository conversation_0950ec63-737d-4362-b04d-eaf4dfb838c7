# Create organizational units for departments
dn: ou=people,dc=company,dc=com
objectClass: organizationalUnit
ou: people
description: All users

dn: ou=groups,dc=company,dc=com
objectClass: organizationalUnit
ou: groups
description: All groups

dn: ou=departments,dc=company,dc=com
objectClass: organizationalUnit
ou: departments
description: All departments

# Create department OUs
dn: ou=finance,ou=departments,dc=company,dc=com
objectClass: organizationalUnit
ou: finance
description: Finance Department

dn: ou=risk,ou=departments,dc=company,dc=com
objectClass: organizationalUnit
ou: risk
description: Risk Management Department

dn: ou=technology,ou=departments,dc=company,dc=com
objectClass: organizationalUnit
ou: technology
description: Technology Department

dn: ou=admin,ou=departments,dc=company,dc=com
objectClass: organizationalUnit
ou: admin
description: Administration Department
