# Crawler Error Handling Guide

This guide provides comprehensive documentation for the unified error handling strategy implemented in the crawler directory, including web scraping error scenarios and browser automation error handling.

## Table of Contents

1. [Overview](#overview)
2. [Exception Hierarchy](#exception-hierarchy)
3. [Layer Architecture](#layer-architecture)
4. [Common Error Scenarios](#common-error-scenarios)
5. [Best Practices](#best-practices)
6. [Code Examples](#code-examples)
7. [Troubleshooting](#troubleshooting)

## Overview

The crawler implements a unified error handling strategy that extends the application-wide error handling framework with crawler-specific exception classes and patterns. This approach provides:

- **Consistent Error Handling**: All crawler operations use the same error handling patterns
- **Rich Context Information**: Errors include detailed context about web scraping operations
- **Layer Boundary Management**: Proper exception handling at each architectural layer
- **Enhanced Logging**: Crawler-specific logging with context variables
- **User-Friendly Messages**: Clear error messages with suggested actions

## Exception Hierarchy

### Base Crawler Exceptions

```python
CrawlerException (BaseAppException)
├── CrawlerUIException (PresentationLayerException)
├── UserInputException (PresentationLayerException)
├── CrawlerServerException (BusinessLogicException)
│   ├── LoginException
│   ├── AccountRestrictedException
│   └── CrawlerSessionException
└── WebScrapingException (UtilityException)
    ├── BrowserException
    ├── PageLoadException
    ├── ElementNotFoundException
    ├── DataExtractionException
    ├── SearchException
    ├── RateLimitException
    ├── CaptchaException
    ├── NetworkException
    └── TimeoutException
```

### Layer Mapping

- **Presentation Layer**: `streamlit_app.py` - UI interactions and user input validation
- **Server Layer**: `server.py` - API endpoints and business logic
- **Core Layer**: `core.py` - Web scraping utilities and browser automation

## Layer Architecture

### Presentation Layer (streamlit_app.py)

**Responsibilities:**
- User input validation
- Error display to users
- UI state management

**Exception Types:**
- `CrawlerUIException`: General UI errors
- `UserInputException`: Invalid user input

**Example:**
```python
@handle_layer_boundary(CrawlerLayerType.PRESENTATION, "status command processing")
def process_status_command(url: str) -> dict:
    if not url or not url.startswith(('http://', 'https://')):
        raise UserInputException(
            message="Invalid URL format",
            input_field="url",
            suggested_action="Please provide a valid URL starting with http:// or https://"
        )
```

### Server Layer (server.py)

**Responsibilities:**
- API endpoint handling
- Business logic coordination
- Session management

**Exception Types:**
- `LoginException`: Authentication failures
- `AccountRestrictedException`: Account access restrictions
- `CrawlerSessionException`: Session management errors

**Example:**
```python
@app.post("/login")
@handle_layer_boundary(CrawlerLayerType.SERVER, "user login process")
async def login():
    if login_status.get("status") == "permanent_failed":
        raise AccountRestrictedException(
            message="Account access restricted",
            restriction_type="permanent",
            suggested_action="Contact administrator for account review"
        )
```

### Core Layer (core.py)

**Responsibilities:**
- Web scraping operations
- Browser automation
- Data extraction

**Exception Types:**
- `BrowserException`: Browser control errors
- `PageLoadException`: Page navigation failures
- `ElementNotFoundException`: Element selection failures
- `DataExtractionException`: Data parsing errors
- `SearchException`: Search operation failures
- `RateLimitException`: Rate limiting issues
- `CaptchaException`: CAPTCHA challenges
- `NetworkException`: Network connectivity issues
- `TimeoutException`: Operation timeouts

**Example:**
```python
@log_and_reraise(logger, "search suggestion reading")
async def read(self, url: str, keyword: str, page):
    with error_boundary("page navigation", CrawlerLayerType.CORE):
        try:
            await page.goto(url)
        except Exception as e:
            raise PageLoadException(
                message="Failed to navigate to target URL",
                url=url,
                suggested_action="Check URL validity and network connectivity"
            )
```

## Common Error Scenarios

### 1. Browser Automation Errors

**Scenario**: Browser fails to launch or becomes unresponsive

**Exception**: `BrowserException`

**Handling**:
```python
try:
    self._browser = await self._playwright.chromium.launch(headless=headless)
except Exception as e:
    raise BrowserException(
        message="Failed to launch browser",
        browser_action="browser_launch",
        details=str(e),
        suggested_action="Check system resources and browser installation"
    )
```

### 2. Element Not Found Errors

**Scenario**: Required page elements cannot be located

**Exception**: `ElementNotFoundException`

**Handling**:
```python
if not suggest_items:
    raise ElementNotFoundException(
        message="Unable to locate dropdown suggestion elements",
        selector=", ".join(selectors),
        url=url,
        suggested_action="Check if page structure has changed"
    )
```

### 3. Login and Authentication Errors

**Scenario**: User authentication fails or account is restricted

**Exception**: `LoginException` or `AccountRestrictedException`

**Handling**:
```python
if login_result.get("status") == "rate_limited":
    raise AccountRestrictedException(
        message="Account access restricted",
        restriction_type="rate_limited",
        suggested_action="Wait before retrying or contact support"
    )
```

### 4. Network and Timeout Errors

**Scenario**: Network connectivity issues or operation timeouts

**Exception**: `NetworkException` or `TimeoutException`

**Handling**:
```python
except httpx.TimeoutException:
    raise TimeoutException(
        message="Request timeout",
        operation="api_request",
        timeout_duration=30.0,
        suggested_action="Check network connectivity and server status"
    )
```

### 5. Data Extraction Errors

**Scenario**: Extracted data is invalid or incomplete

**Exception**: `DataExtractionException`

**Handling**:
```python
if not company_names:
    raise DataExtractionException(
        message="No company data extracted",
        data_type="company_suggestions",
        suggested_action="Check page structure and selectors"
    )
```

## Best Practices

### 1. Use Error Boundaries

Always wrap operations in error boundaries to provide context:

```python
with error_boundary("operation description", CrawlerLayerType.CORE):
    # Your operation here
    pass
```

### 2. Provide Rich Context

Include relevant context information in exceptions:

```python
raise ElementNotFoundException(
    message="Search field not found",
    selector="#searchKey",
    url=current_url,
    context={'page_title': page_title, 'retry_count': retry_count}
)
```

### 3. Use Appropriate Exception Types

Choose the most specific exception type for each scenario:

- Use `BrowserException` for browser control issues
- Use `NetworkException` for connectivity problems
- Use `TimeoutException` for operation timeouts
- Use `LoginException` for authentication failures

### 4. Include Suggested Actions

Always provide actionable suggestions for error resolution:

```python
suggested_action="Check if the crawler server is running and accessible"
```

### 5. Log with Context

Use the enhanced logging system with context variables:

```python
from .logging_config import set_crawler_page_context, log_browser_action

set_crawler_page_context(url)
log_browser_action("page_navigation", f"Navigating to {url}")
```

### 6. Handle Unexpected Exceptions

Use unified exception hierarchy for unexpected exceptions:

```python
except Exception as e:
    # Create appropriate crawler exception based on context
    crawler_exc = CrawlerException(
        message="Unexpected crawler error",
        error_code="CRAWLER_UNEXPECTED_001",
        details=f"Operation failed: {str(e)}",
        original_exception=e,
        suggested_action="Check logs and retry operation"
    )
    crawler_exc.log_error(logger)
    raise crawler_exc
```

## Code Examples

### Complete Error Handling Pattern

```python
@log_and_reraise(logger, "web scraping operation")
async def scrape_data(self, url: str, selectors: list):
    set_operation_context("data_scraping")
    set_crawler_page_context(url)
    
    # Page navigation with error handling
    with error_boundary("page navigation", CrawlerLayerType.CORE):
        try:
            await self.page.goto(url)
        except Exception as e:
            raise PageLoadException(
                message="Failed to load page",
                url=url,
                details=str(e),
                suggested_action="Check URL validity and network connectivity"
            )
    
    # Element selection with error handling
    with error_boundary("element selection", CrawlerLayerType.CORE):
        elements = []
        for selector in selectors:
            try:
                element = await self.page.wait_for_selector(selector, timeout=10000)
                if element:
                    elements.append(element)
            except Exception as e:
                logger.warning(f"Selector {selector} failed: {e}")
        
        if not elements:
            raise ElementNotFoundException(
                message="No elements found with provided selectors",
                selector=", ".join(selectors),
                url=url,
                suggested_action="Check if page structure has changed"
            )
    
    # Data extraction with error handling
    with error_boundary("data extraction", CrawlerLayerType.CORE):
        try:
            data = []
            for element in elements:
                text = await element.text_content()
                if text:
                    data.append(text.strip())
            
            if not data:
                raise DataExtractionException(
                    message="No data extracted from elements",
                    data_type="text_content",
                    suggested_action="Check element content and extraction logic"
                )
            
            return data
            
        except Exception as e:
            if isinstance(e, DataExtractionException):
                raise
            raise DataExtractionException(
                message="Data extraction failed",
                data_type="text_content",
                details=str(e),
                original_exception=e
            )
```

### Streamlit Error Display

```python
def display_error_with_context(error: BaseAppException):
    """Display error with rich context in Streamlit"""
    st.error(f"❌ {error.message}")
    
    if error.suggested_action:
        st.info(f"💡 {error.suggested_action}")
    
    if error.details:
        with st.expander("Error Details"):
            st.text(error.details)
    
    if error.context:
        with st.expander("Context Information"):
            st.json(error.context)
```

## Troubleshooting

### Common Issues and Solutions

1. **Browser Launch Failures**
   - Check system resources and browser installation
   - Verify Playwright dependencies
   - Try running in headless mode

2. **Element Selection Failures**
   - Verify page structure hasn't changed
   - Check selector syntax and specificity
   - Add wait conditions for dynamic content

3. **Network Connectivity Issues**
   - Check server status and accessibility
   - Verify network configuration
   - Increase timeout values if needed

4. **Authentication Problems**
   - Verify login credentials
   - Check for account restrictions
   - Clear session data and retry

5. **Data Extraction Issues**
   - Validate page content and structure
   - Check extraction logic and selectors
   - Handle dynamic content loading

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
export CRAWLER_LOG_LEVEL=DEBUG
export CRAWLER_LOG_DIR=logs/debug
```

### Error Context Analysis

Use the crawler context information for debugging:

```python
from .logging_config import get_crawler_context

context = get_crawler_context()
logger.debug(f"Current crawler context: {context}")
```

This comprehensive error handling strategy ensures robust and maintainable crawler operations with clear error reporting and resolution guidance.

## Advanced Error Handling Patterns

### 1. Retry Logic with Exponential Backoff

```python
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=1, max=10),
    reraise=True
)
async def robust_page_load(self, url: str):
    with error_boundary("robust page loading", CrawlerLayerType.CORE):
        try:
            await self.page.goto(url, wait_until='networkidle')
        except Exception as e:
            if "timeout" in str(e).lower():
                raise TimeoutException(
                    message="Page load timeout with retry",
                    operation="page_load_with_retry",
                    url=url,
                    suggested_action="Check network connectivity and page responsiveness"
                )
            raise PageLoadException(
                message="Page load failed after retries",
                url=url,
                details=str(e),
                original_exception=e
            )
```

### 2. Circuit Breaker Pattern

```python
class CrawlerCircuitBreaker:
    def __init__(self, failure_threshold=5, recovery_timeout=60):
        self.failure_count = 0
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    async def call(self, operation, *args, **kwargs):
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
            else:
                raise CrawlerServerException(
                    message="Circuit breaker is OPEN",
                    error_code="CRAWLER_CIRCUIT_001",
                    details="Too many failures, circuit breaker activated",
                    suggested_action=f"Wait {self.recovery_timeout} seconds before retrying"
                )

        try:
            result = await operation(*args, **kwargs)
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
            return result
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()

            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"

            raise
```

### 3. Error Recovery Strategies

```python
async def resilient_login_with_recovery(self):
    """Login with multiple recovery strategies"""
    recovery_strategies = [
        self._try_normal_login,
        self._try_session_recovery,
        self._try_browser_reset,
        self._try_fresh_start
    ]

    last_exception = None

    for i, strategy in enumerate(recovery_strategies):
        try:
            set_operation_context(f"login_strategy_{i+1}")
            result = await strategy()
            log_login_attempt("success", f"Strategy {i+1} succeeded")
            return result

        except AccountRestrictedException:
            # Don't retry for permanent restrictions
            raise

        except Exception as e:
            last_exception = e
            log_login_attempt("failed", f"Strategy {i+1} failed: {str(e)}")

            if i < len(recovery_strategies) - 1:
                await asyncio.sleep(2 ** i)  # Exponential backoff

    # All strategies failed
    raise LoginException(
        message="All login recovery strategies failed",
        login_status="recovery_exhausted",
        details=f"Last error: {str(last_exception)}",
        original_exception=last_exception,
        suggested_action="Manual intervention required"
    )
```

### 4. Error Aggregation and Reporting

```python
class CrawlerErrorCollector:
    def __init__(self):
        self.errors = []
        self.warnings = []

    def add_error(self, error: BaseAppException):
        self.errors.append({
            'timestamp': datetime.now(),
            'error_code': error.error_code,
            'message': error.message,
            'layer': error.layer,
            'context': error.context
        })

    def add_warning(self, message: str, context: dict = None):
        self.warnings.append({
            'timestamp': datetime.now(),
            'message': message,
            'context': context or {}
        })

    def get_summary(self) -> dict:
        return {
            'total_errors': len(self.errors),
            'total_warnings': len(self.warnings),
            'error_types': list(set(e['error_code'] for e in self.errors)),
            'most_recent_error': self.errors[-1] if self.errors else None,
            'error_rate': len(self.errors) / max(1, len(self.errors) + len(self.warnings))
        }

    def should_abort(self) -> bool:
        """Determine if operation should be aborted based on error patterns"""
        if len(self.errors) > 10:  # Too many errors
            return True

        # Check for critical error patterns
        critical_errors = [e for e in self.errors if 'CRITICAL' in e.get('error_code', '')]
        if len(critical_errors) > 2:
            return True

        return False
```

## Performance Monitoring and Error Metrics

### 1. Operation Timing with Error Tracking

```python
@log_operation_timing(logger, "comprehensive_crawl")
async def crawl_with_metrics(self, urls: list):
    error_collector = CrawlerErrorCollector()
    successful_crawls = 0

    for url in urls:
        try:
            await self.crawl_single_page(url)
            successful_crawls += 1
        except Exception as e:
            if isinstance(e, BaseAppException):
                error_collector.add_error(e)
            else:
                # Create appropriate crawler exception for unexpected errors
                crawler_exc = CrawlerException(
                    message="Unexpected crawl error",
                    error_code="CRAWLER_UNEXPECTED_001",
                    layer=CrawlerLayerType.CORE,
                    details=f"Page crawl failed: {str(e)}",
                    original_exception=e,
                    suggested_action="Check page accessibility and retry"
                )
                error_collector.add_error(crawler_exc)

            if error_collector.should_abort():
                logger.error("Aborting crawl due to excessive errors")
                break

    # Log final metrics
    summary = error_collector.get_summary()
    logger.info(f"Crawl completed: {successful_crawls}/{len(urls)} successful, "
               f"Error rate: {summary['error_rate']:.2%}")

    return {
        'successful_crawls': successful_crawls,
        'total_urls': len(urls),
        'error_summary': summary
    }
```

### 2. Health Check with Error Status

```python
async def health_check(self) -> dict:
    """Comprehensive health check with error status"""
    health_status = {
        'status': 'healthy',
        'checks': {},
        'errors': [],
        'timestamp': datetime.now().isoformat()
    }

    # Browser health check
    try:
        if self.browser and self.browser.is_connected():
            health_status['checks']['browser'] = 'healthy'
        else:
            raise BrowserException("Browser not connected", browser_action="health_check")
    except Exception as e:
        health_status['checks']['browser'] = 'unhealthy'
        health_status['errors'].append(str(e))
        health_status['status'] = 'degraded'

    # Session health check
    try:
        session_valid = await self.session_manager.validate_session()
        health_status['checks']['session'] = 'healthy' if session_valid else 'expired'
    except Exception as e:
        health_status['checks']['session'] = 'unhealthy'
        health_status['errors'].append(str(e))
        health_status['status'] = 'degraded'

    # Network connectivity check
    try:
        await self.test_network_connectivity()
        health_status['checks']['network'] = 'healthy'
    except Exception as e:
        health_status['checks']['network'] = 'unhealthy'
        health_status['errors'].append(str(e))
        health_status['status'] = 'unhealthy'

    return health_status
```

This extended documentation provides advanced patterns for robust error handling, recovery strategies, and performance monitoring in crawler operations.
