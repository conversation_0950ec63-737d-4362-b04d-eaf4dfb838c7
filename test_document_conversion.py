#!/usr/bin/env python3
"""
Test script for document conversion pipeline.

This script tests the RAG workflow document conversion process to ensure
all fixes are working correctly.
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

# Import the necessary modules
from app.workflows.rag_workflow import RAGWorkflow
from app.logging_config import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

def test_document_conversion():
    """Test the document conversion pipeline"""
    
    print("🧪 Testing Document Conversion Pipeline")
    print("=" * 50)
    
    # Test parameters
    workspace_path = "workspaces/test_workspace"
    template_file = "templates/test_workspace/市场.md"
    
    try:
        print("1. Initializing RAG Workflow...")
        workflow = RAGWorkflow(workspace_path, template_file)
        print("   ✅ RAG Workflow initialized successfully")
        
        print("\n2. Testing document conversion...")
        # This should trigger the document conversion process
        result = workflow.execute()
        
        if result.get('status') == 'success':
            print("   ✅ Document conversion completed successfully")
            print(f"   📄 Result: {result.get('message', 'No message')}")
            return True
        else:
            print(f"   ❌ Document conversion failed: {result.get('message', 'Unknown error')}")
            print(f"   🔍 Details: {result.get('details', 'No details')}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception during testing: {str(e)}")
        logger.exception("Document conversion test failed")
        return False

def test_table_processing():
    """Test table processing specifically"""

    print("\n🧪 Testing Table Processing")
    print("=" * 50)

    try:
        # Test the reform utility directly
        import subprocess
        import tempfile
        import os

        print("1. Creating test markdown file...")
        test_content = """
| 科目 | 2024-12-31 |
| --- | --- |
| 货币资金 | 852093637.20 |
| 应收账款 | 1355891782.03 |
"""

        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            test_file = f.name

        print("   ✅ Test file created successfully")

        print("\n2. Testing reform utility...")
        result = subprocess.run([
            'python', 'app/utils/reform.py', test_file
        ], capture_output=True, text=True, cwd=os.getcwd())

        # Clean up test file
        os.unlink(test_file)

        if result.returncode == 0:
            print("   ✅ Table processing completed successfully")
            return True
        else:
            print(f"   ❌ Table processing failed: {result.stderr}")
            return False

    except Exception as e:
        print(f"   ❌ Exception during table processing test: {str(e)}")
        logger.exception("Table processing test failed")
        return False

def main():
    """Main test function"""
    
    print("🚀 Starting Document Conversion Pipeline Tests")
    print("=" * 60)
    
    # Test 1: Table Processing (isolated test)
    table_test_passed = test_table_processing()
    
    # Test 2: Full Document Conversion Pipeline
    conversion_test_passed = test_document_conversion()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    print(f"Table Processing Test:     {'✅ PASSED' if table_test_passed else '❌ FAILED'}")
    print(f"Document Conversion Test:  {'✅ PASSED' if conversion_test_passed else '❌ FAILED'}")
    
    if table_test_passed and conversion_test_passed:
        print("\n🎉 All tests passed! Document conversion pipeline is working correctly.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
