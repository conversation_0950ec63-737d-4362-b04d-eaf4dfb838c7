#!/usr/bin/env python3
"""
Test the row preprocessing functionality in reheader.py
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reheader import MarkdownTableHeaderAdjuster

def test_row_preprocessing():
    """Test the row preprocessing for text analysis"""
    print("="*80)
    print("TESTING ROW PREPROCESSING FOR TEXT ANALYSIS")
    print("="*80)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Test case 1: Basic whitespace cleaning
    print("Test 1: Basic whitespace cleaning")
    print("-" * 40)
    
    test_row = ['  科 目  ', ' 2024-12-31 ', '   ', ' 备注 ']
    cleaned_row = adjuster.preprocess_row_for_text_analysis(test_row)
    
    print(f"Original row: {test_row}")
    print(f"Cleaned row: {cleaned_row}")
    print(f"Expected: ['科目', '2024-12-31', '', '备注']")
    
    expected = ['科目', '2024-12-31', '', '备注']
    success1 = cleaned_row == expected
    print(f"✅ PASS" if success1 else f"❌ FAIL")
    
    print()
    
    # Test case 2: Internal whitespace removal
    print("Test 2: Internal whitespace removal")
    print("-" * 40)
    
    test_row = ['项 目', '本 期 金 额', '上\t期\n金额', '  备  注  ']
    cleaned_row = adjuster.preprocess_row_for_text_analysis(test_row)
    
    print(f"Original row: {test_row}")
    print(f"Cleaned row: {cleaned_row}")
    print(f"Expected: ['项目', '本期金额', '上期金额', '备注']")
    
    expected = ['项目', '本期金额', '上期金额', '备注']
    success2 = cleaned_row == expected
    print(f"✅ PASS" if success2 else f"❌ FAIL")
    
    print()
    
    # Test case 3: Header keyword detection with preprocessing
    print("Test 3: Header keyword detection with preprocessing")
    print("-" * 40)
    
    test_rows = [
        ['  科 目  ', ' 金额 ', ''],  # Should detect '科目'
        [' 项 目 ', ' 数量 ', ' 备注 '],  # Should detect '项目'
        ['数据', '信息', '内容'],  # Should not detect keywords
        ['  时 间  ', '  日 期  ', ''],  # Should detect '时间' and '日期'
    ]
    
    for i, row in enumerate(test_rows):
        has_keywords = adjuster.contains_header_keywords(row)
        keyword_count = adjuster.count_header_keywords(row)
        has_strong = adjuster.has_strong_header_keywords(row)
        
        print(f"Row {i+1}: {row}")
        print(f"  Has keywords: {has_keywords}")
        print(f"  Keyword count: {keyword_count}")
        print(f"  Has strong keywords: {has_strong}")
    
    print()
    
    # Test case 4: Row density calculation with preprocessing
    print("Test 4: Row density calculation with preprocessing")
    print("-" * 40)
    
    test_rows = [
        ['科目', '金额', '备注', ''],  # 3/4 = 0.75
        ['  ', '  ', '  ', '  '],  # 0/4 = 0.0
        ['项目', '  ', '数量', '备注'],  # 3/4 = 0.75
        ['  科 目  ', ' 金 额 ', '   ', ' 备 注 '],  # 3/4 = 0.75
    ]
    
    for i, row in enumerate(test_rows):
        density = adjuster.calculate_row_density(row)
        print(f"Row {i+1}: {row}")
        print(f"  Density: {density:.2f}")
    
    print()
    
    # Test case 5: String density calculation with preprocessing
    print("Test 5: String density calculation with preprocessing")
    print("-" * 40)
    
    test_rows = [
        ['科目', '金额', '备注'],  # All strings: 3/3 = 1.0
        ['项目', '1000', '2000'],  # 1 string, 2 numbers: 1/3 = 0.33
        ['  科 目  ', ' 100.5 ', ' 备 注 '],  # 2 strings, 1 number: 2/3 = 0.67
    ]
    
    for i, row in enumerate(test_rows):
        string_density = adjuster.calculate_string_density(row)
        print(f"Row {i+1}: {row}")
        print(f"  String density: {string_density:.2f}")
    
    print()
    
    return success1 and success2

def test_real_file_with_preprocessing():
    """Test with real file to see improvement"""
    print("="*80)
    print("TESTING REAL FILE WITH PREPROCESSING")
    print("="*80)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Test with the balance sheet file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Processing balance sheet file with preprocessing...")
    
    # Parse the table
    rows = adjuster.parse_table(content)
    
    print(f"Found {len(rows)} rows")
    
    # Test header detection on the first few rows
    print("\nHeader analysis for first 5 rows:")
    for i, row in enumerate(rows[:5]):
        if adjuster.is_separator_row(row):
            print(f"Row {i+1}: SEPARATOR - {row}")
            continue
            
        # Test with and without preprocessing (for comparison)
        original_text = ' '.join(row)
        cleaned_row = adjuster.preprocess_row_for_text_analysis(row)
        cleaned_text = ' '.join(cleaned_row)
        
        has_keywords = adjuster.contains_header_keywords(row)
        keyword_count = adjuster.count_header_keywords(row)
        has_strong = adjuster.has_strong_header_keywords(row)
        
        print(f"Row {i+1}: {row}")
        print(f"  Original text: '{original_text}'")
        print(f"  Cleaned text: '{cleaned_text}'")
        print(f"  Keywords: {keyword_count}, Strong: {has_strong}")
    
    # Test the header candidates
    candidates = adjuster.find_all_header_candidates(rows)
    print(f"\nTop 3 header candidates:")
    for i, (row_idx, score) in enumerate(candidates[:3]):
        row = rows[row_idx]
        cleaned_row = adjuster.preprocess_row_for_text_analysis(row)
        print(f"  {i+1}. Row {row_idx+1}: Score {score:.2f}")
        print(f"      Original: {row}")
        print(f"      Cleaned: {cleaned_row}")

def main():
    """Run all tests"""
    print("Testing Row Preprocessing for Text Analysis")
    print("="*80)
    
    try:
        success = test_row_preprocessing()
        test_real_file_with_preprocessing()
        
        if success:
            print("\n🎉 All preprocessing tests passed!")
            return 0
        else:
            print("\n❌ Some tests failed")
            return 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
