version: '3.8'

services:
  openldap:
    image: osixia/openldap:1.5.0
    container_name: test-openldap
    hostname: ldap.company.com
    ports:
      - "389:389"
      - "636:636"
    environment:
      # Basic LDAP configuration
      LDAP_ORGANISATION: "Test Company"
      LDAP_DOMAIN: "company.com"
      LDAP_BASE_DN: "dc=company,dc=com"
      
      # Admin credentials
      LDAP_ADMIN_PASSWORD: "admin123"
      LDAP_CONFIG_PASSWORD: "config123"
      
      # Security settings
      LDAP_READONLY_USER: "false"
      LDAP_RFC2307BIS_SCHEMA: "false"
      LDAP_BACKEND: "mdb"
      LDAP_TLS: "true"
      LDAP_TLS_CRT_FILENAME: "ldap.crt"
      LDAP_TLS_KEY_FILENAME: "ldap.key"
      LDAP_TLS_DH_PARAM_FILENAME: "dhparam.pem"
      LDAP_TLS_CA_CRT_FILENAME: "ca.crt"
      LDAP_TLS_ENFORCE: "false"
      LDAP_TLS_CIPHER_SUITE: "SECURE256:-VERS-SSL3.0"
      LDAP_TLS_PROTOCOL_MIN: "3.1"
      LDAP_TLS_VERIFY_CLIENT: "demand"
      LDAP_REPLICATION: "false"
      
      # Logging
      LDAP_LOG_LEVEL: "256"
      
    volumes:
      - ldap_data:/var/lib/ldap
      - ldap_config:/etc/ldap/slapd.d
      - ./ldap-setup:/container/service/slapd/assets/config/bootstrap/ldif/custom
    command: --copy-service --loglevel debug
    networks:
      - ldap-network

  ldap-admin:
    image: osixia/phpldapadmin:0.9.0
    container_name: ldap-admin
    hostname: ldap-admin.company.com
    ports:
      - "8080:80"
    environment:
      PHPLDAPADMIN_LDAP_HOSTS: "openldap"
      PHPLDAPADMIN_HTTPS: "false"
    depends_on:
      - openldap
    networks:
      - ldap-network

volumes:
  ldap_data:
  ldap_config:

networks:
  ldap-network:
    driver: bridge
