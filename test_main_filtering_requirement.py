#!/usr/bin/env python3
"""
Test the main filtering requirement from the user
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def test_main_requirement():
    """Test the main requirement: filtering the complex filename"""
    print("="*80)
    print("TESTING MAIN FILTERING REQUIREMENT")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # The main test case from the requirements
    input_filename = "季度合并财报（2024年Q4）_A1O23_Sheet1_adjusted_reformed_资产负债表.md"
    expected_output = "季度合并财报（2024年Q4）_资产负债表"
    
    print(f"Input filename: {input_filename}")
    print(f"Expected output: {expected_output}")
    print()
    
    # Test step by step
    print("Step-by-step processing:")
    print("-" * 40)
    
    # Step 1: Remove extension
    no_ext = input_filename.replace('.md', '')
    print(f"1. Remove extension: {no_ext}")
    
    # Step 2: Extract Excel cell positions
    cell_positions = reformer.extract_excel_cell_positions(input_filename)
    print(f"2. Found Excel cell positions: {cell_positions}")
    
    # Step 3: Preprocess filename
    preprocessed = reformer.preprocess_filename(input_filename)
    print(f"3. Preprocessed filename: {preprocessed}")
    
    # Step 4: Extract table name
    table_name = reformer.extract_table_name_from_filename(input_filename)
    print(f"4. Final table name: {table_name}")
    
    print()
    
    # Check the result
    success = table_name == expected_output
    print(f"Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    if success:
        print("✅ Main requirement successfully implemented!")
        print("✅ Excel cell position '_A1O23_' removed")
        print("✅ Sheet reference 'Sheet1' removed") 
        print("✅ Processing suffixes '_adjusted_reformed' removed")
        print("✅ Meaningful content '季度合并财报（2024年Q4）_资产负债表' preserved")
    else:
        print(f"❌ Expected: {expected_output}")
        print(f"❌ Got: {table_name}")
    
    return success

def test_additional_cases():
    """Test additional important cases"""
    print("\n" + "="*80)
    print("TESTING ADDITIONAL IMPORTANT CASES")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # (input, expected, description)
        ("财务报表_A1O23_资产负债表.md", "财务报表_资产负债表", "Simple Excel cell removal"),
        ("数据_Sheet1_利润表.md", "数据_利润表", "Simple sheet reference removal"),
        ("报表_adjusted_现金流量表.md", "报表_现金流量表", "Simple suffix removal"),
        ("2024年Q4财报.md", "2024年Q4财报", "Preserve Q4 (not Excel cell)"),
        ("合并报表（Q1）_资产负债表.md", "合并报表（Q1）_资产负债表", "Preserve Q1 in parentheses"),
    ]
    
    all_passed = True
    
    for i, (input_filename, expected, description) in enumerate(test_cases):
        table_name = reformer.extract_table_name_from_filename(input_filename)
        passed = table_name == expected
        
        print(f"Test {i+1}: {description}")
        print(f"  Input: {input_filename}")
        print(f"  Output: {table_name}")
        print(f"  Expected: {expected}")
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        print()
        
        if not passed:
            all_passed = False
    
    return all_passed

def test_with_real_balance_sheet():
    """Test with the real balance sheet file"""
    print("="*80)
    print("TESTING WITH REAL BALANCE SHEET FILE")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Test with the balance sheet file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_资产负债表.md"
    
    print(f"Processing real file: {filename}")
    
    # Use the hierarchical extraction
    table_data, separator_line, markdown_headers = reformer.parse_markdown_table(content)
    header_rows = table_data[:separator_line] if separator_line > 0 else []
    
    extracted_name = reformer.hierarchical_table_name_extraction(header_rows, markdown_headers, filename)
    
    print(f"Extracted table name: '{extracted_name}'")
    
    # Check if it contains the expected elements
    expected_elements = ['季度合并财报', '2024年Q4', '资产负债表']
    
    print("\nChecking for expected elements:")
    all_present = True
    for element in expected_elements:
        present = element in extracted_name
        print(f"  '{element}': {'✅ Present' if present else '❌ Missing'}")
        if not present:
            all_present = False
    
    # Check for unwanted elements
    unwanted_elements = ['A1O23', 'Sheet1', 'adjusted', 'reformed']
    
    print("\nChecking for unwanted elements:")
    no_unwanted = True
    for element in unwanted_elements:
        present = element in extracted_name
        print(f"  '{element}': {'❌ Present' if present else '✅ Absent'}")
        if present:
            no_unwanted = False
    
    success = all_present and no_unwanted
    print(f"\nReal file test: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    return success

def main():
    """Run all tests"""
    print("Testing Main Filtering Requirement")
    print("="*80)
    
    try:
        test1 = test_main_requirement()
        test2 = test_additional_cases()
        test3 = test_with_real_balance_sheet()
        
        print("="*80)
        print("FINAL SUMMARY")
        print("="*80)
        print(f"Main requirement test: {'✅ PASS' if test1 else '❌ FAIL'}")
        print(f"Additional cases test: {'✅ PASS' if test2 else '❌ FAIL'}")
        print(f"Real file test: {'✅ PASS' if test3 else '❌ FAIL'}")
        
        if test1:  # Main requirement is the most important
            print("\n🎉 Main filtering requirement successfully implemented!")
            print("✅ Excel cell positions filtered out")
            print("✅ Sheet references removed")
            print("✅ Processing suffixes eliminated")
            print("✅ Meaningful content preserved")
            return 0
        else:
            print("\n❌ Main filtering requirement not fully met")
            return 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
