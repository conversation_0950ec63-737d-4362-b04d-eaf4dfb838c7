#!/usr/bin/env python3
"""
Test script for the enhanced header vs metadata classification system in reform.py
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer
import json
import re

def test_time_keyword_classification():
    """Test the enhanced classification of '时间' keyword"""
    reformer = MarkdownTableReformer()

    # Test case 1: 时间 as table header (the EXACT original problematic case)
    test_content_1 = """|  |  |  |  |  |
| --- | --- | --- | --- | --- |
| 1、全量客群-规模、余额 |  |  |  |  |
| 放款时间 | 当月放款金额(元） | 当月放款笔数 | 在贷本金(元） |  |
| 2021-09-01 | 2369184103.11 | 460151 | 14163781481.84 |  |
| 2021-10-01 | 2264739324 | 443158 | 14093419476.16 |  |"""
    
    print("=== Test Case 1: 时间 as table header ===")
    result_1 = reformer.process_table(test_content_1, "test_table_header.md")
    print("Result:")
    print(result_1[:200] + "..." if len(result_1) > 200 else result_1)
    
    # Extract metadata from result
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result_1)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        print(f"Metadata: {metadata.get('metadata', [])}")
        
        # Check if table headers are incorrectly in metadata
        metadata_list = metadata.get('metadata', [])
        header_keywords_in_metadata = [item for item in metadata_list if any(keyword in item for keyword in ['放款时间', '当月放款金额', '当月放款笔数', '在贷本金'])]
        
        if header_keywords_in_metadata:
            print(f"❌ FAIL: Table headers found in metadata: {header_keywords_in_metadata}")
        else:
            print("✅ PASS: No table headers found in metadata")
    else:
        print("❌ FAIL: No metadata found in result")
    
    print("\n" + "="*60 + "\n")
    
    # Test case 2: 时间 as metadata (in header section before separator)
    test_content_2 = """| 利润表 | | |
| 时间：2024年9月 | | 单位：元 |
| 编制单位：测试公司 | | |
| --- | --- | --- |
| 项目 | 本期金额 | 上期金额 |
| 营业收入 | 1000000 | 900000 |"""

    print("=== Test Case 2: 时间 as metadata ===")

    # Debug: Let's analyze the rows manually
    print("Debug: Analyzing rows manually...")
    table_data, separator_line, _ = reformer.parse_markdown_table(test_content_2)
    print(f"Table data: {table_data}")
    print(f"Separator line: {separator_line}")
    header_rows = table_data[:separator_line]
    print(f"Header rows: {header_rows}")

    for i, row in enumerate(header_rows):
        scores = reformer.calculate_classification_score(row, i, len(header_rows))
        print(f"Row {i}: {row} -> Scores: {scores}")

        # Check individual features
        density = reformer.analyze_row_density(row)
        has_colons = reformer.has_colon_separators(row)
        has_dates = reformer.has_date_patterns(row)
        print(f"  Features: density={density:.2f}, colons={has_colons}, dates={has_dates}")

    result_2 = reformer.process_table(test_content_2, "test_metadata.md")
    print("Result:")
    print(result_2[:300] + "..." if len(result_2) > 300 else result_2)
    
    # Extract metadata from result
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result_2)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        print(f"Metadata: {metadata.get('metadata', [])}")
        
        # Check if time metadata is correctly captured
        metadata_list = metadata.get('metadata', [])
        time_metadata = [item for item in metadata_list if '时间' in item or '2024年9月' in item]
        
        if time_metadata:
            print(f"✅ PASS: Time metadata correctly captured: {time_metadata}")
        else:
            print("❌ FAIL: Time metadata not found")
    else:
        print("❌ FAIL: No metadata found in result")
    
    print("\n" + "="*60 + "\n")

def test_context_aware_features():
    """Test the context-aware features"""
    reformer = MarkdownTableReformer()
    
    # Test row density analysis
    print("=== Testing Row Density Analysis ===")
    dense_row = ['项目', '金额', '备注', '状态']
    sparse_row = ['时间：2024年', '', '', '']
    
    dense_density = reformer.analyze_row_density(dense_row)
    sparse_density = reformer.analyze_row_density(sparse_row)
    
    print(f"Dense row density: {dense_density}")
    print(f"Sparse row density: {sparse_density}")
    
    assert dense_density > sparse_density, "Dense row should have higher density"
    print("✅ PASS: Row density analysis working correctly")
    
    # Test colon separator detection
    print("\n=== Testing Colon Separator Detection ===")
    colon_row = ['编制单位：测试公司', '单位：元']
    no_colon_row = ['项目', '金额']
    
    has_colon = reformer.has_colon_separators(colon_row)
    no_colon = reformer.has_colon_separators(no_colon_row)
    
    print(f"Row with colons: {has_colon}")
    print(f"Row without colons: {no_colon}")
    
    assert has_colon and not no_colon, "Colon detection should work correctly"
    print("✅ PASS: Colon separator detection working correctly")
    
    # Test date pattern detection
    print("\n=== Testing Date Pattern Detection ===")
    date_row = ['时间：2024年9月', '截至：2024-09-30']
    no_date_row = ['项目', '金额']
    
    has_date = reformer.has_date_patterns(date_row)
    no_date = reformer.has_date_patterns(no_date_row)
    
    print(f"Row with dates: {has_date}")
    print(f"Row without dates: {no_date}")
    
    assert has_date and not no_date, "Date pattern detection should work correctly"
    print("✅ PASS: Date pattern detection working correctly")
    
    print("\n" + "="*60 + "\n")

def test_scoring_system():
    """Test the scoring system"""
    reformer = MarkdownTableReformer()
    
    print("=== Testing Scoring System ===")
    
    # Test header-like row
    header_row = ['放款时间', '当月放款金额', '当月放款笔数', '在贷本金']
    header_scores = reformer.calculate_classification_score(header_row, 2, 5)
    print(f"Header row scores: {header_scores}")
    
    # Test metadata-like row
    metadata_row = ['时间：2024年9月', '', '单位：元', '']
    metadata_scores = reformer.calculate_classification_score(metadata_row, 1, 5)
    print(f"Metadata row scores: {metadata_scores}")
    
    # Verify that header row scores higher as header
    assert header_scores['header_score'] > header_scores['metadata_score'], "Header row should score higher as header"
    
    # Verify that metadata row scores higher as metadata
    assert metadata_scores['metadata_score'] > metadata_scores['header_score'], "Metadata row should score higher as metadata"
    
    print("✅ PASS: Scoring system working correctly")
    
    print("\n" + "="*60 + "\n")

def main():
    """Run all tests"""
    print("Testing Enhanced Header vs Metadata Classification System")
    print("="*60)
    
    try:
        test_context_aware_features()
        test_scoring_system()
        test_time_keyword_classification()
        
        print("🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
