"""
Markdown Table Header Adjuster

This script implements an algorithm to adjust table header separator rows in markdown files
based on data type analysis of the columns.

Algorithm:
1. Preprocess: Remove completely blank lines
2. Locate the header separator row (row with |---|---|...)
3. Use header keywords to identify potential header positions
4. Move the separator row down one by one and calculate the data type density for each column
5. Find the position where there's a significant change in the density trend
6. Output the table with the separator at the optimal position
"""

from pathlib import Path
import re
import os
import logging
from typing import List, Dict, Tuple, Optional
from collections import Counter

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    FileProcessingException, DataProcessingException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

class MarkdownTableHeaderAdjuster:
    """Class for adjusting markdown table header separator rows based on data type analysis"""

    @log_and_reraise(logger, "MarkdownTableHeaderAdjuster initialization")
    def __init__(self):
        """Initialize MarkdownTableHeaderAdjuster with unified error handling"""
        set_operation_context("markdown_table_header_adjuster_init")

        with error_boundary("pattern configuration", LayerType.UTILITY):
            try:
                self.placeholder_patterns = [
                    r'^$',                  # Empty
                    r'^\s*$',               # Whitespace only
                    r'^-$',                 # Single dash
                    r'^N/?A$',              # NA or N/A
                    r'^None$',              # None
                    r'^null$',              # null
                    r'^undefined$',         # undefined
                    r'^\s*-\s*$',           # Dash with whitespace
                    r'^\s*N/?A\s*$',        # NA or N/A with whitespace
                ]

                # Compile patterns for validation
                for pattern in self.placeholder_patterns:
                    re.compile(pattern)

            except re.error as e:
                raise DataProcessingException(
                    message="Failed to compile placeholder patterns",
                    details=f"Regex compilation error: {str(e)}",
                    original_exception=e,
                    suggested_action="Check regex pattern syntax"
                )

        with error_boundary("keyword configuration", LayerType.UTILITY):
            # Import header keywords from reform.py for header detection
            self.header_keywords = ['项目', '科目', '行次', '序号', '期初', '上年', '期末', '当期', '期初', '年初', '年末', '月初', '月末', '季初', '季末','日期', '时间', '月度', '季度', '年度', '月份', '年份', '期间', '本月金额', '本季金额', '本年金额', '本月发生', '本季发生', '本年发生', '本月累计', '本季累计', '本年累计']
            self.debug_output = True  # Set to True to output debug information
            self.debug_file = None  # For specific file debugging
            logger.debug("Initialized MarkdownTableHeaderAdjuster")

    def preprocess_row_for_text_analysis(self, row: List[str]) -> List[str]:
        """
        Preprocess row cells for text analysis by cleaning whitespace

        Args:
            row: List of cell contents

        Returns:
            List of cleaned cell contents
        """
        if not row:
            return []

        cleaned_row = []
        for cell in row:
            if cell is None:
                cleaned_row.append('')
            else:
                # Strip leading/trailing whitespace and remove internal spaces
                cleaned_cell = cell.strip()
                # Remove internal whitespace characters (spaces, tabs, newlines)
                cleaned_cell = re.sub(r'\s+', '', cleaned_cell)
                cleaned_row.append(cleaned_cell)

        return cleaned_row

    def preprocess(self, content: str) -> Tuple[int, str]:
        """
        Remove completely blank lines from the content and
        remove rows where all cells are empty or contain only whitespace
        Preserves Markdown section headers (lines starting with #)
        """
        # First, split the content into lines
        lines = content.split('\n')
        
        # Remove lines that are completely blank (no characters or only whitespace)
        filtered_lines = [line for line in lines if line.strip()]
        
        # Parse the table to identify rows where all cells are empty
        result_lines = []
        for line in filtered_lines:
            if line.lstrip().startswith('#'):
                # Preserve Markdown headers
                result_lines.append(line)
            elif '|' in line:
                # This is a table row, check if all cells are empty
                cells = [cell.strip() for cell in line.strip('|').split('|')]
                # Keep the row if at least one cell has content
                if any(cells):
                    result_lines.append(line)
            else:
                # Not a table row, keep it
                result_lines.append(line)
        
        # Join the filtered lines back into a single string
        return (len(lines) - len(result_lines), '\n'.join(result_lines))
    
    def parse_table(self, content: str) -> List[List[str]]:
        """Parse markdown table into a list of rows, each row being a list of cells"""
        lines = content.strip().split('\n')
        rows = []
        
        for line in lines:
            if '|' in line:
                # Remove leading and trailing | and split by |
                cells = [cell.strip() for cell in line.strip('|').split('|')]
                rows.append(cells)
        
        return rows
    
    def is_separator_row(self, row: List[str]) -> bool:
        """Check if a row is a separator row (contains only dashes and colons)"""
        if not row:
            return False
        
        for cell in row:
            cell = cell.strip()
            if cell and not re.match(r'^:?-+:?$', cell):
                return False
        
        return True
    
    def find_separator_rows(self, rows: List[List[str]]) -> List[int]:
        """Find all potential separator rows in the table"""
        separator_indices = []

        for i, row in enumerate(rows):
            if self.is_separator_row(row):
                separator_indices.append(i)

        return separator_indices

    def contains_header_keywords(self, row: List[str]) -> bool:
        """
        Check if a row contains header keywords
        """
        cleaned_row = self.preprocess_row_for_text_analysis(row)
        # Check each cell individually for keywords
        for cell in cleaned_row:
            for keyword in self.header_keywords:
                if keyword in cell:
                    return True
        return False

    def find_header_keyword_positions(self, rows: List[List[str]]) -> List[int]:
        """
        Find rows that contain header keywords, which are likely to be actual header rows
        """
        header_positions = []
        for i, row in enumerate(rows):
            if self.contains_header_keywords(row):
                header_positions.append(i)
        return header_positions

    def detect_column_mismatch(self, rows: List[List[str]], separator_index: int) -> bool:
        """
        Detect if the separator has fewer columns than the actual data rows,
        indicating it's in the wrong position
        """
        if separator_index >= len(rows) - 1:
            return False

        separator_cols = len(rows[separator_index])

        # Check the next few rows to see if they have more columns
        for i in range(separator_index + 1, min(separator_index + 5, len(rows))):
            if len(rows[i]) > separator_cols:
                return True

        return False

    def calculate_row_density(self, row: List[str]) -> float:
        """
        Calculate the density of non-empty text cells in a row
        Returns a value between 0.0 and 1.0
        """
        if not row:
            return 0.0

        cleaned_row = self.preprocess_row_for_text_analysis(row)
        non_empty_count = sum(1 for cell in cleaned_row if cell)
        return non_empty_count / len(row)

    def calculate_string_density(self, row: List[str]) -> float:
        """
        Calculate the density of string (non-numeric) content in a row
        Headers typically have high string density
        """
        if not row:
            return 0.0

        cleaned_row = self.preprocess_row_for_text_analysis(row)
        string_count = 0
        total_non_empty = 0

        for cell in cleaned_row:
            if cell:  # Non-empty cell
                total_non_empty += 1
                cell_type = self.detect_type(cell)
                if cell_type == 'string':
                    string_count += 1

        if total_non_empty == 0:
            return 0.0

        return string_count / total_non_empty

    def count_header_keywords(self, row: List[str]) -> int:
        """
        Count the number of header keywords in a row
        """
        cleaned_row = self.preprocess_row_for_text_analysis(row)
        count = 0
        # Check each cell individually for keywords
        for cell in cleaned_row:
            for keyword in self.header_keywords:
                if keyword in cell:
                    count += 1
        return count

    def has_strong_header_keywords(self, row: List[str]) -> bool:
        """
        Check if row contains strong header keywords like '项目' and '行次'
        """
        cleaned_row = self.preprocess_row_for_text_analysis(row)
        strong_keywords = ['项目', '行次', '序号', '科目', '月份', '年份', '本季', '当期', '期初', '期末', '上年', '小计', '合计', '总计']
        # Check each cell individually for strong keywords
        for cell in cleaned_row:
            for keyword in strong_keywords:
                if keyword in cell:
                    return True
        return False


    def calculate_comprehensive_header_score(self, row: List[str], row_index: int, total_rows: int, rows: List[List[str]]) -> float:
        """
        Calculate a comprehensive score for how likely a row is to be a header

        Factors considered:
        1. Header keyword presence and count
        2. Row density (non-empty cells)
        3. String density (text vs numbers)
        4. Position in table
        5. Following data rows pattern
        6. Strong header keywords
        """
        if row_index >= total_rows - 1:  # Need at least 1 row after for data
            return 0.0

        score = 0.0

        # 1. Header keyword scoring (weighted heavily)
        keyword_count = self.count_header_keywords(row)
        if keyword_count > 0:
            score += keyword_count * 15  # Increased from 10

        # 2. Strong header keywords get extra points
        if self.has_strong_header_keywords(row):
            score += 25  # Significant bonus for strong keywords

        # 2.5. Enhanced keyword scoring - differentiate between keyword types
        cleaned_row = self.preprocess_row_for_text_analysis(row)
        row_text = ' '.join(cleaned_row)
        
        # Data category keywords (stronger indicators) - these indicate actual data structure
        data_category_keywords = ['科目', '项目', '行次', '序号']
        data_category_count = sum(1 for keyword in data_category_keywords if keyword in row_text)
        if data_category_count > 0:
            score += data_category_count * 35  # Extra bonus for data category keywords
        
        # Time period keywords (weaker indicators) - these are often just time headers
        time_period_keywords = ['月度', '季度', '年度', '月份', '年份']
        time_period_count = sum(1 for keyword in time_period_keywords if keyword in row_text)
        if time_period_count > 0 and data_category_count == 0:  # Only bonus if no data category keywords
            score += time_period_count * 5  # Much smaller bonus for time period keywords

        # 3. Row density scoring
        row_density = self.calculate_row_density(row)
        score += row_density * 10  # Favor rows with many filled cells

        # 4. String density scoring (headers are mostly text)
        string_density = self.calculate_string_density(row)
        score += string_density * 8  # Favor rows with high text content

        # 5. Column count scoring
        if len(row) > 1:
            score += min(len(row), 10)  # Cap at 10 to avoid over-weighting wide tables

        # 6. Position scoring (slight preference for earlier rows)
        position_ratio = row_index / max(total_rows - 1, 1)
        if position_ratio < 0.3:  # Early in table
            score += 3
        elif position_ratio < 0.6:  # Middle of table
            score += 1

        # 7. Following data rows analysis
        data_rows_after = 0
        numeric_rows_after = 0

        for j in range(row_index + 1, min(row_index + 5, total_rows)):
            if j < len(rows) and len(rows[j]) >= len(row):  # Same or more columns
                # Check if it looks like data
                has_numbers = any(self.detect_type(cell) == 'number' for cell in rows[j])
                if has_numbers:
                    numeric_rows_after += 1
                data_rows_after += 1

        # Bonus for being followed by data-like rows
        if data_rows_after > 0:
            data_ratio = numeric_rows_after / data_rows_after
            score += data_ratio * 5  # Bonus for being followed by numeric data

        return score

    def find_all_header_candidates(self, rows: List[List[str]]) -> List[Tuple[int, float]]:
        """
        Find ALL potential header row candidates with their scores
        Returns list of (row_index, score) tuples sorted by score descending
        """
        candidates = []

        for i, row in enumerate(rows):
            if i >= len(rows) - 1:  # Need at least 1 row after
                continue

            # Skip separator rows
            if self.is_separator_row(row):
                continue

            # Calculate comprehensive score
            score = self.calculate_comprehensive_header_score(row, i, len(rows), rows)

            # Only consider rows with meaningful content
            if score > 0:
                candidates.append((i, score))

        # Sort by score descending
        candidates.sort(key=lambda x: x[1], reverse=True)

        return candidates

    def detect_multi_level_header_sequence(self, rows: List[List[str]], candidates: List[Tuple[int, float]]) -> Optional[Tuple[int, int]]:
        """
        Detect if consecutive high-scoring header candidates form a multi-level header structure

        Returns:
            Tuple of (start_row_index, end_row_index) if multi-level header detected, None otherwise
        """
        if len(candidates) < 2:
            return None

        # Look for consecutive header candidates with good scores
        # Use more balanced thresholds to avoid missing real headers while still preventing false positives
        header_threshold = 60.0  # Higher threshold for multi-level detection
        max_sequence_length = 3  # Limit multi-level headers to at most 3 rows

        for i in range(len(candidates) - 1):
            current_row_idx, current_score = candidates[i]
            next_row_idx, next_score = candidates[i + 1]

            # Check if both have very high scores and are truly consecutive (within 1-2 rows)
            # Require both scores to be quite high for multi-level detection
            if (current_score >= header_threshold and
                next_score >= header_threshold and
                next_row_idx - current_row_idx <= 2 and  # Must be close together
                next_row_idx - current_row_idx >= 1 and  # But not the same row
                min(current_score, next_score) >= 50.0):  # Both must be high quality

                # Check if the rows actually look like header content
                current_row = rows[current_row_idx]
                next_row = rows[next_row_idx]

                # Verify that both rows have significant text content (not just numbers)
                current_string_density = self.calculate_string_density(current_row)
                next_string_density = self.calculate_string_density(next_row)

                if current_string_density < 0.6 or next_string_density < 0.6:
                    continue  # Skip if either row is mostly numeric (likely data, not header)

                # Additional check: both rows should have header keywords
                current_has_keywords = self.contains_header_keywords(current_row)
                next_has_keywords = self.contains_header_keywords(next_row)
                
                if not (current_has_keywords and next_has_keywords):
                    continue  # Skip if either row doesn't have header keywords

                # Additional check: both rows should have strong header keywords
                current_has_strong_keywords = self.has_strong_header_keywords(current_row)
                next_has_strong_keywords = self.has_strong_header_keywords(next_row)
                
                if not (current_has_strong_keywords and next_has_strong_keywords):
                    continue  # Skip if either row doesn't have strong header keywords

                # Find the end of the sequence (but limit to max_sequence_length)
                end_row_idx = next_row_idx
                sequence_length = 2  # We already have current and next

                for j in range(i + 2, min(i + max_sequence_length, len(candidates))):
                    candidate_row_idx, candidate_score = candidates[j]
                    if (candidate_score >= header_threshold and
                        candidate_row_idx - end_row_idx <= 2 and
                        candidate_row_idx - end_row_idx >= 1):

                        # Check string density for this candidate too
                        candidate_row = rows[candidate_row_idx]
                        candidate_string_density = self.calculate_string_density(candidate_row)
                        candidate_has_keywords = self.contains_header_keywords(candidate_row)
                        candidate_has_strong_keywords = self.has_strong_header_keywords(candidate_row)
                        
                        if (candidate_string_density >= 0.6 and 
                            candidate_has_keywords and 
                            candidate_has_strong_keywords):
                            end_row_idx = candidate_row_idx
                            sequence_length += 1
                        else:
                            break  # Stop if we hit a mostly numeric row or no keywords
                    else:
                        break

                # Only return if we have a reasonable multi-level header (2-3 rows)
                if sequence_length >= 2 and sequence_length <= max_sequence_length:
                    logger.debug(f"Multi-level header sequence detected: rows {current_row_idx + 1} to {end_row_idx + 1} ({sequence_length} rows)")
                    logger.debug(f"  Start row score: {current_score:.2f}, End row score: {next_score:.2f}")
                    return (current_row_idx, end_row_idx)

        return None

    def find_best_header_row(self, rows: List[List[str]]) -> Optional[int]:
        """
        Enhanced multi-pass header detection that evaluates ALL potential header rows
        before making a final determination, with support for multi-level headers
        """
        
        # Get all header candidates with comprehensive scoring
        candidates = self.find_all_header_candidates(rows)

        if not candidates:
            return None

        # Log all candidates for debugging
        logger.debug("Header candidates (row_index, score):")
        for i, (row_idx, score) in enumerate(candidates[:5]):  # Show top 5
            row_preview = ' | '.join(rows[row_idx][:3])  # Show first 3 cells
            logger.debug(f"  {i+1}. Row {row_idx+1}: {score:.2f} - [{row_preview}...]")

        # Check for multi-level header structure
        multi_level_header = self.detect_multi_level_header_sequence(rows, candidates)

        if multi_level_header:
            start_row, end_row = multi_level_header
            logger.debug(f"Using multi-level header structure: rows {start_row + 1} to {end_row + 1}")
            # Return the end row of the multi-level header as the "best" header
            # The separator should be placed after this row
            return end_row
        else:
            # Return the best single candidate
            best_row_idx, best_score = candidates[0]
            logger.debug(f"Selected best single header row: {best_row_idx+1} with score {best_score:.2f}")
            return best_row_idx
    
    def is_placeholder(self, value: str, context: str = 'data') -> bool:
        """
        Check if a value is a placeholder (empty, NA, etc.)

        Args:
            value: The value to check
            context: 'header' or 'data' - affects how empty values are treated
        """
        value = value.strip()

        for pattern in self.placeholder_patterns:
            if re.match(pattern, value, re.IGNORECASE):
                return True

        return False

    def detect_type(self, value: str, context: str = 'data', dominant_type: Optional[str] = None) -> Optional[str]:
        """
        Detect the data type of a value

        Args:
            value: The value to analyze
            context: 'header' or 'data' - affects how placeholders are handled
            dominant_type: The dominant type in the column (used for data context)

        Returns: 'number', 'string', 'date', or None for placeholders
        """
        if self.is_placeholder(value, context):
            if context == 'header':
                # For header analysis, treat empty values as different types to reduce density
                return 'empty'
            elif context == 'data' and dominant_type:
                # For data analysis, treat empty values as same type as dominant type to increase density
                return dominant_type
            else:
                return None

        # Check if it's a number (integer or float)
        # Handle various number formats including commas and negative signs
        if re.match(r'^-?\s*[\d,]+\.?\d*\s*$', value) or re.match(r'^-?\s*\d*\.\d+\s*$', value):
            return 'number'

        # Check if it's a date (various formats)
        date_patterns = [
            r'^\d{4}-\d{2}-\d{2}$',                  # YYYY-MM-DD
            r'^\d{1,2}/\d{1,2}/\d{2,4}$',            # MM/DD/YYYY or DD/MM/YYYY
            r'^\d{4}年\d{1,2}月\d{1,2}日$',           # Chinese date format
            r'^\d{4}年\d{1,2}月$',                    # Chinese year-month format
            r'^\d{4}年Q[1-4]$',                      # Year and quarter format
        ]

        for pattern in date_patterns:
            if re.match(pattern, value):
                return 'date'

        # If not a number or date, it's a string
        return 'string'
    
    def analyze_column_types(self, rows: List[List[str]], separator_index: int) -> Dict[int, Dict[str, float]]:
        """
        Enhanced analysis of data types in each column below the separator row
        Uses differential empty value handling for better header/data distinction

        Returns a dictionary mapping column index to a dictionary of type densities
        Example: {0: {'number': 0.8, 'string': 0.2}, 1: {'number': 1.0}}
        """
        if separator_index >= len(rows) - 1:
            # No data rows below separator
            return {}

        data_rows = rows[separator_index + 1:]
        column_types = {}

        # Determine the number of columns from the data rows, not the separator row
        # The separator row might have fewer columns than the actual data
        num_columns = 0
        if data_rows:
            # Find the maximum number of columns in the data rows
            num_columns = max(len(row) for row in data_rows)

        # If no data rows or all empty, fallback to separator row
        if num_columns == 0:
            num_columns = len(rows[separator_index])

        # First pass: determine dominant types for each column (ignoring placeholders)
        column_dominant_types = {}
        for col_idx in range(num_columns):
            type_counter = Counter()

            for row in data_rows:
                if col_idx < len(row):
                    cell_value = row[col_idx].strip()
                    cell_type = self.detect_type(cell_value, context='data')

                    if cell_type and cell_type != 'empty':  # Ignore placeholders in first pass
                        type_counter[cell_type] += 1

            # Find dominant type
            if type_counter:
                dominant_type = type_counter.most_common(1)[0][0]
                column_dominant_types[col_idx] = dominant_type
            else:
                column_dominant_types[col_idx] = 'string'  # Default fallback

        # Second pass: calculate densities with enhanced empty value handling
        for col_idx in range(num_columns):
            type_counter = Counter()
            total_values = 0
            dominant_type = column_dominant_types[col_idx]

            for row in data_rows:
                if col_idx < len(row):
                    cell_value = row[col_idx].strip()
                    # Use enhanced detect_type with dominant type for data context
                    cell_type = self.detect_type(cell_value, context='data', dominant_type=dominant_type)

                    if cell_type:  # Count all non-None types
                        type_counter[cell_type] += 1
                        total_values += 1

            # Calculate type densities
            type_densities = {}
            if total_values > 0:
                for type_name, count in type_counter.items():
                    type_densities[type_name] = count / total_values
            else:
                # If no valid data, default to TEXT with density 1.0
                type_densities['TEXT'] = 1.0

            column_types[col_idx] = type_densities

        return column_types

    def analyze_header_types(self, rows: List[List[str]], separator_index: int) -> Dict[int, Dict[str, float]]:
        """
        Analyze data types in each column above the separator row (potential header rows)
        Uses differential empty value handling to reduce density for header rows

        Returns a dictionary mapping column index to a dictionary of type densities
        """
        if separator_index <= 0:
            # No header rows above separator
            return {}

        header_rows = rows[:separator_index]
        column_types = {}

        # Determine the number of columns from the separator row
        num_columns = len(rows[separator_index])

        for col_idx in range(num_columns):
            type_counter = Counter()
            total_values = 0

            for row in header_rows:
                if col_idx < len(row):
                    cell_value = row[col_idx].strip()
                    # Use header context - empty values treated as different types
                    cell_type = self.detect_type(cell_value, context='header')

                    if cell_type:  # Count all non-None types
                        type_counter[cell_type] += 1
                        total_values += 1

            # Calculate type densities
            type_densities = {}
            if total_values > 0:
                for type_name, count in type_counter.items():
                    type_densities[type_name] = count / total_values
            else:
                # If no valid data, default to low density
                type_densities['empty'] = 1.0

            column_types[col_idx] = type_densities

        return column_types

    def calculate_density_score(self, column_types: Dict[int, Dict[str, float]]) -> float:
        """
        Calculate the density score as the sum of maximum type densities across all columns
        divided by the number of columns
        """
        if not column_types:
            return 0.0
        
        total_density = 0.0
        
        for col_idx, type_densities in column_types.items():
            if type_densities:
                # Find the dominant type and its density
                dominant_type = max(type_densities.items(), key=lambda x: x[1])
                total_density += dominant_type[1]
        
        # Calculate the average density across all columns
        return total_density / len(column_types)
    
    def print_column_type_info(self, column_types: Dict[int, Dict[str, float]], score: float) -> None:
        """Print debug information about column types and the overall density score"""
        if not self.debug_output:
            return
        
        logger.debug("\nColumn Type Analysis:")
        logger.debug("-" * 50)
        
        for col_idx, type_densities in column_types.items():
            logger.debug(f"Column {col_idx + 1}:")
            if type_densities:
                for type_name, density in sorted(type_densities.items(), key=lambda x: x[1], reverse=True):
                    logger.debug(f"  - {type_name}: {density:.4f}")
            else:
                logger.debug("  - No valid data")
        
        logger.debug("-" * 50)
        logger.debug(f"Overall Density Score: {score:.4f}")
        logger.debug("-" * 50)
    
    def detect_trend_change(self, scores: List[float]) -> int:
        """
        Detect the first significant trend change in the density score trend
        Returns the index where the first significant trend change occurs
        """
        if len(scores) < 3:
            return 0

        # Calculate dynamic threshold based on score range and enhanced algorithm characteristics
        score_range = max(scores) - min(scores)

        # Enhanced algorithm produces larger density differences, so we need a more adaptive threshold
        if score_range > 0.1:
            # Large score range indicates clear header/data distinction
            dynamic_threshold = max(0.025, score_range * 0.15)
        elif score_range > 0.05:
            # Medium score range
            dynamic_threshold = max(0.020, score_range * 0.20)
        else:
            # Small score range - use more sensitive threshold
            dynamic_threshold = max(0.012, score_range * 0.25)

        logger.debug(f"Score range: {score_range:.4f}, Dynamic threshold: {dynamic_threshold:.4f}")

        # Calculate first derivatives (differences between consecutive scores)
        first_deriv = [scores[i+1] - scores[i] for i in range(len(scores)-1)]

        # Find the first point where trend changes direction significantly
        for i in range(1, len(first_deriv)):
            # Check if trend direction changed AND change exceeds dynamic threshold
            if (first_deriv[i-1] * first_deriv[i] < 0 and
                abs(first_deriv[i] - first_deriv[i-1]) > dynamic_threshold):

                logger.debug(f"Trend change detected at position {i} with change magnitude: {abs(first_deriv[i] - first_deriv[i-1]):.4f}")
                return min(5, i)  # Return first significant change

        # If no clear changes, return the maximum score position
        max_score = max(scores)
        return min(5, scores.index(max_score))
    
    def adjust_table(self, content: str) -> str:
        """
        Main method to adjust the table header separator row
        
        Algorithm:
        1. Preprocess and parse the table
        2. Find the initial separator row
        3. Try moving the separator row down one by one and calculate the data type density for each column
        4. Find the position where there's a significant change in the density trend
        5. Output the table with the separator at the optimal position
        """
        logger.debug("Starting table adjustment")
        # Preprocess the content
        offset, content = self.preprocess(content)
        
        # Split into lines and preserve non-table content (headers)
        lines = content.split('\n')
        non_table_lines = []
        table_lines = []
        
        for line in lines:
            if line.lstrip().startswith('#') or not '|' in line:
                non_table_lines.append(line)
            else:
                table_lines.append(line)
        
        # Parse the table from table lines only
        rows = self.parse_table('\n'.join(table_lines))
        if not rows:
            return content
        
        # Find the initial separator row
        separator_indices = self.find_separator_rows(rows)
        if not separator_indices:
            return content

        # Start with the initial separator row (usually the first one found)
        initial_sep_idx = separator_indices[0]

        # Enhanced multi-pass header detection
        logger.debug("=== Enhanced Multi-Pass Header Detection ===")

        # Find ALL header candidates with comprehensive scoring
        header_candidates = self.find_all_header_candidates(rows)

        # Find rows with header keywords (legacy method for comparison)
        header_keyword_positions = self.find_header_keyword_positions(rows)

        # Check for column mismatch (separator has fewer columns than data)
        has_column_mismatch = self.detect_column_mismatch(rows, initial_sep_idx)

        # Get the best header row from comprehensive analysis (may be end of multi-level header)
        best_header_row = None
        multi_level_header_info = None
        if header_candidates:
            # Check for multi-level header structure first
            multi_level_header_info = self.detect_multi_level_header_sequence(rows, header_candidates)

            if multi_level_header_info:
                start_row, end_row = multi_level_header_info
                best_header_row = end_row  # Use the end of the multi-level header
                logger.debug(f"Multi-level header detected: rows {start_row + 1} to {end_row + 1}")
                logger.debug(f"Best header row (end of multi-level): {best_header_row + 1}")
            else:
                best_header_row = header_candidates[0][0]  # Top candidate row index
                logger.debug(f"Best header row from comprehensive analysis: {best_header_row + 1} (score: {header_candidates[0][1]:.2f})")

        # Build priority positions based on comprehensive analysis
        priority_positions = []

        # Add position after the best header row (which may be end of multi-level header)
        if best_header_row is not None:
            separator_pos = best_header_row + 1
            if separator_pos < len(rows) and separator_pos not in separator_indices:
                priority_positions.append(separator_pos)
                if multi_level_header_info:
                    start_row, end_row = multi_level_header_info
                    logger.debug(f"Added priority position {separator_pos + 1} after multi-level header (rows {start_row + 1}-{end_row + 1})")
                else:
                    logger.debug(f"Added priority position {separator_pos + 1} after best header candidate {best_header_row + 1}")

        # Also add positions after other top header candidates for comparison
        for row_idx, score in header_candidates[:3]:  # Consider top 3 candidates
            separator_pos = row_idx + 1
            if separator_pos < len(rows) and separator_pos not in separator_indices and separator_pos not in priority_positions:
                priority_positions.append(separator_pos)
                logger.debug(f"Added additional priority position {separator_pos} after header candidate {row_idx + 1} (score: {score:.2f})")

        # Also include legacy header keyword positions for backward compatibility
        if header_keyword_positions:
            for pos in header_keyword_positions:
                separator_pos = pos + 1
                if separator_pos < len(rows) and separator_pos not in separator_indices and separator_pos not in priority_positions:
                    priority_positions.append(separator_pos)
            logger.debug(f"Added legacy header keyword positions: {header_keyword_positions}")

        # Handle column mismatch
        if has_column_mismatch:
            logger.debug(f"Column mismatch detected: {has_column_mismatch}")
            if best_header_row is not None and best_header_row + 1 < len(rows):
                target_position = best_header_row + 1
                if target_position not in priority_positions:
                    priority_positions.append(target_position)
                    logger.debug(f"Added position {target_position} after best header row due to column mismatch")
        
        # Try each possible separator position and calculate the density score
        scores = []
        positions = []
        column_types_list = []

        logger.debug("\nTrying different separator positions:")
        logger.debug("=" * 70)

        # Determine positions to test - prioritize header keyword positions if found
        test_positions = []
        if priority_positions:
            # Test priority positions first, then a few positions around the initial separator
            test_positions.extend(priority_positions)
            # Add some positions around the initial separator for comparison
            for sep_idx in range(initial_sep_idx, min(initial_sep_idx + 5, len(rows) - 1)):
                if sep_idx not in test_positions:
                    test_positions.append(sep_idx)
        else:
            # No header keywords found, use original algorithm
            test_positions = list(range(initial_sep_idx, min(initial_sep_idx + 10, len(rows) - 1)))

        # Only check a few rows after the initial separator
        for sep_idx in test_positions:
            # Create a new table with the separator at this position
            test_rows = rows.copy()
            
            # Make this row a separator row if it's not already
            if sep_idx not in separator_indices:
                test_rows[sep_idx] = ['---'] * len(test_rows[sep_idx])
            
            # Analyze column types
            column_types = self.analyze_column_types(test_rows, sep_idx)
            
            # Calculate the density score
            score = self.calculate_density_score(column_types)
            
            # Store the results
            scores.append(score)
            positions.append(sep_idx)
            column_types_list.append(column_types)
            
            logger.debug(f"\nSeparator at row {sep_idx + 1}:")
            self.print_column_type_info(column_types, score)
        
        # Enhanced position selection using comprehensive header analysis
        if scores:
            best_idx = None

            # Strategy 0: Check if separator is already in the optimal position
            if header_candidates and separator_indices:
                best_header_row = header_candidates[0][0]  # Top header candidate
                optimal_separator_pos = best_header_row + 1

                # Check if there's already a separator at the optimal position
                if optimal_separator_pos in separator_indices:
                    # Find the index of this position in our test results
                    optimal_idx = None
                    for i, pos in enumerate(positions):
                        if pos == optimal_separator_pos:
                            optimal_idx = i
                            break

                    if optimal_idx is not None:
                        optimal_score = scores[optimal_idx]
                        logger.debug(f"=== Separator Already in Optimal Position ===")
                        logger.debug(f"Best header row: {best_header_row + 1} (score: {header_candidates[0][1]:.2f})")
                        logger.debug(f"Optimal separator position: {optimal_separator_pos + 1}")
                        logger.debug(f"Existing separator score: {optimal_score:.4f}")

                        # If the header quality is high (>40), strongly prefer the existing position
                        if header_candidates[0][1] > 40:
                            best_idx = optimal_idx
                            logger.debug(f"Using existing optimal separator position - high header quality ({header_candidates[0][1]:.2f})")

            # Strategy 1: Check if any priority positions (from comprehensive header analysis) have good scores
            if best_idx is None and priority_positions:
                logger.debug("=== Evaluating Priority Positions ===")

                # Get indices of priority positions in our test results
                priority_indices = [i for i, pos in enumerate(positions) if pos in priority_positions]

                if priority_indices:
                    # Prioritize the position right after the best header candidate
                    best_header_priority_pos = None
                    best_header_priority_idx = None
                    if header_candidates:
                        best_header_row = header_candidates[0][0]  # Top header candidate
                        target_priority_pos = best_header_row + 1
                        
                        # Find this position in our priority indices
                        for idx in priority_indices:
                            if positions[idx] == target_priority_pos:
                                best_header_priority_pos = target_priority_pos
                                best_header_priority_idx = idx
                                break
                    
                    # If we found the position after the best header, use it; otherwise use highest scoring priority
                    if best_header_priority_idx is not None:
                        best_priority_idx = best_header_priority_idx
                        best_priority_score = scores[best_priority_idx]
                        best_priority_pos = positions[best_priority_idx]
                        logger.debug(f"Using position after best header candidate as priority: {best_priority_pos + 1}")
                    else:
                        # Fallback to highest scoring priority position
                        best_priority_idx = max(priority_indices, key=lambda i: scores[i])
                        best_priority_score = scores[best_priority_idx]
                        best_priority_pos = positions[best_priority_idx]
                        logger.debug(f"Using highest scoring priority position: {best_priority_pos + 1}")

                    # Use trend change detection for comparison
                    trend_idx = self.detect_trend_change(scores)
                    trend_score = scores[trend_idx]
                    trend_pos = positions[trend_idx]

                    logger.debug(f"Best priority position: {best_priority_pos + 1} with score {best_priority_score:.4f}")
                    logger.debug(f"Trend change position: {trend_pos + 1} with score {trend_score:.4f}")

                    # Enhanced decision logic that considers header candidate quality
                    score_difference = trend_score - best_priority_score
                    score_difference_percent = (score_difference / trend_score) * 100 if trend_score > 0 else 0

                    # Check if the priority position corresponds to a high-quality header candidate
                    priority_header_quality = 0.0
                    if header_candidates:
                        for header_row_idx, header_score in header_candidates:
                            if header_row_idx + 1 == best_priority_pos:  # This priority pos is after a strong header
                                priority_header_quality = header_score
                                break

                    logger.debug(f"Priority position header quality score: {priority_header_quality:.2f}")
                    logger.debug(f"Score difference: {score_difference:.4f} ({score_difference_percent:.1f}%)")

                    # Decision criteria (enhanced to consider header quality):
                    # 1. If header quality is very high (>100), strongly prefer priority position regardless of score difference
                    # 2. If header quality is high (>50) and score difference is reasonable (<15%), use priority
                    # 3. If scores are very close (<5%), prefer priority position
                    # 4. If priority has good quality (>30) and reasonable score difference (<10%), use priority
                    # 5. Otherwise, use trend position

                    if priority_header_quality > 100:
                        best_idx = best_priority_idx
                        logger.debug(f"Using priority position - extremely high header quality ({priority_header_quality:.2f}) - strongly preferred")
                    elif priority_header_quality > 50 and score_difference_percent < 15.0:
                        best_idx = best_priority_idx
                        logger.debug(f"Using priority position - very high header quality ({priority_header_quality:.2f}) with acceptable score difference ({score_difference_percent:.1f}%)")
                    elif score_difference_percent < 5.0:
                        best_idx = best_priority_idx
                        logger.debug(f"Using priority position - scores very close ({score_difference_percent:.1f}% difference)")
                    elif priority_header_quality > 30 and score_difference_percent < 10.0:
                        best_idx = best_priority_idx
                        logger.debug(f"Using priority position - good header quality ({priority_header_quality:.2f}) with reasonable score difference ({score_difference_percent:.1f}%)")
                    else:
                        best_idx = trend_idx
                        logger.debug(f"Using trend position - better density score advantage ({score_difference_percent:.1f}% better) or low header quality ({priority_header_quality:.2f})")

            # Strategy 2: If no priority position was selected, use trend change detection
            if best_idx is None:
                best_idx = self.detect_trend_change(scores)
                logger.debug("Using trend change detection as fallback")

            best_position = positions[best_idx]
            best_score = scores[best_idx]
            best_column_types = column_types_list[best_idx]

            logger.debug("\nBest separator position:")
            logger.debug(f"Row {best_position + 1} with score {best_score:.4f}")
            self.print_column_type_info(best_column_types, best_score)

            # Use the best position found by the algorithm
            pass
        else:
            # If no scores were calculated, use the initial separator position
            best_position = initial_sep_idx
        
        # If the best position is the same as the initial position, just return the original content
        if best_position == initial_sep_idx:
            return content
        
        # Otherwise, manually reconstruct the table with the separator at the best position
        result_lines = []
        
        # First add all non-table content (headers)
        result_lines.extend(non_table_lines)
        
        # Create a separator row with the correct format
        # Use the column count from the best position or the row that will be the header
        if best_position < len(rows):
            # If the best position is a data row, use its column count
            # If it's after a header row, use the header row's column count
            if best_position > 0 and len(rows[best_position - 1]) > 1:
                # Use the previous row (likely header) column count
                separator_cols = len(rows[best_position - 1])
            elif best_position < len(rows) and len(rows[best_position]) > 1:
                # Use the current row column count
                separator_cols = len(rows[best_position])
            else:
                # Fallback to the first row
                separator_cols = len(rows[0])
        else:
            separator_cols = len(rows[0])

        separator_cells = ['---' for _ in range(separator_cols)]
        separator_line = '| ' + ' | '.join(separator_cells) + ' |'
        
        # Create the new table with the separator at the right position
        for i, row in enumerate(rows):
            if i == initial_sep_idx:
                # Skip the old separator row
                continue

            # Add the current row
            result_lines.append('| ' + ' | '.join(row) + ' |')

            # If this is the row just before where we want the separator, add it
            if i == best_position - 1:
                result_lines.append(separator_line)
        
        return '\n'.join(result_lines)
    
    def process_file(self, input_file: str, output_file: Optional[str] = None, delete: bool = False) -> bool:
        """Process a single markdown file"""
        logger.debug(f"Processing file: {input_file}")
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            logger.error(f"Error reading file {input_file}: {e}")
            return False
        
        # Adjust the table
        adjusted_content = self.adjust_table(content)
        
        # Determine output file
        if not output_file:
            base_name = os.path.splitext(input_file)[0]
            output_file = f"{base_name}_adjusted.md"
        
        # Write the result
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(adjusted_content)
            logger.info(f"Processed {input_file} -> {output_file}")
            
            # Delete original file if requested and output is different
            if delete and os.path.exists(output_file) and output_file != input_file:
                try:
                    os.remove(input_file)
                    logger.info(f"Deleted original file: {input_file}")
                except Exception as e:
                    logger.error(f"Error deleting original file {input_file}: {e}")
                    return False
            
            return True
        except Exception as e:
            logger.error(f"Error writing to {output_file}: {e}")
            return False
    
    def process_directory(self, input_dir: str, output_dir: Optional[str] = None, delete: bool = False) -> None:
        """Process all markdown files in a directory"""
        logger.debug(f"Processing directory: {input_dir}")
        if not os.path.isdir(input_dir):
            logger.error(f"Error: {input_dir} is not a directory")
            return
        
        # Create output directory if specified
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Find all markdown files
        md_files = []
        for root, _, files in os.walk(input_dir):
            for file in files:
                if file.lower().endswith('.md'):
                    md_files.append(os.path.join(root, file))
        
        if not md_files:
            logger.info(f"No markdown files found in {input_dir}")
            return
               
        self.process_files(md_files, output_dir, delete)
    
    def process_files(self, md_files: List[str], output_dir: Optional[str] = None, delete: bool = False):
        logger.debug(f"Processing {len(md_files)} files")
        # Process each file
        success_count = 0
        for md_file in md_files:
            # Determine output file path
            if output_dir:
                rel_path = Path(md_file).name
                out_file = os.path.join(output_dir, rel_path)
                # Ensure directory exists
                os.makedirs(os.path.dirname(out_file), exist_ok=True)
            else:
                out_file = None
            
            if self.process_file(md_file, out_file, delete):
                success_count += 1
        
        logger.info(f"Successfully processed {success_count}/{len(md_files)} files")
        return success_count
