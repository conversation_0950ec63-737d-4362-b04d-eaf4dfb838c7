import argparse
import logging
import sys
from typing import Dict, Callable, Optional, Type

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    ConfigurationException, CLIException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for CLI operations
set_layer_context("cli")

class BaseCommand:
    """Base class for all CLI commands"""
    def __init__(self, description: str = ""):
        self.parser = argparse.ArgumentParser(description=description)
        self.add_common_arguments()
    
    def add_common_arguments(self):
        """Add common arguments for all commands"""
        self.parser.add_argument('--verbose', action='store_true', help='Enable debug logging')
        self.parser.add_argument('--dry-run', action='store_true', help='Simulate execution without side effects')
    
    def add_argument(self, *args, **kwargs):
        """Add argument to the command parser"""
        self.parser.add_argument(*args, **kwargs)
    
    def execute(self, args):
        """Execute the command with parsed arguments"""
        raise NotImplementedError("Subclasses must implement this method")
    


class CLIEngine:
    """Main CLI engine for command registration and dispatch"""
    def __init__(self, prog: str = "auto-report"):
        self.commands: Dict[str, Type[BaseCommand]] = {}
        self.root_parser = argparse.ArgumentParser(prog=prog)
        self.subparsers = self.root_parser.add_subparsers(title='commands', dest='command', required=True)
    
    def register_command(self, name: str, command_class: Type[BaseCommand]):
        """Register a command class with the CLI engine"""
        command = command_class()
        command_parser = self.subparsers.add_parser(name, help=command.parser.description, parents=[command.parser], add_help=False)
        self.commands[name] = command_class
    
    @log_and_reraise(logger, "CLI engine execution")
    def run(self):
        """Run the CLI engine with unified error handling"""
        set_operation_context("cli_engine_run")

        # Parse arguments
        with error_boundary("argument parsing", LayerType.PRESENTATION):
            try:
                args = self.root_parser.parse_args()
            except SystemExit as e:
                # argparse calls sys.exit on error, which is expected behavior
                raise
            except Exception as e:
                raise CLIException(
                    message="Failed to parse command line arguments",
                    details=f"Argument parsing error: {str(e)}",
                    original_exception=e,
                    suggested_action="Check command syntax and arguments"
                )

        # Validate command
        with error_boundary("command validation", LayerType.PRESENTATION):
            if args.command not in self.commands:
                raise CLIException(
                    message="Unknown command",
                    details=f"Command '{args.command}' not found",
                    context={'available_commands': list(self.commands.keys()), 'requested_command': args.command},
                    suggested_action="Use --help to see available commands"
                )

        # Execute command
        with error_boundary("command execution", LayerType.PRESENTATION):
            try:
                command_class = self.commands[args.command]
                command = command_class()
                command.execute(args)

            except Exception as e:
                if isinstance(e, CLIException):
                    raise
                raise CLIException(
                    message="Command execution failed",
                    details=f"Execution error: {str(e)}",
                    original_exception=e,
                    context={'command': args.command},
                    suggested_action="Check command arguments and system state"
                )
