#!/usr/bin/env python3
"""
Test that reform.py has been reverted to its original functionality
without header adjustment integration
"""

import sys
import os
from pathlib import Path
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def test_reform_separation():
    """Test that reform.py focuses only on its core responsibilities"""
    print("="*80)
    print("TESTING REFORM.PY SEPARATION OF CONCERNS")
    print("="*80)
    
    # Read the financial statement file
    with open('test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract just the table content without metadata
    lines = content.split('\n')
    
    # Find the start of the actual table content
    table_start = -1
    for i, line in enumerate(lines):
        if line.startswith('| 编制单位：'):
            table_start = i
            break
    
    if table_start == -1:
        print("❌ Could not find table start")
        return False
    
    # Extract just the table content
    table_content = '\n'.join(lines[table_start:])
    
    print("Original table structure:")
    table_lines = table_content.split('\n')
    for i, line in enumerate(table_lines[:6]):
        print(f"  Line {i+1}: {line}")
    
    print()
    
    # Test reform.py processing
    filename = "1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted.md"
    
    reformer = MarkdownTableReformer()
    
    try:
        result = reformer.process_table(table_content, filename)
        print("✅ Reform processing successful")
        
        # Show the result structure
        result_lines = result.split('\n')
        print("\nReform result structure:")
        for i, line in enumerate(result_lines[:10]):
            print(f"  Line {i+1}: {line}")
        
        # Check that reform.py does NOT change header structure
        # It should preserve the original structure and only add metadata
        
        # Find the table content in the result (skip metadata and title)
        result_table_start = -1
        for i, line in enumerate(result_lines):
            if line.startswith('| 编制单位：') or line.startswith('| 项 目'):
                result_table_start = i
                break
        
        if result_table_start == -1:
            print("❌ Could not find table content in result")
            return False
        
        # Compare original and result table structures
        original_table_lines = table_lines[:6]
        result_table_lines = result_lines[result_table_start:result_table_start+6]
        
        print(f"\nStructure comparison:")
        print(f"Original table start: {original_table_lines}")
        print(f"Result table start: {result_table_lines}")
        
        # The table structure should be preserved (reform.py should not change header positioning)
        structure_preserved = True
        for i, (orig, res) in enumerate(zip(original_table_lines, result_table_lines)):
            if orig.strip() != res.strip():
                print(f"❌ Structure difference at line {i+1}:")
                print(f"  Original: {orig}")
                print(f"  Result:   {res}")
                structure_preserved = False
        
        if structure_preserved:
            print("✅ Table structure preserved - reform.py does not modify header positioning")
        else:
            print("❌ Table structure modified - reform.py should not change header positioning")
        
        # Check that metadata extraction works correctly
        metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
        if metadata_match:
            metadata_str = metadata_match.group(1)
            print(f"\nMetadata extracted: {metadata_str}")
            
            # Check date extraction
            if '"date": "2024年9月"' in metadata_str:
                print("✅ Date extraction working correctly")
                date_correct = True
            else:
                print("❌ Date extraction not working")
                date_correct = False
            
            # Check table name extraction
            if '"table_name": "1、2024年9月财务报表_利润表"' in metadata_str:
                print("✅ Table name extraction working correctly")
                table_name_correct = True
            else:
                print("❌ Table name extraction not working")
                table_name_correct = False
        else:
            print("❌ No metadata found")
            date_correct = False
            table_name_correct = False
        
        # Overall assessment
        reform_working_correctly = structure_preserved and date_correct and table_name_correct
        
        if reform_working_correctly:
            print("\n✅ REFORM.PY WORKING CORRECTLY")
            print("  - Table structure preserved (no header positioning changes)")
            print("  - Date extraction working")
            print("  - Table name extraction working")
            print("  - Metadata generation working")
            print("  - Separation of concerns maintained")
        else:
            print("\n❌ REFORM.PY HAS ISSUES")
        
        return reform_working_correctly
        
    except Exception as e:
        print(f"❌ Reform processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pipeline_architecture():
    """Test that the pipeline architecture is respected"""
    print("\n" + "="*80)
    print("TESTING PIPELINE ARCHITECTURE")
    print("="*80)
    
    # Test that reform.py and reheader.py work independently
    from app.utils.reheader import MarkdownTableHeaderAdjuster
    
    # Read the original content
    with open('test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract table content
    lines = content.split('\n')
    table_start = -1
    for i, line in enumerate(lines):
        if line.startswith('| 编制单位：'):
            table_start = i
            break
    
    table_content = '\n'.join(lines[table_start:])
    
    print("Testing pipeline sequence:")
    print("1. reheader.py (header adjustment)")
    print("2. reform.py (metadata extraction)")
    
    # Step 1: Apply reheader.py
    adjuster = MarkdownTableHeaderAdjuster()
    try:
        adjusted_content = adjuster.adjust_table(table_content)
        print("✅ Step 1: Header adjustment successful")
        
        # Show adjusted structure
        adj_lines = adjusted_content.split('\n')
        print("\nAfter header adjustment:")
        for i, line in enumerate(adj_lines[:6]):
            print(f"  Line {i+1}: {line}")
        
    except Exception as e:
        print(f"❌ Step 1: Header adjustment failed: {e}")
        return False
    
    # Step 2: Apply reform.py to the adjusted content
    filename = "1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted.md"
    reformer = MarkdownTableReformer()
    
    try:
        final_result = reformer.process_table(adjusted_content, filename)
        print("✅ Step 2: Reform processing successful")
        
        # Show final result
        final_lines = final_result.split('\n')
        print("\nAfter reform processing:")
        for i, line in enumerate(final_lines[:10]):
            print(f"  Line {i+1}: {line}")
        
        # Check that the pipeline produces the optimal result
        metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', final_result)
        if metadata_match and '"date": "2024年9月"' in metadata_match.group(1):
            print("✅ Pipeline produces correct metadata")
            
            # Check header structure
            xiangmu_found = False
            separator_after_xiangmu = False
            
            for i, line in enumerate(final_lines):
                if '项 目' in line and '本年累计' in line:
                    xiangmu_found = True
                    # Check if next line is separator
                    if i + 1 < len(final_lines) and '---' in final_lines[i + 1]:
                        separator_after_xiangmu = True
                    break
            
            if xiangmu_found and separator_after_xiangmu:
                print("✅ Pipeline produces optimal header structure")
                pipeline_success = True
            else:
                print("❌ Pipeline does not produce optimal header structure")
                pipeline_success = False
        else:
            print("❌ Pipeline does not produce correct metadata")
            pipeline_success = False
        
        return pipeline_success
        
    except Exception as e:
        print(f"❌ Step 2: Reform processing failed: {e}")
        return False

def main():
    """Main testing function"""
    try:
        # Test reform.py separation
        reform_test = test_reform_separation()
        
        # Test pipeline architecture
        pipeline_test = test_pipeline_architecture()
        
        print("\n" + "="*80)
        print("FINAL ASSESSMENT")
        print("="*80)
        print(f"Reform.py separation working: {'✅' if reform_test else '❌'}")
        print(f"Pipeline architecture working: {'✅' if pipeline_test else '❌'}")
        
        if reform_test and pipeline_test:
            print("\n🎉 SEPARATION OF CONCERNS SUCCESSFULLY RESTORED!")
            print("✅ reform.py focuses on metadata extraction only")
            print("✅ reheader.py handles header positioning independently")
            print("✅ Pipeline architecture maintained")
            print("✅ Each utility maintains distinct responsibility")
            return 0
        else:
            print("\n❌ Some issues remain with separation of concerns")
            return 1
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
