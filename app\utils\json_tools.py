import re
import json
import logging
from typing import Optional, Dict
from pathlib import Path
from langchain_core.messages import HumanMessage

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    DataProcessingException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

@log_and_reraise(logger, "JSON extraction")
def extract_last_valid_json(text: str) -> Optional[Dict]:
    """从文本中提取最后一个有效的JSON对象 with unified error handling

    处理流程：
    1. 尝试直接解析输入文本
    2. 使用正则匹配可能的JSON块
    3. 清理并验证匹配到的JSON内容

    Args:
        text: 可能包含JSON的原始文本

    Returns:
        解析成功的字典对象，或None
    """
    set_operation_context("json_extraction")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if text is None:
            raise ValidationException(
                message="Text input cannot be None",
                field_name="text",
                details="Input text is None",
                suggested_action="Provide a valid text string"
            )

        if not isinstance(text, str):
            raise ValidationException(
                message="Text input must be a string",
                field_name="text",
                details=f"Expected string, got {type(text)}",
                context={'input_type': type(text).__name__},
                suggested_action="Provide a valid text string"
            )

    # Try direct parsing first
    with error_boundary("direct JSON parsing", LayerType.UTILITY):
        stripped_text = text.strip()
        if stripped_text:
            try:
                logging.debug(f"尝试解析text: {stripped_text}")
                data = json.loads(stripped_text)
                return data
            except json.JSONDecodeError:
                logging.debug(f"不是合法json，继续清洗")

    # Extract JSON blocks using regex
    with error_boundary("JSON block extraction", LayerType.UTILITY):
        try:
            # 增强正则匹配模式
            json_blocks = re.findall(
                r'(?i)(?<=```json)[\s\S]*?```|{(?:[^{}]*|{[^{}]*})*}',  # 匹配代码块或简单嵌套JSON
                text,
                flags=re.IGNORECASE
            )
            logging.debug(f"json 块：{json_blocks}")
        except Exception as e:
            raise DataProcessingException(
                message="Failed to extract JSON blocks with regex",
                details=f"Regex extraction error: {str(e)}",
                original_exception=e,
                suggested_action="Check text format and content"
            )

    # Clean and parse JSON blocks
    with error_boundary("JSON block parsing", LayerType.UTILITY):
        try:
            # 严格清洗逻辑
            for block in reversed(json_blocks):
                cleaned = re.sub(r'^```|```$', '', block).strip()  # 去除代码块标记
                if not cleaned:
                    continue

                # 处理尾部逗号等常见错误
                cleaned = re.sub(r',(\s*[}\]])', r'\1', cleaned)  # 移除JSON尾逗号

                try:
                    logging.debug(f"解析清理后json： {cleaned}")
                    data = json.loads(cleaned)
                    return data
                except json.JSONDecodeError as e:
                    # 错误定位提示（开发时调试用）
                    logging.error(f"Invalid JSON at position {e.pos}: {e.doc[e.pos - 10:e.pos + 10]}")
                    continue

        except Exception as e:
            raise DataProcessingException(
                message="Failed to parse JSON blocks",
                details=f"JSON parsing error: {str(e)}",
                original_exception=e,
                suggested_action="Check JSON format and syntax"
            )

    return None
