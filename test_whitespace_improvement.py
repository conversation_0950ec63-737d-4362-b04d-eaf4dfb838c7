#!/usr/bin/env python3
"""
Test to demonstrate the improvement from whitespace preprocessing
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reheader import MarkdownTableHeaderAdjuster

def test_whitespace_improvement():
    """Test to show how preprocessing improves keyword detection"""
    print("="*80)
    print("DEMONSTRATING WHITESPACE PREPROCESSING IMPROVEMENT")
    print("="*80)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Create a test case with problematic whitespace
    test_content = """# 财务报表
| 科  目 | 本 期 金 额 | 上 期 金 额 |
|-------|---------|---------|
| 营业收入 | 1000000 | 900000 |
| 营业成本 | 600000 | 550000 |"""
    
    print("Test content with whitespace issues:")
    print(test_content)
    print()
    
    # Parse the table
    rows = adjuster.parse_table(test_content)
    
    print("Parsed rows:")
    for i, row in enumerate(rows):
        print(f"Row {i+1}: {row}")
    
    print()
    
    # Test the header row (should be row 0)
    header_row = rows[0]
    print(f"Testing header row: {header_row}")
    
    # Show the preprocessing effect
    cleaned_row = adjuster.preprocess_row_for_text_analysis(header_row)
    print(f"After preprocessing: {cleaned_row}")
    
    # Test keyword detection
    has_keywords = adjuster.contains_header_keywords(header_row)
    keyword_count = adjuster.count_header_keywords(header_row)
    has_strong = adjuster.has_strong_header_keywords(header_row)
    
    print(f"\nKeyword detection results:")
    print(f"  Has header keywords: {has_keywords}")
    print(f"  Keyword count: {keyword_count}")
    print(f"  Has strong keywords: {has_strong}")
    
    # Test scoring
    score = adjuster.calculate_comprehensive_header_score(header_row, 0, len(rows), rows)
    print(f"  Comprehensive score: {score:.2f}")
    
    # Show what would happen without preprocessing (simulate)
    print(f"\nComparison - what would happen without preprocessing:")
    original_text = ' '.join(header_row)
    print(f"  Original joined text: '{original_text}'")
    
    # Manual check for keywords in original text
    manual_keyword_count = 0
    for keyword in adjuster.header_keywords:
        if keyword in original_text:
            manual_keyword_count += 1
            print(f"    Found keyword: '{keyword}'")
    
    print(f"  Manual keyword count in original: {manual_keyword_count}")
    
    # The preprocessing should find '科目' even with spaces
    if '科目' in ' '.join(cleaned_row):
        print(f"  ✅ Preprocessing successfully found '科目' despite spaces")
    else:
        print(f"  ❌ Preprocessing failed to find '科目'")
    
    print()
    
    # Test with even more challenging whitespace
    print("="*60)
    print("CHALLENGING WHITESPACE TEST")
    print("="*60)
    
    challenging_rows = [
        ['  项   目  ', ' 行  次 ', '  金   额  '],  # Multiple spaces
        [' \t科\t目\t ', ' \n时\n间\n ', '  备注  '],  # Tabs and newlines
        ['   ', '  ', '   '],  # Only whitespace
        ['序号', '  ', '项目名称'],  # Mixed content
    ]
    
    for i, row in enumerate(challenging_rows):
        print(f"Challenging row {i+1}: {row}")
        
        cleaned = adjuster.preprocess_row_for_text_analysis(row)
        has_kw = adjuster.contains_header_keywords(row)
        kw_count = adjuster.count_header_keywords(row)
        has_strong = adjuster.has_strong_header_keywords(row)
        density = adjuster.calculate_row_density(row)
        
        print(f"  Cleaned: {cleaned}")
        print(f"  Keywords: {kw_count}, Strong: {has_strong}, Density: {density:.2f}")
        print()

def test_full_processing_improvement():
    """Test the full processing with the balance sheet file"""
    print("="*80)
    print("FULL PROCESSING IMPROVEMENT TEST")
    print("="*80)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Process the balance sheet file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Processing balance sheet with improved preprocessing...")
    
    result = adjuster.adjust_table(content)
    
    # Check if the header is correctly positioned
    result_lines = result.split('\n')
    
    print("First 10 lines of result:")
    for i, line in enumerate(result_lines[:10]):
        print(f"{i+1:2d}: {line}")
    
    # Verify the header is correct
    header_found = False
    separator_after_header = False
    
    for i, line in enumerate(result_lines):
        if '科目' in line and '2024-12-31' in line:
            header_found = True
            print(f"\n✅ Found correct header at line {i+1}: {line}")
            
            # Check if next line is separator
            if i + 1 < len(result_lines) and '---' in result_lines[i + 1]:
                separator_after_header = True
                print(f"✅ Separator correctly positioned at line {i+2}")
            break
    
    if header_found and separator_after_header:
        print(f"\n🎉 Header processing with whitespace preprocessing is working perfectly!")
        return True
    else:
        print(f"\n❌ Header processing has issues")
        return False

def main():
    """Run all tests"""
    print("Testing Whitespace Preprocessing Improvements")
    print("="*80)
    
    try:
        test_whitespace_improvement()
        success = test_full_processing_improvement()
        
        if success:
            print("\n🎉 All whitespace preprocessing improvements verified!")
            return 0
        else:
            print("\n❌ Some improvements need attention")
            return 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
