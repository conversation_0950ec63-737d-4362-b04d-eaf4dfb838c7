#!/usr/bin/env python3
"""
Test the hierarchical table name extraction system
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def test_hierarchical_extraction():
    """Test the hierarchical table name extraction system"""
    print("="*80)
    print("TESTING HIERARCHICAL TABLE NAME EXTRACTION SYSTEM")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Test case 1: Priority 1 - Table header lines
    print("Test 1: Priority 1 - Table header lines")
    print("-" * 40)
    
    header_rows = [
        ['利润表', '', ''],
        ['科目', '本期金额', '上期金额'],
        ['营业收入', '1000000', '900000']
    ]
    markdown_headers = ['# 财务报表']
    filename = "company_financial_data.md"
    
    result1 = reformer.hierarchical_table_name_extraction(header_rows, markdown_headers, filename)
    print(f"Result: '{result1}'")
    print(f"Expected: Should extract '利润表' from header lines")
    print(f"✅ PASS" if '利润表' in result1 else "❌ FAIL")
    
    print()
    
    # Test case 2: Priority 2 - Markdown headers
    print("Test 2: Priority 2 - Markdown headers")
    print("-" * 40)
    
    header_rows = [
        ['科目', '金额'],
        ['收入', '1000']
    ]
    markdown_headers = ['# 现金流量表', '## 公司:测试公司']
    filename = "data.md"
    
    result2 = reformer.hierarchical_table_name_extraction(header_rows, markdown_headers, filename)
    print(f"Result: '{result2}'")
    print(f"Expected: Should extract '现金流量表' from markdown headers")
    print(f"✅ PASS" if '现金流量表' in result2 else "❌ FAIL")
    
    print()
    
    # Test case 3: Priority 3 - Filename (Chinese with comprehensive info)
    print("Test 3: Priority 3 - Chinese filename with comprehensive info")
    print("-" * 40)
    
    header_rows = [
        ['科目', '金额'],
        ['收入', '1000']
    ]
    markdown_headers = ['# 报表']
    filename = "季度合并财报（2024年Q4）_资产负债表.md"
    
    result3 = reformer.hierarchical_table_name_extraction(header_rows, markdown_headers, filename)
    print(f"Result: '{result3}'")
    print(f"Expected: Should use filename due to comprehensive info")
    print(f"✅ PASS" if '季度合并财报' in result3 and '2024年Q4' in result3 else "❌ FAIL")
    
    print()
    
    # Test case 4: Date/period extraction
    print("Test 4: Date/period extraction from filename")
    print("-" * 40)
    
    test_filenames = [
        "财务报表_2024年Q3_利润表.md",
        "资产负债表（2024年9月）.md", 
        "现金流量表-2024-三季度-.md",
        "合并报表_Q4_2024年.md"
    ]
    
    for filename in test_filenames:
        dates = reformer.extract_date_period_info(filename)
        table_type = reformer.extract_table_type_from_filename(filename)
        print(f"Filename: {filename}")
        print(f"  Dates: {dates}")
        print(f"  Table type: {table_type}")
    
    print()
    
    # Test case 5: Excel cell position filtering
    print("Test 5: Excel cell position filtering")
    print("-" * 40)
    
    test_filenames = [
        "财务数据_A1E34_Sheet1.md",  # Should remove _A1E34_
        "季度报表（Q4）_B2C25_数据.md",  # Should remove _B2C25_
        "2024年Q4财报.md"  # Should preserve Q4
    ]
    
    for filename in test_filenames:
        cell_positions = reformer.extract_excel_cell_positions(filename)
        cleaned = reformer.preprocess_filename(filename)
        print(f"Filename: {filename}")
        print(f"  Cell positions: {cell_positions}")
        print(f"  Cleaned: {cleaned}")
    
    print()

def test_real_file_processing():
    """Test with the actual balance sheet file"""
    print("="*80)
    print("TESTING REAL FILE PROCESSING")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Test the balance sheet file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_资产负债表.md"
    
    print(f"Processing: {filename}")
    result = reformer.process_table(content, filename)
    
    # Extract metadata
    import re, json
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        table_name = metadata.get('table_name', '')
        
        print(f"Extracted table name: '{table_name}'")
        
        # Check all requirements
        checks = {
            "Contains '季度合并财报'": '季度合并财报' in table_name,
            "Contains '2024年Q4'": '2024年Q4' in table_name,
            "Contains '资产负债表'": '资产负债表' in table_name,
            "No truncation": len(table_name) > 20,
            "No malformed characters": ' - ' not in table_name
        }
        
        print("\nQuality checks:")
        for check, passed in checks.items():
            print(f"  {check}: {'✅ PASS' if passed else '❌ FAIL'}")
        
        all_passed = all(checks.values())
        print(f"\nOverall result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
        
        return all_passed
    else:
        print("❌ No metadata found")
        return False

def main():
    """Run all tests"""
    print("Testing Hierarchical Table Name Extraction System")
    print("="*80)
    
    try:
        test_hierarchical_extraction()
        success = test_real_file_processing()
        
        if success:
            print("\n🎉 All hierarchical extraction tests passed!")
            return 0
        else:
            print("\n❌ Some tests failed")
            return 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
