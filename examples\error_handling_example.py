#!/usr/bin/env python3
"""
Example demonstrating the unified error handling strategy.

This example shows how to properly implement error handling across
the three application layers with proper exception wrapping and logging.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any
import sys

sys.path.append(str(Path(__file__).parent.parent))

# Import unified error handling framework
from app.exceptions import (
    LayerType, ErrorSeverity,
    ValidationException, FileProcessingException,
    BusinessLogicException, APIException, PresentationLayerException
)
from app.error_handling import (
    handle_layer_boundary, log_and_reraise, error_boundary, safe_execute
)
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


# =============================================================================
# UTILITY LAYER EXAMPLES
# =============================================================================

class FileProcessor:
    """Example utility layer class with proper error handling."""
    
    @log_and_reraise(logger, "file validation")
    def validate_file(self, file_path: str) -> bool:
        """Validate file exists and is readable."""
        if not os.path.exists(file_path):
            raise FileProcessingException(
                message="File not found",
                file_path=file_path,
                details=f"The specified file does not exist: {file_path}",
                suggested_action="Check the file path and ensure the file exists"
            )
        
        if not os.access(file_path, os.R_OK):
            raise FileProcessingException(
                message="File not readable",
                file_path=file_path,
                details="Insufficient permissions to read the file",
                suggested_action="Check file permissions or run with appropriate privileges"
            )
        
        return True
    
    @log_and_reraise(logger, "file processing")
    def process_file(self, file_path: str) -> Dict[str, Any]:
        """Process a file and return metadata."""
        # Validate first
        self.validate_file(file_path)
        
        # Simulate processing
        file_stats = os.stat(file_path)
        
        return {
            'file_path': file_path,
            'size_bytes': file_stats.st_size,
            'modified_time': file_stats.st_mtime,
            'status': 'processed'
        }


# =============================================================================
# BUSINESS LOGIC LAYER EXAMPLES
# =============================================================================

class DocumentWorkflow:
    """Example business logic layer class with proper error handling."""
    
    def __init__(self):
        self.file_processor = FileProcessor()
    
    @handle_layer_boundary(LayerType.BUSINESS_LOGIC, "document validation")
    def validate_document_request(self, request_data: Dict[str, Any]) -> bool:
        """Validate a document processing request."""
        required_fields = ['file_path', 'output_format']
        
        for field in required_fields:
            if field not in request_data:
                raise ValidationException(
                    message=f"Missing required field: {field}",
                    field_name=field,
                    context={
                        'provided_fields': list(request_data.keys()),
                        'required_fields': required_fields
                    },
                    suggested_action=f"Include '{field}' in the request data"
                )
        
        # Validate file extension
        file_path = request_data['file_path']
        if not file_path.endswith(('.pdf', '.docx', '.txt')):
            raise ValidationException(
                message="Unsupported file format",
                field_name="file_path",
                details=f"File extension not supported: {Path(file_path).suffix}",
                context={'supported_formats': ['.pdf', '.docx', '.txt']},
                suggested_action="Use a supported file format (PDF, DOCX, or TXT)"
            )
        
        return True
    
    @handle_layer_boundary(LayerType.BUSINESS_LOGIC, "document processing workflow")
    def process_document(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a document through the complete workflow."""
        set_operation_context("document_processing_workflow")
        
        # Validate request
        self.validate_document_request(request_data)
        
        file_path = request_data['file_path']
        output_format = request_data['output_format']
        
        # Process file through utility layer
        with error_boundary("file processing", LayerType.BUSINESS_LOGIC):
            file_metadata = self.file_processor.process_file(file_path)
        
        # Simulate business logic processing
        result = {
            'input_file': file_metadata,
            'output_format': output_format,
            'workflow_status': 'completed',
            'processing_time': 1.5  # seconds
        }
        
        logger.info(f"Document workflow completed for {file_path}")
        return result


# =============================================================================
# PRESENTATION LAYER EXAMPLES
# =============================================================================

class DocumentAPI:
    """Example presentation layer class with proper error handling."""
    
    def __init__(self):
        self.workflow = DocumentWorkflow()
    
    @handle_layer_boundary(LayerType.PRESENTATION, "API request processing")
    def process_document_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle a document processing API request."""
        set_layer_context("presentation")
        set_operation_context("api_document_processing")
        
        logger.info("Processing document API request")
        
        # Basic request validation
        if not isinstance(request_data, dict):
            raise APIException(
                message="Invalid request format",
                error_code="API_001",
                http_status=400,
                details="Request body must be a JSON object",
                suggested_action="Send request data as a JSON object"
            )
        
        # Process through business logic layer
        try:
            result = self.workflow.process_document(request_data)
            
            # Format successful response
            return {
                'status': 'success',
                'data': result,
                'message': 'Document processed successfully'
            }
            
        except ValidationException as e:
            # Re-raise validation errors with API context
            raise APIException(
                message="Request validation failed",
                error_code="API_VAL_001",
                http_status=400,
                details=e.message,
                context=e.context,
                original_exception=e,
                suggested_action=e.suggested_action
            )


# =============================================================================
# EXAMPLE USAGE AND DEMONSTRATION
# =============================================================================

def demonstrate_successful_flow():
    """Demonstrate successful error handling flow."""
    print("\n=== Demonstrating Successful Flow ===")
    
    # Create a test file
    test_file = "test_document.txt"
    with open(test_file, 'w') as f:
        f.write("This is a test document for error handling demonstration.")
    
    try:
        api = DocumentAPI()
        
        request_data = {
            'file_path': test_file,
            'output_format': 'markdown'
        }
        
        result = api.process_document_request(request_data)
        print(f"✅ Success: {result['message']}")
        print(f"   Status: {result['status']}")
        print(f"   File processed: {result['data']['input_file']['file_path']}")
        
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)


def demonstrate_error_handling():
    """Demonstrate error handling with various error scenarios."""
    print("\n=== Demonstrating Error Handling ===")
    
    api = DocumentAPI()
    
    # Test 1: Missing file
    print("\n1. Testing file not found error:")
    try:
        result = api.process_document_request({
            'file_path': 'nonexistent_file.txt',
            'output_format': 'markdown'
        })
    except (APIException, PresentationLayerException) as e:
        print(f"   ❌ Caught {e.__class__.__name__}: {e.message}")
        print(f"   Error Code: {e.error_code}")
        print(f"   Suggested Action: {e.suggested_action}")
        if e.original_exception:
            print(f"   Root Cause: {e.original_exception.__class__.__name__}")
    
    # Test 2: Invalid request format
    print("\n2. Testing invalid request format:")
    try:
        result = api.process_document_request("invalid_request")
    except (APIException, PresentationLayerException) as e:
        print(f"   ❌ Caught {e.__class__.__name__}: {e.message}")
        if hasattr(e, 'http_status'):
            print(f"   HTTP Status: {e.http_status}")

    # Test 3: Missing required field
    print("\n3. Testing missing required field:")
    try:
        result = api.process_document_request({
            'file_path': 'some_file.txt'
            # Missing 'output_format'
        })
    except (APIException, PresentationLayerException) as e:
        print(f"   ❌ Caught {e.__class__.__name__}: {e.message}")
        if hasattr(e, 'original_exception') and hasattr(e.original_exception, 'field_name'):
            print(f"   Field Name: {e.original_exception.field_name}")
            print(f"   Context: {e.original_exception.context}")

    # Test 4: Unsupported file format
    print("\n4. Testing unsupported file format:")
    try:
        result = api.process_document_request({
            'file_path': 'document.xyz',
            'output_format': 'markdown'
        })
    except (APIException, PresentationLayerException) as e:
        print(f"   ❌ Caught {e.__class__.__name__}: {e.message}")
        print(f"   Details: {e.details}")


def demonstrate_safe_execution():
    """Demonstrate safe execution patterns."""
    print("\n=== Demonstrating Safe Execution ===")
    
    def risky_operation():
        raise FileProcessingException("Simulated file error")
    
    # Safe execution with default return value
    result = safe_execute(
        risky_operation,
        operation_context="optional cleanup",
        layer=LayerType.UTILITY,
        default_return="cleanup_skipped"
    )
    
    print(f"✅ Safe execution result: {result}")
    print("   (Error was logged but didn't crash the program)")


if __name__ == "__main__":
    print("Unified Error Handling Strategy Demonstration")
    print("=" * 50)
    
    # Run demonstrations
    demonstrate_successful_flow()
    demonstrate_error_handling()
    demonstrate_safe_execution()
    
    print("\n" + "=" * 50)
    print("Demonstration completed. Check logs for detailed error information.")
