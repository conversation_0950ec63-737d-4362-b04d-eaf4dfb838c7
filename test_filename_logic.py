#!/usr/bin/env python3
"""
Test the filename logic specifically
"""

import sys
import os
from pathlib import Path
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

def test_filename_logic():
    """Test the filename logic"""
    print("="*80)
    print("TESTING FILENAME LOGIC")
    print("="*80)
    
    filename = "-季度合并财报（2024年Q4）_资产负债表.md"
    main_title = "资产负债表"  # This is what gets extracted from content
    
    print(f"Original filename: {filename}")
    print(f"Main title from content: {main_title}")
    
    # Test the logic
    base_filename = filename.replace('.md', '').replace('_adjusted', '').replace('_reformed', '')
    print(f"Base filename: {base_filename}")
    
    # Check if contains Chinese
    has_chinese = re.search(r'[\u4e00-\u9fff]', base_filename)
    print(f"Contains Chinese: {bool(has_chinese)}")
    
    # Test the conditions
    condition1 = len(base_filename) > len(main_title or '')
    condition2 = '2024' in base_filename
    condition3 = 'Q' in base_filename
    
    print(f"Length comparison: {len(base_filename)} > {len(main_title or '')} = {condition1}")
    print(f"Contains '2024': {condition2}")
    print(f"Contains 'Q': {condition3}")
    
    should_use_filename = condition1 or condition2 or condition3
    print(f"Should use filename: {should_use_filename}")
    
    if should_use_filename:
        print(f"Would set main_title to: '{base_filename}'")
    else:
        print(f"Would keep main_title as: '{main_title}'")

def main():
    """Main test function"""
    try:
        test_filename_logic()
        return 0
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
