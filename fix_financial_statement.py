#!/usr/bin/env python3
"""
Fix the financial statement file with correct date extraction
"""

import sys
import os
from pathlib import Path
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def fix_financial_statement():
    """Fix the financial statement file"""
    print("="*80)
    print("FIXING FINANCIAL STATEMENT FILE")
    print("="*80)
    
    # Read the current file
    input_file = 'test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed.md'
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Processing file: {input_file}")
    
    # Extract the table content without metadata
    lines = content.split('\n')
    
    # Find the start of the actual table content (after title)
    table_start = -1
    for i, line in enumerate(lines):
        if line.startswith('| 编制单位：'):
            table_start = i
            break
    
    if table_start == -1:
        print("❌ Could not find table start")
        return False
    
    # Extract just the table content
    table_content = '\n'.join(lines[table_start:])
    
    print("Original table content found")
    print("Processing with correct filename...")
    
    # Process with the correct filename
    filename = "1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted.md"
    
    reformer = MarkdownTableReformer()
    
    try:
        result = reformer.process_table(table_content, filename)
        print("✅ Processing successful")
        
        # Extract the new metadata
        metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
        if metadata_match:
            print("\nNew metadata extracted:")
            print(metadata_match.group(1))
            
            # Check if date is correct
            if '"date": "2024年9月"' in metadata_match.group(1):
                print("✅ Date extraction correct: '2024年9月'")
            else:
                print("❌ Date extraction still incorrect")
                return False
        
        # Write the corrected file
        output_file = 'test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed_fixed.md'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result)
        
        print(f"\n✅ Fixed file written to: {output_file}")
        
        # Show first 10 lines of the result
        result_lines = result.split('\n')
        print("\nFirst 10 lines of fixed file:")
        for i, line in enumerate(result_lines[:10]):
            print(f"  {i+1}: {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_fixes():
    """Verify that both issues are resolved"""
    print("\n" + "="*80)
    print("VERIFYING FIXES")
    print("="*80)
    
    # Read the fixed file
    fixed_file = 'test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed_fixed.md'
    
    try:
        with open(fixed_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("Checking fixed file...")
        
        # Check Issue 1: Date extraction
        metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', content)
        if metadata_match:
            metadata_str = metadata_match.group(1)
            
            if '"date": "2024年9月"' in metadata_str:
                print("✅ Issue 1 RESOLVED: Date extraction correct")
                issue1_fixed = True
            else:
                print("❌ Issue 1 NOT RESOLVED: Date extraction incorrect")
                issue1_fixed = False
        else:
            print("❌ Issue 1 NOT RESOLVED: No metadata found")
            issue1_fixed = False
        
        # Check Issue 2: Header structure
        lines = content.split('\n')
        
        # Find the header structure
        xiangmu_found = False
        separator_after_xiangmu = False
        
        for i, line in enumerate(lines):
            if '项 目' in line and '本年累计' in line:
                xiangmu_found = True
                # Check if next line is separator
                if i + 1 < len(lines) and '---' in lines[i + 1]:
                    separator_after_xiangmu = True
                break
        
        if xiangmu_found and separator_after_xiangmu:
            print("✅ Issue 2 RESOLVED: Header structure correct")
            issue2_fixed = True
        else:
            print("❌ Issue 2 NOT RESOLVED: Header structure incorrect")
            issue2_fixed = False
        
        # Overall result
        if issue1_fixed and issue2_fixed:
            print("\n🎉 ALL ISSUES RESOLVED!")
            print("✅ Date extraction: '2024年9月' correctly extracted from filename")
            print("✅ Header detection: '项 目' row properly positioned with separator")
            print("✅ Format preservation: Original date format maintained")
            print("✅ Multi-level headers: Proper structure maintained")
            return True
        else:
            print("\n❌ Some issues remain unresolved")
            return False
        
    except FileNotFoundError:
        print(f"❌ Fixed file not found: {fixed_file}")
        return False
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main function"""
    try:
        # Fix the file
        fix_success = fix_financial_statement()
        
        if fix_success:
            # Verify the fixes
            verify_success = verify_fixes()
            return 0 if verify_success else 1
        else:
            return 1
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
