import json
import time
import typer
from typing import List, Optional
from pathlib import Path
import os
from datetime import datetime
import logging
from dotenv import load_dotenv
from .client import RAGFlowClient

# Load environment variables from .env file
load_dotenv(override=True)
RAGFLOW_BASE_URL = os.getenv('RAGFLOW_BASE_URL')
RAGFLOW_API_KEY = os.getenv('RAGFLOW_API_KEY')

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

app = typer.Typer(help="RAGFlow API Command Line Interface")

class ProcessManager:
    LOCK_FILE = "ragflow_session.lock"
    
    @classmethod
    def start(cls, pid: int, logged_in: bool = False):
        """Record process info to lock file"""
        try:
            os.makedirs("ragflow_api", exist_ok=True)
            with open(cls.LOCK_FILE, "w") as f:
                json.dump({
                    "pid": pid,
                    "start_time": datetime.now().isoformat(),
                    "status": "running",
                    "logged_in": logged_in
                }, f)
            logger.info(f"Process {pid} started and recorded")
            return True
        except Exception as e:
            logger.error(f"Failed to record process: {str(e)}")
            return False

    @classmethod
    def check(cls):
        """Check if process is still running"""
        if not os.path.exists(cls.LOCK_FILE):
            return False
            
        try:
            with open(cls.LOCK_FILE) as f:
                data = json.load(f)
            
            # Check if process exists
            os.kill(data["pid"], 0)
            
            # Check if exceeded max runtime (30 minutes)
            start_time = datetime.fromisoformat(data["start_time"])
            if (datetime.now() - start_time).total_seconds() > 1800:
                cls.stop()
                return False
                
            return True
        except (FileNotFoundError, json.JSONDecodeError):
            return False
        except OSError:  # Process doesn't exist
            return False
        except Exception as e:
            logger.error(f"Process check failed: {str(e)}")
            return False

    @classmethod
    def stop(cls):
        """Stop process and clean up lock file"""
        try:
            if os.path.exists(cls.LOCK_FILE):
                os.remove(cls.LOCK_FILE)
            logger.info("Process stopped and lock file removed")
            return True
        except Exception as e:
            logger.error(f"Failed to stop process: {str(e)}")
            return False

    @classmethod
    def update_login_status(cls, logged_in: bool):
        """Update login status"""
        if not os.path.exists(cls.LOCK_FILE):
            return False
            
        try:
            with open(cls.LOCK_FILE, 'r+') as f:
                data = json.load(f)
                data['logged_in'] = logged_in
                f.seek(0)
                json.dump(data, f)
                f.truncate()
            logger.info(f"Updated login status to {logged_in}")
            return True
        except Exception as e:
            logger.error(f"Failed to update login status: {str(e)}")
            return False

    @classmethod
    def get_info(cls):
        """Get process info"""
        if not os.path.exists(cls.LOCK_FILE):
            return None
            
        try:
            with open(cls.LOCK_FILE) as f:
                data = json.load(f)
                # Ensure backward compatibility
                if 'logged_in' not in data:
                    data['logged_in'] = False
                return data
        except Exception as e:
            logger.error(f"Failed to get process info: {str(e)}")
            return None

# Helper function to get client
def get_client(base_url: Optional[str] = None, api_key: Optional[str] = None) -> RAGFlowClient:
    url = base_url or RAGFLOW_BASE_URL
    key = api_key or RAGFLOW_API_KEY
    
    if not url:
        raise ValueError("No base URL provided. Either set RAGFLOW_BASE_URL in .env or use --base-url parameter")
    if not key:
        raise ValueError("No API key provided. Either set RAGFLOW_API_KEY in .env or use --api-key parameter")
        
    return RAGFlowClient(base_url=url, api_key=key)

# Dataset Management Commands
@app.command()
def create_dataset(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    name: str = typer.Option(..., "--name", "-n", help="Dataset name"),
    embedding_model: str = typer.Option(..., "--embedding-model", "-e", help="Embedding model name"),
    chunk_method: str = typer.Option("naive", "--chunk-method", "-c", help="Chunking method"),
    description: Optional[str] = typer.Option(None, "--description", "-d", help="Dataset description"),
    permission: str = typer.Option("me", "--permission", "-p", help="Access permission")
):
    """Create a new dataset"""
    client = get_client(base_url, api_key)
    try:
        result = client.create_dataset(
            name=name,
            embedding_model=embedding_model,
            chunk_method=chunk_method,
            description=description,
            permission=permission,
            pagerank=0,
            parser_config={
                "auto_keywords": 15,
                "auto_questions": 3,
            }
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to create dataset: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def delete_datasets(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_ids: List[str] = typer.Option(..., "--ids", help="Dataset IDs to delete")
):
    """Delete datasets"""
    client = get_client(base_url, api_key)
    try:
        result = client.delete_datasets(dataset_ids=dataset_ids)
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to delete datasets: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def update_dataset(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--id", help="Dataset ID to update"),
    name: Optional[str] = typer.Option(None, "--name", "-n", help="New dataset name"),
    embedding_model: Optional[str] = typer.Option(None, "--embedding-model", "-e", help="New embedding model"),
    chunk_method: Optional[str] = typer.Option(None, "--chunk-method", "-c", help="New chunk method")
):
    """Update dataset configuration"""
    client = get_client(base_url, api_key)
    try:
        result = client.update_dataset(
            dataset_id=dataset_id,
            name=name,
            embedding_model=embedding_model,
            chunk_method=chunk_method
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to update dataset: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def list_datasets(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    page: int = typer.Option(1, "--page", "-p", help="Page number"),
    page_size: int = typer.Option(30, "--page-size", "-s", help="Items per page"),
    orderby: str = typer.Option("create_time", "--orderby", "-o", help="Field to sort by"),
    desc: bool = typer.Option(True, "--desc/--asc", help="Sort descending/ascending"),
    name: Optional[str] = typer.Option(None, "--name", "-n", help="Filter by name"),
    dataset_id: Optional[str] = typer.Option(None, "--id", help="Filter by ID")
):
    """List datasets"""
    client = get_client(base_url, api_key)
    try:
        result = client.list_datasets(
            page=page,
            page_size=page_size,
            orderby=orderby,
            desc=desc,
            name=name,
            dataset_id=dataset_id
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to list datasets: {str(e)}")
        raise typer.Exit(code=1)

# File Management Commands
@app.command()
def upload_documents(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Target dataset ID"),
    file_paths: List[str] = typer.Option(..., "--files", "-f", help="File paths to upload")
):
    """Upload documents to a dataset"""
    client = get_client(base_url, api_key)
    try:
        result = client.upload_documents(
            dataset_id=dataset_id,
            file_paths=file_paths
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to upload documents: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def update_document(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    document_id: str = typer.Option(..., "--document-id", "-i", help="Document ID to update"),
    name: Optional[str] = typer.Option(None, "--name", "-n", help="New document name"),
    meta_fields: Optional[str] = typer.Option(None, "--meta-fields", "-m", help="Metadata fields as JSON string"),
    chunk_method: Optional[str] = typer.Option(None, "--chunk-method", "-c", help="New chunk method"),
    parser_config: Optional[str] = typer.Option(None, "--parser-config", "-p", help="Parser config as JSON string")
):
    """Update document configuration"""
    client = get_client(base_url, api_key)
    try:
        meta = json.loads(meta_fields) if meta_fields else None
        parser = json.loads(parser_config) if parser_config else None
        
        result = client.update_document(
            dataset_id=dataset_id,
            document_id=document_id,
            name=name,
            meta_fields=meta,
            chunk_method=chunk_method,
            parser_config=parser
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to update document: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def download_document(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    document_id: str = typer.Option(..., "--document-id", "-i", help="Document ID to download"),
    output_path: str = typer.Option(..., "--output", "-o", help="Output file path")
):
    """Download a document from a dataset"""
    client = get_client(base_url, api_key)
    try:
        client.download_document(
            dataset_id=dataset_id,
            document_id=document_id,
            output_path=output_path
        )
        typer.echo(f"Document downloaded to {output_path}")
    except Exception as e:
        logger.error(f"Failed to download document: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def list_documents(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    page: int = typer.Option(1, "--page", "-p", help="Page number"),
    page_size: int = typer.Option(30, "--page-size", "-s", help="Items per page"),
    orderby: str = typer.Option("create_time", "--orderby", "-o", help="Field to sort by"),
    desc: bool = typer.Option(True, "--desc/--asc", help="Sort descending/ascending"),
    keywords: Optional[str] = typer.Option(None, "--keywords", "-k", help="Filter by keywords"),
    document_id: Optional[str] = typer.Option(None, "--id", help="Filter by document ID"),
    document_name: Optional[str] = typer.Option(None, "--name", "-n", help="Filter by document name")
):
    """List documents in a dataset"""
    client = get_client(base_url, api_key)
    try:
        result = client.list_documents(
            dataset_id=dataset_id,
            page=page,
            page_size=page_size,
            orderby=orderby,
            desc=desc,
            keywords=keywords,
            document_id=document_id,
            document_name=document_name
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to list documents: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def delete_documents(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    document_ids: List[str] = typer.Option(..., "--ids", help="Document IDs to delete")
):
    """Delete documents from a dataset"""
    client = get_client(base_url, api_key)
    try:
        result = client.delete_documents(
            dataset_id=dataset_id,
            document_ids=document_ids
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to delete documents: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def parse_documents(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    document_ids: List[str] = typer.Option(..., "--ids", help="Document IDs to parse")
):
    """Parse documents in a dataset"""
    client = get_client(base_url, api_key)
    try:
        result = client.parse_documents(
            dataset_id=dataset_id,
            document_ids=document_ids
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to parse documents: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def stop_parsing_documents(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    document_ids: List[str] = typer.Option(..., "--ids", help="Document IDs to stop parsing")
):
    """Stop parsing documents"""
    client = get_client(base_url, api_key)
    try:
        result = client.stop_parsing_documents(
            dataset_id=dataset_id,
            document_ids=document_ids
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to stop parsing documents: {str(e)}")
        raise typer.Exit(code=1)

# Chunk Management Commands
@app.command()
def add_chunk(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    document_id: str = typer.Option(..., "--document-id", "-i", help="Document ID"),
    content: str = typer.Option(..., "--content", "-c", help="Chunk content"),
    important_keywords: Optional[str] = typer.Option(None, "--keywords", "-k", help="Important keywords as JSON list")
):
    """Add a chunk to a document"""
    client = get_client(base_url, api_key)
    try:
        keywords = json.loads(important_keywords) if important_keywords else None
        
        result = client.add_chunk(
            dataset_id=dataset_id,
            document_id=document_id,
            content=content,
            important_keywords=keywords
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to add chunk: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def list_chunks(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    document_id: str = typer.Option(..., "--document-id", "-i", help="Document ID"),
    keywords: Optional[str] = typer.Option(None, "--keywords", "-k", help="Filter by keywords"),
    page: int = typer.Option(1, "--page", "-p", help="Page number"),
    page_size: int = typer.Option(1024, "--page-size", "-s", help="Items per page"),
    chunk_id: Optional[str] = typer.Option(None, "--chunk-id", "-c", help="Filter by chunk ID")
):
    """List chunks in a document"""
    client = get_client(base_url, api_key)
    try:
        result = client.list_chunks(
            dataset_id=dataset_id,
            document_id=document_id,
            keywords=keywords,
            page=page,
            page_size=page_size,
            chunk_id=chunk_id
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to list chunks: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def delete_chunks(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    document_id: str = typer.Option(..., "--document-id", "-i", help="Document ID"),
    chunk_ids: List[str] = typer.Option(..., "--ids", help="Chunk IDs to delete")
):
    """Delete chunks from a document"""
    client = get_client(base_url, api_key)
    try:
        result = client.delete_chunks(
            dataset_id=dataset_id,
            document_id=document_id,
            chunk_ids=chunk_ids
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to delete chunks: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def update_chunk(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    dataset_id: str = typer.Option(..., "--dataset-id", "-d", help="Dataset ID"),
    document_id: str = typer.Option(..., "--document-id", "-i", help="Document ID"),
    chunk_id: str = typer.Option(..., "--chunk-id", "-c", help="Chunk ID to update"),
    content: Optional[str] = typer.Option(None, "--content", help="New chunk content"),
    important_keywords: Optional[str] = typer.Option(None, "--keywords", "-k", help="New important keywords as JSON list"),
    available: Optional[bool] = typer.Option(None, "--available/--unavailable", help="Set chunk availability")
):
    """Update a chunk"""
    client = get_client(base_url, api_key)
    try:
        keywords = json.loads(important_keywords) if important_keywords else None
        
        result = client.update_chunk(
            dataset_id=dataset_id,
            document_id=document_id,
            chunk_id=chunk_id,
            content=content,
            important_keywords=keywords,
            available=available
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to update chunk: {str(e)}")
        raise typer.Exit(code=1)

# Chat Assistant Management Commands
@app.command()
def create_chat_assistant(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    name: str = typer.Option(..., "--name", "-n", help="Chat assistant name"),
    dataset_ids: List[str] = typer.Option(..., "--dataset-ids", "-d", help="Dataset IDs", show_default=False),
    llm: Optional[str] = typer.Option(None, "--llm", help="LLM settings as JSON string"),
    prompt: Optional[str] = typer.Option(None, "--prompt", "-p", help="Prompt instructions as JSON string"),
    avatar: Optional[str] = typer.Option(None, "--avatar", "-a", help="Base64 encoded avatar")
):
    """Create a chat assistant"""
    client = get_client(base_url, api_key)
    try:
        llm_settings = json.loads(llm) if llm else None
        prompt_settings = json.loads(prompt) if prompt else None
        
        result = client.create_chat_assistant(
            name=name,
            dataset_ids=dataset_ids,
            llm=llm_settings,
            prompt=prompt_settings,
            avatar=avatar
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to create chat assistant: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def update_chat_assistant(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    chat_id: str = typer.Option(..., "--chat-id", "-i", help="Chat assistant ID to update"),
    name: Optional[str] = typer.Option(None, "--name", "-n", help="New chat assistant name"),
    dataset_ids: Optional[List[str]] = typer.Option(None, "--dataset-ids", "-d", help="Dataset IDs", show_default=False),
    llm: Optional[str] = typer.Option(None, "--llm", help="Updated LLM settings as JSON string"),
    prompt: Optional[str] = typer.Option(None, "--prompt", "-p", help="Updated prompt instructions as JSON string"),
    avatar: Optional[str] = typer.Option(None, "--avatar", "-a", help="New avatar as base64 string")
):
    """Update chat assistant configuration"""
    client = get_client(base_url, api_key)
    try:
        datasets = json.loads(dataset_ids) if dataset_ids else None
        llm_settings = json.loads(llm) if llm else None
        prompt_settings = json.loads(prompt) if prompt else None
        
        result = client.update_chat_assistant(
            chat_id=chat_id,
            name=name,
            dataset_ids=datasets,
            llm=llm_settings,
            prompt=prompt_settings,
            avatar=avatar
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to update chat assistant: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def delete_chat_assistants(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    chat_ids: List[str] = typer.Option(..., "--ids", help="Chat assistant IDs to delete", show_default=False)
):
    """Delete chat assistants"""
    client = get_client(base_url, api_key)
    try:
        result = client.delete_chat_assistants(chat_ids=chat_ids)
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to delete chat assistants: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def list_chat_assistants(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    page: int = typer.Option(1, "--page", "-p", help="Page number"),
    page_size: int = typer.Option(30, "--page-size", "-s", help="Items per page"),
    orderby: str = typer.Option("create_time", "--orderby", "-o", help="Field to sort by"),
    desc: bool = typer.Option(True, "--desc/--asc", help="Sort descending/ascending"),
    name: Optional[str] = typer.Option(None, "--name", "-n", help="Filter by name"),
    chat_id: Optional[str] = typer.Option(None, "--id", help="Filter by chat ID")
):
    """List chat assistants"""
    client = get_client(base_url, api_key)
    try:
        result = client.list_chat_assistants(
            page=page,
            page_size=page_size,
            orderby=orderby,
            desc=desc,
            name=name,
            chat_id=chat_id
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to list chat assistants: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def create_chat_session(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    chat_id: str = typer.Option(..., "--chat-id", "-i", help="Chat assistant ID"),
    name: str = typer.Option(..., "--name", "-n", help="Session name"),
    user_id: Optional[str] = typer.Option(None, "--user-id", "-u", help="User-defined ID")
):
    """Create a session with a chat assistant"""
    client = get_client(base_url, api_key)
    try:
        result = client.create_chat_session(
            chat_id=chat_id,
            name=name,
            user_id=user_id
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to create chat session: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def update_chat_session(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    chat_id: str = typer.Option(..., "--chat-id", "-i", help="Chat assistant ID"),
    session_id: str = typer.Option(..., "--session-id", "-s", help="Session ID to update"),
    name: Optional[str] = typer.Option(None, "--name", "-n", help="New session name"),
    user_id: Optional[str] = typer.Option(None, "--user-id", "-u", help="New user ID")
):
    """Update a chat session"""
    client = get_client(base_url, api_key)
    try:
        result = client.update_chat_session(
            chat_id=chat_id,
            session_id=session_id,
            name=name,
            user_id=user_id
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to update chat session: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def list_chat_sessions(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    chat_id: str = typer.Option(..., "--chat-id", "-i", help="Chat assistant ID"),
    page: int = typer.Option(1, "--page", "-p", help="Page number"),
    page_size: int = typer.Option(30, "--page-size", "-s", help="Items per page"),
    orderby: str = typer.Option("create_time", "--orderby", "-o", help="Field to sort by"),
    desc: bool = typer.Option(True, "--desc/--asc", help="Sort descending/ascending"),
    name: Optional[str] = typer.Option(None, "--name", "-n", help="Filter by name"),
    session_id: Optional[str] = typer.Option(None, "--session-id", "-s", help="Filter by session ID"),
    user_id: Optional[str] = typer.Option(None, "--user-id", "-u", help="Filter by user ID")
):
    """List sessions associated with a chat assistant"""
    client = get_client(base_url, api_key)
    try:
        result = client.list_chat_sessions(
            chat_id=chat_id,
            page=page,
            page_size=page_size,
            orderby=orderby,
            desc=desc,
            name=name,
            session_id=session_id,
            user_id=user_id
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to list chat sessions: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def delete_chat_sessions(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    chat_id: str = typer.Option(..., "--chat-id", "-i", help="Chat assistant ID"),
    session_ids: List[str] = typer.Option(..., "--ids", help="Session IDs to delete", show_default=False)
):
    """Delete chat sessions"""
    client = get_client(base_url, api_key)
    try:
        result = client.delete_chat_sessions(
            chat_id=chat_id,
            session_ids=session_ids
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to delete chat sessions: {str(e)}")
        raise typer.Exit(code=1)

@app.command()
def converse_with_chat(
    base_url: str = typer.Option(None, "--base-url", "-u", help="RAGFlow API base URL (defaults to RAGFLOW_BASE_URL from .env)"),
    api_key: str = typer.Option(None, "--api-key", "-k", help="API key (defaults to RAGFLOW_API_KEY from .env)"),
    chat_id: str = typer.Option(..., "--chat-id", "-i", help="Chat assistant ID"),
    question: str = typer.Option(..., "--question", "-q", help="User question/input"),
    stream: bool = typer.Option(False, "--stream/--no-stream", help="Stream responses"),
    session_id: Optional[str] = typer.Option(None, "--session-id", "-s", help="Existing session ID"),
    user_id: Optional[str] = typer.Option(None, "--user-id", "-u", help="User ID")
):
    """Converse with a chat assistant"""
    client = get_client(base_url, api_key)
    try:
        result = client.converse_with_chat(
            chat_id=chat_id,
            question=question,
            stream=stream,
            session_id=session_id,
            user_id=user_id
        )
        typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Failed to converse with chat: {str(e)}")
        raise typer.Exit(code=1)

if __name__ == "__main__":
    app()
