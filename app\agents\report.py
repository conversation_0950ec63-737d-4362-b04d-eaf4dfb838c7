import time
import logging
import os
from pathlib import Path
from typing import Dict, Optional, TypedDict

from langchain_core.messages import HumanMessage

from app.agents.base import BaseAgent
from app.agents.openai import OpenAIAgent
from app.config import AgentConfig

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    ConfigurationException, WorkflowException, FileProcessingException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()

# Set layer context for agent operations
set_layer_context("business_logic")


class ReportWorkflowError(Exception):
    """工作流特定错误"""
    def __init__(self, message, action="工作流执行失败"):
        self.message = message
        self.action = action
        super().__init__(message)


class ReportAgent(BaseAgent):
    """报告生成流程控制Agent
    
    主要职责：
    - 管理报告生成流程的状态（公司、年份、季度等）
    - 处理用户输入并协调工作流执行
    - 同步UI状态与Agent内部状态
    
    Attributes:
        workspace_id: 关联的工作区ID
        agent: OpenAI Agent实例
        company_name: 当前处理的公司名称
        report_year: 报告年份
        report_quarter: 报告季度
        required_files: 需要收集的文件清单
    """

    @log_and_reraise(logging.getLogger(__name__), "report agent initialization")
    def __init__(self, config: AgentConfig):
        """初始化报告Agent with unified error handling

        Args:
            config: 包含所有配置参数的AgentConfig对象
        """
        set_operation_context("report_agent_init")

        with error_boundary("agent configuration", LayerType.BUSINESS_LOGIC):
            if not config:
                raise ValidationException(
                    message="Agent configuration is required",
                    field_name="config",
                    details="Configuration cannot be None",
                    suggested_action="Provide a valid AgentConfig object"
                )

            super().__init__(config)

        with error_boundary("OpenAI agent initialization", LayerType.BUSINESS_LOGIC):
            try:
                # 初始化OpenAI Agent实例
                self.agent = OpenAIAgent(config)
            except Exception as e:
                raise ConfigurationException(
                    message="Failed to initialize OpenAI agent",
                    details=f"OpenAI agent initialization error: {str(e)}",
                    original_exception=e,
                    suggested_action="Check OpenAI agent configuration"
                )

        with error_boundary("workspace setup", LayerType.BUSINESS_LOGIC):
            # 设置工作区ID
            self.workspace_id = config.workspace_id
            if not self.workspace_id:
                raise ValidationException(
                    message="Workspace ID is required",
                    field_name="workspace_id",
                    details="Workspace ID cannot be empty",
                    suggested_action="Provide a valid workspace ID in configuration"
                )

            # 初始化状态变量
            self.company_name = None
            self.report_year = None
            self.report_quarter = None
            self.template = None
            # 初始化所需文件结构
            self.required_files = {
                'collections': [],  # 文件列表
                'confirmed_empty': False  # 是否确认无需文件
            }

    @handle_layer_boundary(LayerType.BUSINESS_LOGIC, "check_confirmation_intent")
    def check_confirmation_intent(self, user_input: str) -> dict:
        """混合模式判断用户确认意图
        
        使用规则匹配和LLM分析相结合的方式判断用户确认意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            dict: 包含确认状态和消息的字典
        """
        set_operation_context("check_confirmation_intent")
        
        with error_boundary("confirmation_intent_check", LayerType.BUSINESS_LOGIC) as ctx:
            ctx.add_context("user_input", user_input)
            ctx.add_context("workspace_id", self.workspace_id)
            
            # 1. 先检查明确否定关键词
            if self._rule_based_negative(user_input):
                return {"confirmed": False, "message": "用户明确拒绝"}

            # 2. 检查明确肯定关键词
            if self._rule_based_confirmation(user_input):
                return {"confirmed": True, "message": "用户明确确认"}

            # 3. 规则匹配失败时使用LLM检查
            result = self._strict_llm_confirmation(user_input)
            return {
                "confirmed": bool(result),
                "message": "LLM分析确认" if result else "LLM分析拒绝"
            }

    @log_and_reraise(logging.getLogger(__name__), "rule_based_negative_check")
    def _rule_based_negative(self, input_str: str) -> bool:
        """基于规则的否定检查
        
        Args:
            input_str: 用户输入文本
            
        Returns:
            bool: 是否匹配否定关键词
        """
        with error_boundary("rule_based_negative_check", LayerType.BUSINESS_LOGIC) as ctx:
            ctx.add_context("input_str", input_str)
            
            # 定义否定关键词集合
            negative_keywords = {
                'false', '否', 'no', 'n', '不', '不要', '不行', '不同意',
                '拒绝', '取消', 'stop', '终止', '结束', '算了', '别'
            }
            return input_str.strip().lower() in negative_keywords

    @log_and_reraise(logging.getLogger(__name__), "rule_based_confirmation_check")
    def _rule_based_confirmation(self, input_str: str) -> bool:
        """基于规则的确认检查
        
        Args:
            input_str: 用户输入文本
            
        Returns:
            bool: 是否匹配肯定关键词
        """
        with error_boundary("rule_based_confirmation_check", LayerType.BUSINESS_LOGIC) as ctx:
            ctx.add_context("input_str", input_str)
            
            # 定义肯定关键词集合
            confirm_keywords = {
                'true', '是', 'yes', 'y', 
                '确认', '同意', 'ok', '好的', '要得', '可以', '对', '行',
                'continue', 'proceed', 'confirm', '继续', '开始'
            }
            return input_str.strip().lower() in confirm_keywords

    @log_and_reraise(logging.getLogger(__name__), "strict_llm_confirmation")
    def _strict_llm_confirmation(self, input_str: str) -> bool:
        """严格的LLM确认检查
        
        当规则匹配无法确定时，使用LLM进行精确判断
        
        Args:
            input_str: 用户输入文本
            
        Returns:
            bool: LLM判断的确认结果
        """
        set_operation_context("strict_llm_confirmation")
        
        with error_boundary("strict_llm_confirmation", LayerType.BUSINESS_LOGIC) as ctx:
            ctx.add_context("input_str", input_str)
            
            # 构建LLM提示词
            prompt = f"""严格判断以下输入是否表达肯定意涵，严格按格式回答：
输入：{input_str}
回答模板：[答案]true/false[/答案]"""

            # 调用LLM获取响应
            response = self.agent.llm.invoke([HumanMessage(content=prompt)])
            content = response if type(response) == str else response.content
            
            # 解析响应内容
            content = content.lower()
            return 'true' in content

    @handle_layer_boundary(LayerType.BUSINESS_LOGIC, "reset_session")
    def reset(self):
        """重置会话状态
        
        清除当前会话的所有状态，重新初始化
        """
        set_operation_context("reset_session")
        
        with error_boundary("session_reset", LayerType.BUSINESS_LOGIC) as ctx:
            ctx.add_context("workspace_id", self.workspace_id)
            
            # 重置状态变量
            self.company_name = None
            self.report_year = None
            self.report_quarter = None
            self.required_files = {
                'collections': [],
                'confirmed_empty': False
            }

    @log_and_reraise(logging.getLogger(__name__), "ensure_workspace_directory")
    def _ensure_workspace_directory(self):
        """确保工作区目录存在
        
        根据当前公司、年份创建对应的工作目录
        
        Returns:
            str: 工作区路径
        """
        set_operation_context("ensure_workspace_directory")
        
        with error_boundary("workspace_directory_creation", LayerType.BUSINESS_LOGIC) as ctx:
            if not self.company_name or not self.report_year:
                raise ValidationException(
                    message="Cannot create workspace directory without company and year",
                    field_name="company_name,report_year",
                    details=f"Current values: company_name={self.company_name}, report_year={self.report_year}",
                    suggested_action="Set company_name and report_year before creating workspace directory"
                )
                
            # 构建工作区路径
            workspace_path = f"workspaces/{self.workspace_id}/{self.company_name}/{self.report_year}"
            ctx.add_context("workspace_path", workspace_path)
            
            try:
                # 创建目录（如果不存在）
                os.makedirs(workspace_path, exist_ok=True)
                return workspace_path
            except OSError as e:
                raise FileProcessingException(
                    message="Failed to create workspace directory",
                    details=f"Error creating directory {workspace_path}: {str(e)}",
                    original_exception=e,
                    suggested_action="Check file system permissions and available disk space"
                )

    @handle_layer_boundary(LayerType.BUSINESS_LOGIC, "sync_to_ui")
    def sync_to_ui(self, ui_state: dict) -> None:
        """同步agent状态到UI
        
        将Agent内部状态同步到UI状态字典
        
        Args:
            ui_state: UI状态字典
        """
        set_operation_context("sync_to_ui")
        
        with error_boundary("sync_to_ui", LayerType.BUSINESS_LOGIC) as ctx:
            if not isinstance(ui_state, dict):
                raise ValidationException(
                    message="UI state must be a dictionary",
                    field_name="ui_state",
                    details=f"Expected dict, got {type(ui_state)}",
                    suggested_action="Provide a valid dictionary for UI state synchronization"
                )
                
            ctx.add_context("workspace_id", self.workspace_id)
            ctx.add_context("current_agent_state", {
                'company': self.company_name,
                'year': self.report_year,
                'quarter': self.report_quarter
            })

            # 更新文件计数
            if self.company_name and self.report_year:
                workspace_path = Path(f"workspaces/{self.workspace_id}/{self.company_name}/{self.report_year}")
                if workspace_path.exists():
                    # 统计有效文件数量
                    file_count = len([
                        f for f in workspace_path.glob("*.*")
                        if f.suffix.lower() in ('.pdf', '.xls', '.xlsx')
                    ])
                    ui_state['materials_count'] = file_count

            # 执行状态更新
            ui_state.update({
                'company': self.company_name,
                'year': self.report_year,
                'quarter': self.report_quarter,
                'template': self.template
            })
            # 季度单独处理确保更新
            if self.report_quarter is not None:
                ui_state['quarter'] = self.report_quarter
            else:
                ui_state['quarter'] = None

            # 当公司或年份变更时，自动创建目录
            if self.company_name and self.report_year:
                workspace_path = self._ensure_workspace_directory()

    @handle_layer_boundary(LayerType.BUSINESS_LOGIC, "sync_from_ui")
    def sync_from_ui(self, ui_state: dict) -> None:
        """从UI同步状态到agent
        
        Args:
            ui_state: UI状态字典
        """
        set_operation_context("sync_from_ui")
        
        with error_boundary("sync_from_ui", LayerType.BUSINESS_LOGIC) as ctx:
            if not isinstance(ui_state, dict):
                raise ValidationException(
                    message="UI state must be a dictionary",
                    field_name="ui_state",
                    details=f"Expected dict, got {type(ui_state)}",
                    suggested_action="Provide a valid dictionary for UI state synchronization"
                )
                
            ctx.add_context("workspace_id", self.workspace_id)
            ctx.add_context("ui_state_keys", list(ui_state.keys()))

    @handle_layer_boundary(LayerType.BUSINESS_LOGIC, "check_workspace_files")
    def check_workspace_files(self) -> tuple[list[str], str]:
        """检查并更新工作区文件状态
        
        检查工作区目录中的文件状态，更新Agent内部状态
        
        Returns:
            tuple: (文件列表, 状态) 状态可能是'exists'/'missing'/'confirmed_empty'
        """
        set_operation_context("check_workspace_files")
        
        with error_boundary("workspace_files_check", LayerType.BUSINESS_LOGIC) as ctx:
            # 基础检查 - 确保必要参数存在
            missing_params = []
            if not self.workspace_id:
                missing_params.append("workspace_id")
            if not self.company_name:
                missing_params.append("company_name")
            if not self.report_year:
                missing_params.append("report_year")
                
            if missing_params:
                raise ValidationException(
                    message="Missing required parameters for workspace file check",
                    field_name=",".join(missing_params),
                    details=f"Required parameters missing: {missing_params}",
                    suggested_action="Set all required parameters before checking workspace files"
                )
            
            # 构建工作区路径
            workspace_path = Path(f"workspaces/{self.workspace_id}/{self.company_name}/{self.report_year}")
            ctx.add_context("workspace_path", str(workspace_path))
            
            try:
                # 检查路径是否存在
                if not workspace_path.exists():
                    ctx.add_context("workspace_exists", "false")
                    self.required_files['collections'] = []
                    return [], 'missing'
                
                # 获取所有有效文件
                valid_extensions = {'.pdf', '.xls', '.xlsx'}
                existing_files = []
                
                for file_path in workspace_path.glob("*.*"):
                    if file_path.suffix.lower() in valid_extensions:
                        try:
                            # 验证文件可读性
                            if file_path.is_file() and os.access(file_path, os.R_OK):
                                existing_files.append(file_path.name)
                        except (OSError, PermissionError) as e:
                            ctx.add_context("file_access_error", f"{file_path.name}: {str(e)}")
                            continue
                
                # 确定状态
                if existing_files:
                    status = 'exists'
                elif self.required_files.get('confirmed_empty', False):
                    status = 'confirmed_empty'
                else:
                    status = 'missing'
                
                # 更新agent状态
                self.required_files['collections'] = existing_files
                ctx.add_context("file_count", len(existing_files))
                ctx.add_context("status", status)
                
                return existing_files, status
                
            except Exception as e:
                if isinstance(e, (ValidationException, FileProcessingException)):
                    raise
                
                raise FileProcessingException(
                    message="Failed to check workspace files",
                    details=f"Error accessing workspace directory: {str(e)}",
                    original_exception=e,
                    suggested_action="Check workspace directory permissions and file system"
                )

    @handle_layer_boundary(LayerType.BUSINESS_LOGIC, "process_message")
    def process_message(self, message: str,
                        current_company: Optional[str] = None,
                        current_year: Optional[str] = None,
                        current_quarter: Optional[str] = None,
                        extra_context: Optional[Dict] = None) -> Dict[str, Optional[str]]:
        """处理用户消息并返回四要素及响应
        
        核心消息处理方法，协调整个报告生成流程
        
        Args:
            message: 用户输入消息
            current_company: 当前公司名称
            current_year: 当前年份
            current_quarter: 当前季度
            extra_context: 额外上下文信息
            
        Returns:
            dict: 包含处理结果的状态字典
        """
        set_operation_context("process_message")
        
        with error_boundary("message_processing", LayerType.BUSINESS_LOGIC) as ctx:
            ctx.add_context("message", message)
            ctx.add_context("current_company", current_company)
            ctx.add_context("current_year", current_year)
            ctx.add_context("current_quarter", current_quarter)
            ctx.add_context("workspace_id", self.workspace_id)
            
            # 同步当前状态 - 确保季度同步
            if current_company:
                self.company_name = current_company
            if current_year:
                self.report_year = current_year
            if current_quarter is not None:  # 允许空值
                self.report_quarter = current_quarter
            
            # 动态导入工作流创建函数
            from app.workflows.report_workflow import create_report_workflow
            
            # 初始化工作流
            workflow = create_report_workflow(self)
            
            # 初始状态设置
            initial_state = {
                "last_message": message,
                "company_name": self.company_name,
                "report_year": self.report_year,
                "report_quarter": self.report_quarter,
                "materials_status": 'missing',  # 初始假设资料缺失
                "intent": None,
                "missing_elements": None,
                "company_options": getattr(self, 'company_options', None),  # 确保company_options传递
                "conversation_history": [],  # 初始化对话历史
                "conversation_phase": "initial"
            }
            
            # 合并额外上下文
            if extra_context:
                initial_state.update(extra_context)
            if extra_context and 'company_options' in extra_context:
                initial_state['company_options'] = extra_context['company_options']
            
            # 检查资料状态
            workspace_path = Path(f"workspaces/{self.workspace_id}/{self.company_name}/{self.report_year}")
            if workspace_path.exists():
                # 检查是否存在Excel或PDF文件
                if any(workspace_path.glob("*.[xX][lL][sS]*")) or any(workspace_path.glob("*.pdf")):
                    initial_state['materials_status'] = 'exists'
            
            # 执行工作流
            final_state = workflow.invoke(initial_state)
            
            # 更新agent状态 - 只更新非None的值
            if final_state.get('company_name') is not None:
                self.company_name = final_state['company_name']
            if final_state.get('report_year') is not None:
                self.report_year = final_state['report_year']
            if final_state.get('report_quarter') is not None:
                self.report_quarter = final_state['report_quarter']
            
            # 构建结果字典
            result = {
                "company": self.company_name,
                "year": self.report_year,
                "quarter": self.report_quarter,
                "message": final_state['last_message'],
                "status": "success"
            }
            
            return result
