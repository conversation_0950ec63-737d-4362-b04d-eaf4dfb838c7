"""
PDF Processing Performance Configuration

This module provides configuration options for optimizing PDF processing performance
while maintaining high accuracy for financial document processing.
"""

import os
from dataclasses import dataclass
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

@dataclass
class PDFProcessingConfig:
    """Configuration for high-accuracy PDF processing with performance optimizations."""

    # Model configuration - all enabled for maximum accuracy
    enable_layout_detection: bool = True
    enable_table_detection: bool = True
    enable_ocr: bool = True
    enable_image_extraction: bool = True
    enable_form_detection: bool = True

    # Performance tuning while maintaining accuracy
    batch_size: int = 4
    max_pages_per_batch: int = 4
    confidence_threshold: float = 0.9  # High confidence for accuracy

    # Memory optimization
    enable_model_caching: bool = True
    max_memory_usage_mb: int = 8192  # Higher memory for better performance

    # Timeout settings
    processing_timeout_seconds: int = 600  # Longer timeout for accuracy
    model_load_timeout_seconds: int = 180

class PDFPerformanceOptimizer:
    """Manages high-accuracy PDF processing with performance optimizations."""

    @classmethod
    def get_config(cls) -> PDFProcessingConfig:
        """Get high-accuracy configuration with performance optimizations."""
        config = PDFProcessingConfig()
        logger.info("Using high-accuracy PDF processing configuration")
        return config

    @classmethod
    def get_marker_config(cls) -> Dict[str, Any]:
        """Get marker library configuration optimized for high accuracy."""
        config = cls.get_config()

        marker_config = {
            "batch_size": config.batch_size,
            "max_pages": config.max_pages_per_batch,
            "confidence_threshold": config.confidence_threshold,
            # High accuracy settings - all features enabled
            "force_ocr": True,
            "extract_images": True,
            "detect_handwriting": True,
            "extract_tables": True,
            "extract_forms": True,
            "preserve_formatting": True,
            "detect_charts": True,
        }

        return marker_config
    
    @classmethod
    def optimize_for_financial_documents(cls, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply optimizations specific to financial documents."""
        optimized = base_config.copy()
        
        # Financial documents typically have:
        # - Structured tables (high priority)
        # - Minimal images (can disable)
        # - Standard fonts (OCR not always needed)
        # - Forms and structured data
        
        optimized.update({
            "prioritize_tables": True,
            "table_confidence_threshold": 0.7,  # Lower threshold for financial tables
            "extract_images": False,  # Financial docs rarely need image extraction
            "detect_charts": True,  # But may have charts/graphs
            "preserve_formatting": True,  # Important for financial data
        })
        
        return optimized

def get_environment_config() -> PDFProcessingConfig:
    """Get high-accuracy configuration with optional environment variable overrides."""
    config = PDFPerformanceOptimizer.get_config()

    # Override with environment variables if present (while maintaining high accuracy)
    if os.getenv("PDF_BATCH_SIZE"):
        config.batch_size = int(os.getenv("PDF_BATCH_SIZE"))

    if os.getenv("PDF_CONFIDENCE_THRESHOLD"):
        # Ensure minimum confidence threshold for accuracy
        threshold = float(os.getenv("PDF_CONFIDENCE_THRESHOLD"))
        config.confidence_threshold = max(threshold, 0.85)  # Minimum 0.85 for accuracy

    if os.getenv("PDF_PROCESSING_TIMEOUT"):
        config.processing_timeout_seconds = int(os.getenv("PDF_PROCESSING_TIMEOUT"))

    if os.getenv("PDF_MAX_MEMORY_MB"):
        config.max_memory_usage_mb = int(os.getenv("PDF_MAX_MEMORY_MB"))

    return config

# Performance monitoring utilities
class PerformanceMonitor:
    """Monitor and log PDF processing performance."""
    
    def __init__(self):
        self.metrics = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation."""
        import time
        self.metrics[operation] = {"start": time.time()}
    
    def end_timer(self, operation: str):
        """End timing an operation and log results."""
        import time
        if operation in self.metrics:
            elapsed = time.time() - self.metrics[operation]["start"]
            self.metrics[operation]["duration"] = elapsed
            logger.info(f"Performance: {operation} completed in {elapsed:.2f}s")
            return elapsed
        return 0
    
    def get_summary(self) -> Dict[str, float]:
        """Get performance summary."""
        return {op: data.get("duration", 0) for op, data in self.metrics.items()}
