import argparse
from pathlib import Path
from .processor import MarkdownProcessor

def main():
    parser = argparse.ArgumentParser(
        description='分割Markdown文件：按标题分段，保留含表格的章节',
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument('files', nargs='+', help='输入文件路径（支持通配符）\n示例: python -m md_splitter *.md')
    parser.add_argument('-o', '--output-dir', default='.', help='输出目录')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    processor = MarkdownProcessor()
    
    total_files = 0
    success_files = 0
    
    for pattern in args.files:
        for path in Path().glob(pattern):
            if not path.is_file():
                continue
            try:
                total_files += 1
                count = processor.process_file(str(path), args.output_dir)
                success_files += 1
                if args.verbose:
                    print(f"✅ 成功处理 {path.name}，生成 {count} 个文件")
            except Exception as e:
                print(f"❌ 处理 {path.name} 失败: {str(e)}")
    
    print(f"\n处理完成：{success_files}/{total_files} 文件成功")
    print(f"输出目录：{Path(args.output_dir).resolve()}")

if __name__ == "__main__":
    main()
