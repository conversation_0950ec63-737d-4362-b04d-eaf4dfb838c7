#!/usr/bin/env python3
"""
Initialize test users in the database
"""
import os
import sqlite3
from passlib.context import CryptContext

# Configuration
DB_PATH = "data/users.db"
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password):
    return pwd_context.hash(password)

def init_database():
    """Initialize database with test users"""
    # Create data directory if not exists
    os.makedirs("data", exist_ok=True)
    
    # Connect to database
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Create users table if not exists
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            username TEXT UNIQUE,
            password_hash TEXT,
            workspace_id TEXT
        )
    ''')
    
    # Create templates table if not exists
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS templates (
            id INTEGER PRIMARY KEY,
            name TEXT,
            workspace_id TEXT,
            path TEXT
        )
    ''')
    
    # Test users data
    test_users = [
        ("test", "test123", "test_workspace"),
        ("user1", "user123", "dept_finance"),
        ("user2", "user123", "dept_risk"),
        ("admin", "admin123", "admin_workspace")
    ]
    
    # Insert test users
    for username, password, workspace_id in test_users:
        password_hash = get_password_hash(password)
        try:
            cursor.execute(
                "INSERT OR REPLACE INTO users (username, password_hash, workspace_id) VALUES (?, ?, ?)",
                (username, password_hash, workspace_id)
            )
            print(f"✅ Created user: {username} -> {workspace_id}")
        except sqlite3.IntegrityError:
            print(f"⚠️  User {username} already exists")
    
    # Create workspace directories
    for _, _, workspace_id in test_users:
        workspace_dir = os.path.join("workspaces", workspace_id)
        template_dir = os.path.join("templates", workspace_id)
        os.makedirs(workspace_dir, exist_ok=True)
        os.makedirs(template_dir, exist_ok=True)
        print(f"📁 Created directories for workspace: {workspace_id}")
    
    # Create default template for test_workspace
    default_template_content = """# {{company_name}} 市场分析报告

## 公司概况
{{company_name}} 是一家专业的金融服务公司，成立于{{establishment_year}}年。

## 财务状况分析
### 资产负债表
- 总资产：{{total_assets}}万元
- 总负债：{{total_liabilities}}万元  
- 净资产：{{net_assets}}万元
- 资产负债率：{{debt_ratio}}%

### 利润表
- 营业收入：{{revenue}}万元
- 营业成本：{{cost}}万元
- 净利润：{{net_profit}}万元
- 净利润率：{{profit_margin}}%

## 业务数据分析
### 放贷规模
- 累计放贷：{{total_loans}}万元
- 在贷余额：{{outstanding_balance}}万元
- 新增放贷：{{new_loans}}万元

### 风险指标
- 不良贷款率：{{npl_rate}}%
- 逾期率：{{overdue_rate}}%
- 拨备覆盖率：{{provision_coverage}}%

### 客户结构
- 个人客户：{{individual_customers}}户
- 企业客户：{{corporate_customers}}户
- 平均贷款金额：{{avg_loan_amount}}万元

## 市场表现
### 市场份额
{{company_name}} 在{{year}}年度在当地消费金融市场占有率为{{market_share}}%。

### 竞争优势
1. 风控体系完善
2. 产品创新能力强
3. 客户服务质量高
4. 技术平台先进

## 风险管理
### 信用风险
- 建立了完善的信用评估体系
- 实施分层风险管理策略
- 不良率控制在行业平均水平以下

### 操作风险
- 完善的内控制度
- 定期风险排查
- 应急预案健全

## 发展前景
### 市场机遇
1. 消费升级带来的市场需求增长
2. 金融科技发展提供技术支撑
3. 监管政策逐步完善

### 发展策略
1. 深化数字化转型
2. 拓展业务覆盖范围
3. 加强风险管控能力
4. 提升客户服务水平

## 总结
{{company_name}} 在{{year}}年度整体表现良好，各项指标稳健增长。公司将继续坚持稳健经营的理念，在控制风险的前提下，积极拓展业务，为股东创造更大价值。

---
*报告生成时间：{{report_date}}*
*数据来源：{{company_name}} {{year}}年度财务报表及业务数据*
"""
    
    template_path = os.path.join("templates", "test_workspace", "市场.md")
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(default_template_content)
    
    # Insert template record
    cursor.execute(
        "INSERT OR REPLACE INTO templates (name, workspace_id, path) VALUES (?, ?, ?)",
        ("市场", "test_workspace", template_path)
    )
    print(f"📄 Created default template: 市场.md")
    
    conn.commit()
    conn.close()
    print("\n🎉 Database initialization completed!")
    print("\n📋 Available test users:")
    print("- test / test123 (test_workspace)")
    print("- user1 / user123 (dept_finance)")
    print("- user2 / user123 (dept_risk)")
    print("- admin / admin123 (admin_workspace)")

if __name__ == "__main__":
    init_database()
