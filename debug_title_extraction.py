#!/usr/bin/env python3
"""
Debug the title extraction process
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def debug_title_extraction():
    """Debug the title extraction step by step"""
    print("="*80)
    print("DEBUGGING TITLE EXTRACTION")
    print("="*80)
    
    # Read the original input file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_资产负债表.md"
    
    print("Original content:")
    print("-" * 40)
    print(content[:300] + "..." if len(content) > 300 else content)
    
    print(f"\nFilename: {filename}")
    
    # Create reformer
    reformer = MarkdownTableReformer()
    
    # Parse the table to see the structure
    print("\n" + "="*60)
    print("PARSING TABLE STRUCTURE")
    print("="*60)
    
    table_data, separator_line, markdown_headers = reformer.parse_markdown_table(content)
    print(f"Separator line: {separator_line}")
    print(f"Markdown headers: {markdown_headers}")
    print(f"Total rows: {len(table_data)}")
    
    # Test title extraction from rows
    print("\n" + "="*60)
    print("TITLE EXTRACTION FROM ROWS")
    print("="*60)
    
    header_rows = table_data[:separator_line] if separator_line > 0 else []
    print(f"Header rows ({len(header_rows)}):")
    
    for i, row in enumerate(header_rows):
        print(f"  Row {i+1}: {row}")
        title = reformer.extract_title_from_row(row)
        if title:
            print(f"    -> Extracted title: '{title}'")
    
    # Test filename processing
    print("\n" + "="*60)
    print("FILENAME PROCESSING")
    print("="*60)
    
    # Test the new filename logic
    base_filename = filename.replace('.md', '').replace('_adjusted', '').replace('_reformed', '')
    print(f"Base filename: '{base_filename}'")
    
    import re
    has_chinese = re.search(r'[\u4e00-\u9fff]', base_filename)
    print(f"Contains Chinese characters: {bool(has_chinese)}")
    
    if has_chinese:
        print(f"Would use filename as title: '{base_filename}'")
    
    # Test the full process
    print("\n" + "="*60)
    print("FULL REFORM PROCESS")
    print("="*60)
    
    result = reformer.process_table(content, filename)
    
    # Extract metadata to see what was actually set
    import json
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        print("Final metadata:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
    else:
        print("No metadata found in result")

def main():
    """Main debug function"""
    try:
        debug_title_extraction()
        return 0
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
