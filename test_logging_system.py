#!/usr/bin/env python3
"""
Test script for the comprehensive 4-file logging system.

This script tests all four logging components:
1. streamlit.log - Streamlit UI application logs
2. server.log - Server/API component logs  
3. crawler.log - Crawler component logs
4. cli.log - CLI component logs

Usage:
    python test_logging_system.py
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

# Import logging configurations
from app.logging_config import setup_logging, get_app_component_logger
from app.logging_base import set_layer_context, set_operation_context
from crawler.logging_config import setup_crawler_logging, set_crawler_session_context

def test_streamlit_logging():
    """Test Streamlit UI logging"""
    print("\n=== Testing Streamlit Logging ===")
    
    # Get streamlit logger
    logger = get_app_component_logger('streamlit', 'app.main')
    set_operation_context('streamlit_test')
    
    logger.info("Streamlit application started")
    logger.debug("Loading Streamlit configuration")
    logger.warning("Streamlit warning: torch.classes issue detected")
    logger.error("Streamlit error: Failed to load component")
    
    # Test with different module names
    main_logger = logging.getLogger('app')
    main_logger.info("Main Streamlit app logger test")
    
    print("✅ Streamlit logging test completed")


def test_server_logging():
    """Test Server/API logging"""
    print("\n=== Testing Server/API Logging ===")
    
    # Get server logger
    logger = get_app_component_logger('server', 'app.api.server')
    set_operation_context('server_test')
    
    logger.info("Server starting on port 8100")
    logger.debug("Loading API endpoints")
    logger.warning("Server warning: High memory usage detected")
    logger.error("Server error: Database connection failed")
    
    # Test uvicorn logging
    uvicorn_logger = logging.getLogger('uvicorn')
    uvicorn_logger.info("Uvicorn server started")
    
    # Test business logic routing to server
    workflow_logger = logging.getLogger('app.workflows')
    workflow_logger.info("Workflow execution started")
    
    utils_logger = logging.getLogger('app.utils')
    utils_logger.info("Utility function called")
    
    print("✅ Server/API logging test completed")


def test_crawler_logging():
    """Test Crawler logging"""
    print("\n=== Testing Crawler Logging ===")
    
    # Get crawler logger
    from crawler.logging_config import get_crawler_logger
    logger = get_crawler_logger('crawler.server')
    set_operation_context('crawler_test')
    set_crawler_session_context('test_session_123')
    
    logger.info("Crawler server started")
    logger.debug("Browser automation initialized")
    logger.warning("Crawler warning: Login required")
    logger.error("Crawler error: Page load timeout")
    
    # Test different crawler components
    core_logger = logging.getLogger('crawler.core')
    core_logger.info("Crawler core operation")
    
    streamlit_crawler_logger = logging.getLogger('crawler.streamlit_app')
    streamlit_crawler_logger.info("Crawler Streamlit UI operation")
    
    print("✅ Crawler logging test completed")


def test_cli_logging():
    """Test CLI logging"""
    print("\n=== Testing CLI Logging ===")
    
    # Get CLI logger
    logger = get_app_component_logger('cli', 'app.cli.data_tools_cli')
    set_operation_context('cli_test')
    
    logger.info("CLI tool started")
    logger.debug("Processing command line arguments")
    logger.warning("CLI warning: File not found")
    logger.error("CLI error: Invalid command syntax")
    
    # Test different CLI modules
    cli_logger = logging.getLogger('app.cli')
    cli_logger.info("CLI engine operation")
    
    main_cli_logger = logging.getLogger('__main__')
    main_cli_logger.info("Main CLI script execution")
    
    print("✅ CLI logging test completed")


def test_sensitive_data_filtering():
    """Test sensitive data filtering"""
    print("\n=== Testing Sensitive Data Filtering ===")
    
    logger = get_app_component_logger('server')
    
    # Test API key masking
    logger.info("API key: sk-1234567890abcdef1234567890abcdef")
    logger.info("Private key: pk-abcdef1234567890abcdef1234567890")
    logger.info("Regular text should not be masked")
    
    print("✅ Sensitive data filtering test completed")


def check_log_files():
    """Check if all 4 log files are created"""
    print("\n=== Checking Log Files ===")
    
    logs_dir = Path("logs")
    expected_files = ["streamlit.log", "server.log", "crawler.log", "cli.log"]
    
    for log_file in expected_files:
        file_path = logs_dir / log_file
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {log_file} exists ({size} bytes)")
        else:
            print(f"❌ {log_file} not found")
    
    print("\n=== Log Directory Contents ===")
    if logs_dir.exists():
        for item in logs_dir.iterdir():
            if item.is_file():
                size = item.stat().st_size
                print(f"📄 {item.name} ({size} bytes)")


def main():
    """Main test function"""
    print("🧪 Testing Comprehensive 4-File Logging System")
    print("=" * 50)
    
    # Setup logging systems
    print("Setting up logging configuration...")
    setup_logging()
    setup_crawler_logging()
    
    # Run all tests
    test_streamlit_logging()
    test_server_logging()
    test_crawler_logging()
    test_cli_logging()
    test_sensitive_data_filtering()
    
    # Check results
    check_log_files()
    
    print("\n" + "=" * 50)
    print("🎉 Logging system test completed!")
    print("\nCheck the logs/ directory for the following files:")
    print("  - streamlit.log (Streamlit UI logs)")
    print("  - server.log (Server/API logs)")
    print("  - crawler.log (Crawler logs)")
    print("  - cli.log (CLI logs)")


if __name__ == "__main__":
    main()
