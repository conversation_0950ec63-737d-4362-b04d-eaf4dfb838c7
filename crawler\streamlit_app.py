import streamlit as st
import requests
from io import BytesIO
from PIL import Image
import time
import logging
from fastapi import Response
import os

# Import unified error handling framework
from app.exceptions import LayerType, ErrorSeverity
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context
from crawler.exceptions import (
    CrawlerUIException, UserInputException, NetworkException,
    CrawlerLayerType
)

# Import enhanced crawler logging
from crawler.logging_config import (
    ui_logger, set_crawler_page_context, log_operation_timing
)

# Setup enhanced logging
setup_logging()
logger = ui_logger

# Set layer context for streamlit app
set_layer_context("presentation")

# Server configuration
SERVER_URL = os.getenv("CRAWLER_SERVER_URL", "http://localhost:8000")
logger.info(f"Configured server URL: {SERVER_URL}")


# Helper functions with unified error handling
@handle_layer_boundary(CrawlerLayerType.PRESENTATION, "status command processing")
def process_status_command(url: str) -> dict:
    """Process status command with unified error handling"""
    set_operation_context("status_command")
    logger.info(f"Processing status command for URL: {url}")

    # Validate URL input
    if not url or not url.startswith(('http://', 'https://')):
        raise UserInputException(
            message="Invalid URL format",
            input_field="url",
            details=f"URL must start with http:// or https://, got: {url}",
            suggested_action="Please provide a valid URL starting with http:// or https://"
        )

    # Make API request with error handling
    with error_boundary("status API request", CrawlerLayerType.PRESENTATION):
        try:
            logger.debug(f"Making GET request to {SERVER_URL}/status")
            response = requests.get(f"{SERVER_URL}/status", params={"url": url}, timeout=30)
            response.raise_for_status()
            logger.debug(f"Received response: {response.status_code}")
            return response.json()
        except requests.exceptions.Timeout:
            raise NetworkException(
                message="Request timeout",
                details="Server did not respond within 30 seconds",
                suggested_action="Check server status and try again"
            )
        except requests.exceptions.ConnectionError:
            raise NetworkException(
                message="Connection failed",
                details=f"Could not connect to server at {SERVER_URL}",
                suggested_action="Check if the crawler server is running"
            )
        except requests.exceptions.HTTPError as e:
            raise NetworkException(
                message="HTTP error",
                status_code=e.response.status_code if e.response else None,
                details=f"Server returned error: {e}",
                suggested_action="Check server logs for details"
            )


@handle_layer_boundary(CrawlerLayerType.PRESENTATION, "login command processing")
def process_login_command(url: str) -> dict:
    """Process login command with unified error handling"""
    set_operation_context("login_command")
    logger.info(f"Processing login command for URL: {url}")

    # Validate URL input
    if not url or not url.startswith(('http://', 'https://')):
        raise UserInputException(
            message="Invalid URL format",
            input_field="url",
            details=f"URL must start with http:// or https://, got: {url}",
            suggested_action="Please provide a valid URL starting with http:// or https://"
        )

    # Make API request with error handling
    with error_boundary("login API request", CrawlerLayerType.PRESENTATION):
        try:
            logger.debug(f"Making POST request to {SERVER_URL}/login")
            response = requests.post(f"{SERVER_URL}/login", timeout=60)
            response.raise_for_status()
            logger.debug(f"Received response: {response.status_code}")
            return response.json()
        except requests.exceptions.Timeout:
            raise NetworkException(
                message="Login request timeout",
                details="Login process did not complete within 60 seconds",
                suggested_action="Login process may take time, please wait and check status"
            )
        except requests.exceptions.ConnectionError:
            raise NetworkException(
                message="Connection failed during login",
                details=f"Could not connect to server at {SERVER_URL}",
                suggested_action="Check if the crawler server is running"
            )
        except requests.exceptions.HTTPError as e:
            raise NetworkException(
                message="Login HTTP error",
                status_code=e.response.status_code if e.response else None,
                details=f"Server returned error during login: {e}",
                suggested_action="Check server logs for login details"
            )


@handle_layer_boundary(CrawlerLayerType.PRESENTATION, "screenshot retrieval")
def get_screenshot() -> bytes:
    """Get screenshot from crawler server with unified error handling"""
    set_operation_context("screenshot_retrieval")
    logger.debug(f"Making GET request to {SERVER_URL}/screenshot")

    with error_boundary("screenshot API request", CrawlerLayerType.PRESENTATION):
        try:
            response = requests.get(f"{SERVER_URL}/screenshot", timeout=30)
            response.raise_for_status()
            logger.debug(f"Received screenshot response: {response.status_code}")
            return response.content
        except requests.exceptions.Timeout:
            raise NetworkException(
                message="Screenshot request timeout",
                details="Server did not respond within 30 seconds",
                suggested_action="Check server status and try again"
            )
        except requests.exceptions.ConnectionError:
            raise NetworkException(
                message="Connection failed during screenshot request",
                details=f"Could not connect to server at {SERVER_URL}",
                suggested_action="Check if the crawler server is running"
            )
        except requests.exceptions.HTTPError as e:
            raise NetworkException(
                message="Screenshot HTTP error",
                status_code=e.response.status_code if e.response else None,
                details=f"Server returned error during screenshot: {e}",
                suggested_action="Check server logs for screenshot details"
            )


def display_error_message(error_msg: str, suggested_action: str = None):
    """Display user-friendly error message in Streamlit"""
    error_content = f"❌ {error_msg}"
    if suggested_action:
        error_content += f"\n\n💡 {suggested_action}"
    st.error(error_content)

st.set_page_config(layout="wide")
st.title("Remote Browser Control")

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []

if "current_url" not in st.session_state:
    st.session_state.current_url = None

# Sidebar for chat
with st.sidebar:
    st.header("Chat Panel")
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    if prompt := st.chat_input("Enter command (e.g. status https://www.qcc.com)"):
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)

        # Process command with unified error handling
        if prompt.startswith("status"):
            url = prompt.split(" ")[1] if len(prompt.split(" ")) > 1 else "https://www.qcc.com"

            try:
                result = process_status_command(url)
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": f"Status for {url}: {result}"
                })
                st.session_state.current_url = url
            except (UserInputException, NetworkException, CrawlerUIException) as e:
                e.log_error(logger)
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": f"❌ {e.message}\n\n💡 {e.suggested_action}"
                })
            except Exception as e:
                # Handle unexpected exceptions with unified error handling
                ui_exc = CrawlerUIException(
                    message="Status command failed",
                    details=f"Unexpected error during status check: {str(e)}",
                    original_exception=e,
                    suggested_action="Please try again or contact support"
                )
                ui_exc.log_error(logger)
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": f"❌ {ui_exc.message}\n\n💡 {ui_exc.suggested_action}"
                })

        elif prompt.startswith("login"):
            url = prompt.split(" ")[1] if len(prompt.split(" ")) > 1 else "https://www.qcc.com"

            try:
                result = process_login_command(url)

                # Handle different login results
                if result.get("status") == "qr_code_required":
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": "🔐 QR Code login required. Please scan the QR code displayed in the browser to complete login."
                    })
                elif result.get("logged_in"):
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": f"✅ Login successful! Status: {result.get('status', 'logged_in')}"
                    })
                elif result.get("status") == "already_logged_in":
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": "ℹ️ Already logged in. No action needed."
                    })
                else:
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": f"🔄 Login status: {result.get('status', 'unknown')}\nMessage: {result.get('message', 'No additional information')}"
                    })

                st.session_state.current_url = url

            except (UserInputException, NetworkException, CrawlerUIException) as e:
                e.log_error(logger)
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": f"❌ Login failed: {e.message}\n\n💡 {e.suggested_action}"
                })
            except Exception as e:
                # Handle unexpected exceptions with unified error handling
                ui_exc = CrawlerUIException(
                    message="Login command failed",
                    details=f"Unexpected error during login: {str(e)}",
                    original_exception=e,
                    suggested_action="Please try again or contact support"
                )
                ui_exc.log_error(logger)
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": f"❌ {ui_exc.message}\n\n💡 {ui_exc.suggested_action}"
                })

# Main content area - browser display
col1, col2 = st.columns([1, 3])

with col1:
    st.header("Chat History")
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

with col2:
    st.header("Browser Content")
    if st.session_state.current_url:
        logger.info(f"Displaying browser content for URL: {st.session_state.current_url}")

        try:
            # Get screenshot with unified error handling
            screenshot_data = get_screenshot()
            logger.debug("Successfully received screenshot")
            img = Image.open(BytesIO(screenshot_data))
            st.image(img, caption=f"Current page: {st.session_state.current_url}")

        except (NetworkException, CrawlerUIException) as e:
            e.log_error(logger)
            display_error_message(e.message, e.suggested_action)

        except Exception as e:
            # Handle unexpected exceptions with unified error handling
            ui_exc = CrawlerUIException(
                message="Screenshot display failed",
                details=f"Failed to load browser content: {str(e)}",
                original_exception=e,
                suggested_action="Please check server connection and try refreshing"
            )
            ui_exc.log_error(logger)
            display_error_message(f"{ui_exc.message}: {ui_exc.details}",
                                 ui_exc.suggested_action)

        # Refresh button
        if st.button("Refresh"):
            logger.info("Refresh button clicked")
            st.rerun()
    else:
        st.info("Enter a command to view browser content")

# Auto-refresh every 5 seconds
time.sleep(5)
st.rerun()
