#!/usr/bin/env python3
"""
Test the scoring for the "科目" row specifically
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reheader import MarkdownTableHeaderAdjuster

def test_kemu_row_scoring():
    """Test scoring for the row containing '科目'"""
    print("="*80)
    print("TESTING '科目' ROW SCORING")
    print("="*80)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Read the original file and parse it
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    rows = adjuster.parse_table(content)
    
    print("All rows in the table:")
    for i, row in enumerate(rows):
        print(f"Row {i+1}: {row}")
    
    print("\n" + "="*60)
    print("DETAILED SCORING ANALYSIS")
    print("="*60)
    
    # Find the row with "科目"
    kemu_row_idx = None
    for i, row in enumerate(rows):
        if '科目' in ' '.join(row):
            kemu_row_idx = i
            print(f"Found '科目' in Row {i+1}: {row}")
            break
    
    if kemu_row_idx is None:
        print("❌ No row containing '科目' found!")
        return
    
    # Test scoring for all rows
    print(f"\nScoring analysis for all rows:")
    print("-" * 60)
    
    for i, row in enumerate(rows):
        if adjuster.is_separator_row(row):
            print(f"Row {i+1}: SEPARATOR - {row}")
            continue
            
        # Calculate comprehensive score
        score = adjuster.calculate_comprehensive_header_score(row, i, len(rows), rows)
        
        # Get individual components
        keyword_count = adjuster.count_header_keywords(row)
        has_strong = adjuster.has_strong_header_keywords(row)
        row_density = adjuster.calculate_row_density(row)
        string_density = adjuster.calculate_string_density(row)
        
        # Calculate expected keyword score
        keyword_score = keyword_count * 15
        strong_bonus = 25 if has_strong else 0
        total_keyword_score = keyword_score + strong_bonus
        
        print(f"Row {i+1}: Total Score = {score:.2f}")
        print(f"  Content: {row}")
        print(f"  Keywords: {keyword_count} × 15 = {keyword_score}")
        print(f"  Strong bonus: {strong_bonus}")
        print(f"  Total keyword score: {total_keyword_score}")
        print(f"  Row density: {row_density:.2f}")
        print(f"  String density: {string_density:.2f}")
        
        if i == kemu_row_idx:
            print(f"  ⭐ THIS IS THE '科目' ROW - Should score highest!")
        print()
    
    # Test the header candidate detection
    print("="*60)
    print("HEADER CANDIDATE RANKING")
    print("="*60)
    
    candidates = adjuster.find_all_header_candidates(rows)
    print(f"Found {len(candidates)} candidates:")
    
    for i, (row_idx, score) in enumerate(candidates):
        row = rows[row_idx]
        marker = "⭐ '科目' ROW" if row_idx == kemu_row_idx else ""
        print(f"  {i+1}. Row {row_idx+1}: Score {score:.2f} {marker}")
        print(f"      Content: {row}")
    
    # Check if the '科目' row is ranked #1
    if candidates and candidates[0][0] == kemu_row_idx:
        print(f"\n✅ SUCCESS: '科目' row is ranked #1 with score {candidates[0][1]:.2f}")
    else:
        print(f"\n❌ PROBLEM: '科目' row is not ranked #1")
        if candidates:
            top_row_idx = candidates[0][0]
            top_score = candidates[0][1]
            print(f"    Top row is Row {top_row_idx+1} with score {top_score:.2f}")
            print(f"    Content: {rows[top_row_idx]}")
            
            if kemu_row_idx is not None:
                kemu_score = next((score for row_idx, score in candidates if row_idx == kemu_row_idx), 0)
                print(f"    '科目' row score: {kemu_score:.2f}")
                print(f"    Score difference: {top_score - kemu_score:.2f}")

def main():
    """Main test function"""
    try:
        test_kemu_row_scoring()
        return 0
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
