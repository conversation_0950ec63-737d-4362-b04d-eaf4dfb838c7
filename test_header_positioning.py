#!/usr/bin/env python3
"""
Test header positioning for the financial statement
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reheader import MarkdownTableHeaderAdjuster

def test_header_positioning():
    """Test if the header positioning can be improved"""
    print("="*80)
    print("TESTING HEADER POSITIONING")
    print("="*80)
    
    # Read the file
    with open('test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    print("Current table structure:")
    lines = content.split('\n')
    for i, line in enumerate(lines[3:10]):  # Skip metadata and title
        print(f"  Line {i+4}: {line}")
    
    print()
    
    # Test header adjustment
    print("Testing header adjustment...")
    try:
        result = adjuster.adjust_table(content)
        print("✅ Header adjustment successful")
        
        # Show the adjusted structure
        result_lines = result.split('\n')
        print("\nAdjusted table structure:")
        for i, line in enumerate(result_lines[3:10]):  # Skip metadata and title
            print(f"  Line {i+4}: {line}")
        
        # Check if the separator is properly positioned
        separator_found = False
        xiangmu_line = -1
        separator_line = -1
        
        for i, line in enumerate(result_lines):
            if '项 目' in line and '本年累计' in line:
                xiangmu_line = i
            elif '---' in line and separator_line == -1:
                separator_line = i
        
        print(f"\n'项 目' line: {xiangmu_line}")
        print(f"Separator line: {separator_line}")
        
        if xiangmu_line > 0 and separator_line > 0:
            if separator_line == xiangmu_line + 1:
                print("✅ Separator correctly positioned after '项 目' row")
                return True
            elif separator_line < xiangmu_line:
                print("❌ Separator positioned before '项 目' row - should be after")
                return False
            else:
                print(f"❌ Separator positioned {separator_line - xiangmu_line} lines after '项 目' row")
                return False
        else:
            print("❌ Could not find '项 目' row or separator")
            return False
        
    except Exception as e:
        print(f"❌ Header adjustment failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_optimal_header_structure():
    """Test what the optimal header structure should be"""
    print("\n" + "="*80)
    print("TESTING OPTIMAL HEADER STRUCTURE")
    print("="*80)
    
    # Read the original content without existing processing
    with open('test3\\1、2024年9月财务报表_利润表_A1C25_Sheet1_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove existing metadata and title to test fresh processing
    lines = content.split('\n')
    
    # Find the start of the actual table content
    table_start = -1
    for i, line in enumerate(lines):
        if '编制单位：' in line:
            table_start = i
            break
    
    if table_start == -1:
        print("❌ Could not find table start")
        return False
    
    # Extract just the table content
    table_content = '\n'.join(lines[table_start:])
    
    print("Original table content:")
    table_lines = table_content.split('\n')
    for i, line in enumerate(table_lines[:8]):
        print(f"  Line {i+1}: {line}")
    
    print()
    
    # Test with header adjuster
    adjuster = MarkdownTableHeaderAdjuster()
    
    try:
        result = adjuster.adjust_table(table_content)
        print("✅ Fresh header adjustment successful")
        
        result_lines = result.split('\n')
        print("\nOptimal header structure:")
        for i, line in enumerate(result_lines[:8]):
            print(f"  Line {i+1}: {line}")
        
        # Analyze the structure
        metadata_row = -1
        xiangmu_row = -1
        separator_row = -1
        
        for i, line in enumerate(result_lines[:10]):
            if '编制单位：' in line:
                metadata_row = i
            elif '项 目' in line and '本年累计' in line:
                xiangmu_row = i
            elif '---' in line:
                separator_row = i
                break
        
        print(f"\nStructure analysis:")
        print(f"  Metadata row: {metadata_row}")
        print(f"  '项 目' row: {xiangmu_row}")
        print(f"  Separator row: {separator_row}")
        
        # The optimal structure should be:
        # 1. Metadata row (编制单位)
        # 2. 项 目 row (main header)
        # 3. Separator row
        # 4. Data rows
        
        if metadata_row == 0 and xiangmu_row == 1 and separator_row == 2:
            print("✅ Optimal structure achieved")
            return True
        else:
            print("❌ Structure could be improved")
            return False
        
    except Exception as e:
        print(f"❌ Fresh header adjustment failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main testing function"""
    try:
        test1 = test_header_positioning()
        test2 = test_optimal_header_structure()
        
        print("\n" + "="*80)
        print("HEADER POSITIONING SUMMARY")
        print("="*80)
        print(f"Current positioning correct: {'✅' if test1 else '❌'}")
        print(f"Optimal structure achievable: {'✅' if test2 else '❌'}")
        
        if test1 and test2:
            print("\n✅ Header positioning is working correctly")
            return 0
        else:
            print("\n❌ Header positioning needs improvement")
            return 1
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
