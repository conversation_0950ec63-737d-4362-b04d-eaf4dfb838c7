#!/usr/bin/env python3
"""
Simple validation script for the refactored logging configurations.
"""

import sys
from pathlib import Path

def test_imports():
    """Test that all logging modules can be imported without issues."""
    print("Testing imports...")
    
    try:
        # Test base logging import
        print("  - Importing app.logging_base...")
        import app.logging_base
        print("    ✅ app.logging_base imported successfully")
        
        # Test app logging import
        print("  - Importing app.logging_config...")
        import app.logging_config
        print("    ✅ app.logging_config imported successfully")
        
        # Test crawler logging import
        print("  - Importing crawler.logging_config...")
        import crawler.logging_config
        print("    ✅ crawler.logging_config imported successfully")
        
        return True
    except Exception as e:
        print(f"    ❌ Import failed: {e}")
        return False

def test_basic_functionality():
    """Test basic logging functionality."""
    print("\nTesting basic functionality...")
    
    try:
        from app.logging_config import setup_logging, get_app_component_logger
        from app.logging_base import set_layer_context, set_operation_context
        
        # Setup logging
        print("  - Setting up app logging...")
        setup_logging()
        print("    ✅ App logging setup successful")
        
        # Test app logger
        print("  - Testing app component logger...")
        logger = get_app_component_logger('server', 'test.module')
        set_layer_context('test')
        set_operation_context('validation')
        logger.info("Test log message from validation script")
        print("    ✅ App logging test successful")
        
        # Test crawler logger
        print("  - Testing crawler logger...")
        from crawler.logging_config import get_crawler_logger
        crawler_logger = get_crawler_logger('crawler.test')
        crawler_logger.info("Test log message from crawler validation")
        print("    ✅ Crawler logging test successful")
        
        return True
    except Exception as e:
        print(f"    ❌ Functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main validation function."""
    print("🧪 Validating Refactored Logging Configurations")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    # Test functionality
    functionality_ok = test_basic_functionality()
    
    print("\n" + "=" * 50)
    if imports_ok and functionality_ok:
        print("🎉 All validation tests passed!")
        return 0
    else:
        print("❌ Some validation tests failed!")
        return 1

if __name__ == '__main__':
    sys.exit(main())
