#!/usr/bin/env python3
"""
Test the strict filename filtering in hierarchical table name extraction
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def test_excel_cell_position_filtering():
    """Test Excel cell position filtering with strict regex"""
    print("="*80)
    print("TESTING EXCEL CELL POSITION FILTERING")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # (input_filename, expected_output, description)
        ("季度合并财报（2024年Q4）_A1O23_资产负债表.md", "季度合并财报（2024年Q4）_资产负债表", "Single cell position"),
        ("财务报表_B2C25_Sheet1_利润表.md", "财务报表_利润表", "Cell position + sheet reference"),
        ("数据_AA123BB456_processed.md", "数据", "Complex cell position + suffix"),
        ("2024年Q4财报.md", "2024年Q4财报", "No cell position (preserve Q4)"),
        ("报表_Q1_A1_数据.md", "报表_Q1_数据", "Preserve Q1, remove A1 (not surrounded by underscores)"),
        ("test_A1O23_B2C25_multiple.md", "test_multiple", "Multiple cell positions"),
    ]
    
    print("Excel Cell Position Filtering Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (input_filename, expected, description) in enumerate(test_cases):
        # Test cell position extraction
        cell_positions = reformer.extract_excel_cell_positions(input_filename)
        
        # Test preprocessing
        preprocessed = reformer.preprocess_filename(input_filename)
        
        print(f"Test {i+1}: {description}")
        print(f"  Input: {input_filename}")
        print(f"  Cell positions found: {cell_positions}")
        print(f"  Preprocessed: {preprocessed}")
        print(f"  Expected: {expected}")
        
        passed = preprocessed == expected
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_sheet_reference_filtering():
    """Test sheet reference filtering"""
    print("="*80)
    print("TESTING SHEET REFERENCE FILTERING")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        ("财务报表_Sheet1_资产负债表.md", "财务报表_资产负债表", "Sheet1 reference"),
        ("数据_Sheet2_利润表.md", "数据_利润表", "Sheet2 reference"),
        ("报表_sheet3_现金流.md", "报表_现金流", "Lowercase sheet reference"),
        ("test_Sheet_data.md", "test_data", "Sheet without number"),
        ("多Sheet数据_Sheet1_Sheet2.md", "多Sheet数据", "Multiple sheet references"),
    ]
    
    print("Sheet Reference Filtering Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (input_filename, expected, description) in enumerate(test_cases):
        preprocessed = reformer.preprocess_filename(input_filename)
        
        print(f"Test {i+1}: {description}")
        print(f"  Input: {input_filename}")
        print(f"  Preprocessed: {preprocessed}")
        print(f"  Expected: {expected}")
        
        passed = preprocessed == expected
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_processing_suffix_filtering():
    """Test processing suffix filtering"""
    print("="*80)
    print("TESTING PROCESSING SUFFIX FILTERING")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        ("财务报表_adjusted.md", "财务报表", "Single suffix"),
        ("数据_reformed_processed.md", "数据", "Multiple suffixes"),
        ("报表_adjusted_reformed_output.md", "报表", "Chained suffixes"),
        ("test_processed_adjusted_reformed.md", "test", "Mixed order suffixes"),
        ("资产负债表_adjusted_A1O23_Sheet1.md", "资产负债表", "Suffix + cell + sheet"),
    ]
    
    print("Processing Suffix Filtering Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (input_filename, expected, description) in enumerate(test_cases):
        preprocessed = reformer.preprocess_filename(input_filename)
        
        print(f"Test {i+1}: {description}")
        print(f"  Input: {input_filename}")
        print(f"  Preprocessed: {preprocessed}")
        print(f"  Expected: {expected}")
        
        passed = preprocessed == expected
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_comprehensive_filtering():
    """Test the comprehensive filtering with complex cases"""
    print("="*80)
    print("TESTING COMPREHENSIVE FILTERING")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # The main test case from the requirements
    main_test = "季度合并财报（2024年Q4）_A1O23_Sheet1_adjusted_reformed_资产负债表.md"
    expected_main = "季度合并财报（2024年Q4）_资产负债表"
    
    print("Main Test Case (from requirements):")
    print("-" * 50)
    
    # Test step by step
    print(f"Original: {main_test}")
    
    # Step 1: Remove extensions
    no_ext = main_test.replace('.md', '')
    print(f"No extension: {no_ext}")
    
    # Step 2: Full preprocessing
    preprocessed = reformer.preprocess_filename(main_test)
    print(f"Preprocessed: {preprocessed}")
    
    # Step 3: Extract table name
    table_name = reformer.extract_table_name_from_filename(main_test)
    print(f"Final table name: {table_name}")
    print(f"Expected: {expected_main}")
    
    main_passed = table_name == expected_main
    print(f"Main test result: {'✅ PASS' if main_passed else '❌ FAIL'}")
    
    print()
    
    # Additional complex test cases
    complex_cases = [
        ("财务数据_B2C25_Sheet2_copy_backup_adjusted_reformed_利润表.xlsx", "财务数据_利润表"),
        ("2024年Q3报表_AA123_Sheet1_temp_processed_现金流量表.md", "2024年Q3报表_现金流量表"),
        ("合并报表_A1O23_B2C25_Sheet1_Sheet2_draft_output_资产负债表.csv", "合并报表_资产负债表"),
    ]
    
    print("Additional Complex Cases:")
    print("-" * 50)
    
    all_passed = main_passed
    for i, (input_filename, expected) in enumerate(complex_cases):
        table_name = reformer.extract_table_name_from_filename(input_filename)
        
        print(f"Complex Test {i+1}:")
        print(f"  Input: {input_filename}")
        print(f"  Output: {table_name}")
        print(f"  Expected: {expected}")
        
        passed = table_name == expected
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_preservation_of_meaningful_content():
    """Test that meaningful content is preserved during filtering"""
    print("="*80)
    print("TESTING PRESERVATION OF MEANINGFUL CONTENT")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # These should preserve important date/period indicators
        ("2024年Q4财报.md", "2024年Q4财报", "Preserve Q4 (not Excel cell)"),
        ("一季度合并报表.md", "一季度合并报表", "Preserve Chinese quarter"),
        ("2024年9月资产负债表.md", "2024年9月资产负债表", "Preserve month"),
        ("年度财务报表2023.md", "年度财务报表2023", "Preserve year"),
        ("Q1_Q2_对比分析.md", "Q1_Q2_对比分析", "Preserve multiple quarters"),
    ]
    
    print("Meaningful Content Preservation Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (input_filename, expected, description) in enumerate(test_cases):
        table_name = reformer.extract_table_name_from_filename(input_filename)
        
        print(f"Test {i+1}: {description}")
        print(f"  Input: {input_filename}")
        print(f"  Output: {table_name}")
        print(f"  Expected: {expected}")
        
        passed = table_name == expected
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def main():
    """Run all filtering tests"""
    print("Testing Strict Filename Filtering in Hierarchical Table Name Extraction")
    print("="*80)
    
    try:
        test1 = test_excel_cell_position_filtering()
        test2 = test_sheet_reference_filtering()
        test3 = test_processing_suffix_filtering()
        test4 = test_comprehensive_filtering()
        test5 = test_preservation_of_meaningful_content()
        
        all_passed = test1 and test2 and test3 and test4 and test5
        
        print("="*80)
        print("FINAL RESULTS")
        print("="*80)
        print(f"Excel Cell Position Filtering: {'✅ PASS' if test1 else '❌ FAIL'}")
        print(f"Sheet Reference Filtering: {'✅ PASS' if test2 else '❌ FAIL'}")
        print(f"Processing Suffix Filtering: {'✅ PASS' if test3 else '❌ FAIL'}")
        print(f"Comprehensive Filtering: {'✅ PASS' if test4 else '❌ FAIL'}")
        print(f"Meaningful Content Preservation: {'✅ PASS' if test5 else '❌ FAIL'}")
        
        if all_passed:
            print("\n🎉 All strict filtering tests passed!")
            return 0
        else:
            print("\n❌ Some filtering tests failed")
            return 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
