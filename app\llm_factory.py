from langchain_openai import ChatOpenAI
from langchain_community.llms import Ollama
from typing import Literal, Any
import os

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    ConfigurationException, ExternalServiceException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context
import logging

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for LLM factory operations
set_layer_context("business_logic")

class LLMFactory:
    """统一LLM创建工厂，支持多后端切换"""
    
    BACKEND_TYPES = Literal['OPENAI', 'OLLAMA']
    
    @classmethod
    @log_and_reraise(logger, "LLM creation")
    def create_llm(cls,
                  model_name: str,
                  temperature: float,
                  api_key: str = None,
                  api_base: str = None) -> Any:
        """创建LLM实例 with unified error handling"""
        set_operation_context("llm_creation")

        # Validate input parameters
        with error_boundary("parameter validation", LayerType.BUSINESS_LOGIC):
            if not model_name:
                raise ValidationException(
                    message="Model name is required",
                    field_name="model_name",
                    details="Model name cannot be empty or None",
                    suggested_action="Provide a valid model name"
                )

            if not isinstance(temperature, (int, float)) or temperature < 0 or temperature > 2:
                raise ValidationException(
                    message="Invalid temperature value",
                    field_name="temperature",
                    details=f"Temperature must be a number between 0 and 2, got: {temperature}",
                    context={'temperature': temperature},
                    suggested_action="Provide a temperature value between 0.0 and 2.0"
                )

        # Get backend configuration
        with error_boundary("backend configuration", LayerType.BUSINESS_LOGIC):
            backend = os.getenv('LLM_BACKEND', 'OLLAMA')
            if not backend:
                raise ConfigurationException(
                    message="LLM backend not configured",
                    details="LLM_BACKEND environment variable is not set",
                    suggested_action="Set LLM_BACKEND environment variable to 'OPENAI' or 'OLLAMA'"
                )

        # Create LLM instance based on backend
        with error_boundary("LLM instantiation", LayerType.BUSINESS_LOGIC):
            try:
                if backend.upper() == 'OPENAI':
                    final_api_key = api_key or os.getenv('OPENAI_API_KEY')
                    final_api_base = api_base or os.getenv('OPENAI_API_BASE')

                    if not final_api_key:
                        raise ConfigurationException(
                            message="OpenAI API key not configured",
                            details="OPENAI_API_KEY environment variable is not set and no api_key provided",
                            suggested_action="Set OPENAI_API_KEY environment variable or provide api_key parameter"
                        )

                    return ChatOpenAI(
                        model=model_name,
                        temperature=temperature,
                        openai_api_key=final_api_key,
                        openai_api_base=final_api_base,
                        request_timeout=300,
                        max_retries=3
                    )

                elif backend.upper() == 'OLLAMA':
                    final_api_base = api_base or os.getenv('OLLAMA_API_BASE')

                    return Ollama(
                        model=model_name,
                        temperature=temperature,
                        base_url=final_api_base
                    )

                else:
                    raise ConfigurationException(
                        message="Unsupported LLM backend",
                        details=f"Backend '{backend}' is not supported",
                        context={'backend': backend, 'supported_backends': ['OPENAI', 'OLLAMA']},
                        suggested_action="Set LLM_BACKEND to 'OPENAI' or 'OLLAMA'"
                    )

            except Exception as e:
                if isinstance(e, (ValidationException, ConfigurationException)):
                    raise
                raise ExternalServiceException(
                    message="Failed to create LLM instance",
                    service_name=f"LLM_{backend}",
                    details=f"LLM instantiation error: {str(e)}",
                    original_exception=e,
                    context={
                        'backend': backend,
                        'model_name': model_name,
                        'temperature': temperature
                    },
                    suggested_action="Check LLM service configuration and connectivity"
                )
