#!/usr/bin/env python3
"""
Debug script to isolate the hanging issue in reheader.py
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_step_by_step():
    """Test each step of the algorithm separately"""
    print("=== Step-by-Step Debug ===")
    
    try:
        print("Step 1: Import")
        from app.utils.reheader import MarkdownTableHeaderAdjuster
        print("✅ Import successful")
        
        print("Step 2: Create object")
        adjuster = MarkdownTableHeaderAdjuster()
        print("✅ Object creation successful")
        
        print("Step 3: Test simple content")
        simple_content = '''| 表头1 | 表头2 |
| --- | --- |
| 数据1 | 数据2 |'''
        
        print("Step 4: Parse table")
        rows = adjuster.parse_table(simple_content)
        print(f"✅ Parsed {len(rows)} rows")
        
        print("Step 5: Find separator rows")
        separator_indices = adjuster.find_separator_rows(rows)
        print(f"✅ Found separator indices: {separator_indices}")
        
        if separator_indices:
            sep_idx = separator_indices[0]
            print(f"Step 6: Test analyze_column_types with separator at {sep_idx}")
            
            # This might be where it hangs
            try:
                column_types = adjuster.analyze_column_types(rows, sep_idx)
                print(f"✅ Column types analysis successful: {column_types}")
            except Exception as e:
                print(f"❌ analyze_column_types failed: {e}")
                import traceback
                traceback.print_exc()
                return
        
        print("Step 7: Test position-weighted scoring")
        try:
            keyword_score, keyword_weight, density_weight = adjuster.calculate_position_weighted_keyword_score(rows, 1)
            print(f"✅ Position-weighted scoring successful: {keyword_score}, {keyword_weight}, {density_weight}")
        except Exception as e:
            print(f"❌ Position-weighted scoring failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        print("✅ All steps completed successfully")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_analyze_column_types_directly():
    """Test the analyze_column_types method directly"""
    print("\n=== Testing analyze_column_types Directly ===")
    
    try:
        from app.utils.reheader import MarkdownTableHeaderAdjuster
        adjuster = MarkdownTableHeaderAdjuster()
        
        # Simple test data
        test_rows = [
            ['表头1', '表头2'],
            ['---', '---'],
            ['数据1', '100'],
            ['数据2', '200']
        ]
        
        print("Test rows:", test_rows)
        print("Testing with separator at index 1")
        
        # Test the method that might be hanging
        result = adjuster.analyze_column_types(test_rows, 1)
        print("✅ analyze_column_types result:", result)
        
    except Exception as e:
        print(f"❌ analyze_column_types error: {e}")
        import traceback
        traceback.print_exc()

def test_detect_type_method():
    """Test the detect_type method"""
    print("\n=== Testing detect_type Method ===")
    
    try:
        from app.utils.reheader import MarkdownTableHeaderAdjuster
        adjuster = MarkdownTableHeaderAdjuster()
        
        test_values = ['100', 'text', '', '2024-01-01', '项目']
        
        for value in test_values:
            result = adjuster.detect_type(value)
            print(f"detect_type('{value}') = {result}")
        
        print("✅ detect_type tests completed")
        
    except Exception as e:
        print(f"❌ detect_type error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_detect_type_method()
    test_analyze_column_types_directly()
    test_step_by_step()
    print("\n🎉 Debug tests completed!")
