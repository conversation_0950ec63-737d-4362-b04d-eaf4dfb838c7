import json
import logging
from enum import Enum
from typing import Dict, List

import pandas as pd

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    DataProcessingException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

class GroupType(str, Enum):
    """Enumeration for different grouping types in data processing."""
    COLUMN = "Column"
    YEAR = "Year"
    HALF_YEAR = "HalfYear"
    QUARTER = "Quarter"

@log_and_reraise(logger, "column name extraction")
def extract_column_names_from_expression(expression: str) -> List[str]:
    """Extract column names from a mathematical expression with unified error handling."""
    set_operation_context("column_name_extraction")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if not expression:
            raise ValidationException(
                message="Empty expression provided",
                field_name="expression",
                details="Expression cannot be empty or None",
                suggested_action="Provide a valid mathematical expression"
            )

        if not isinstance(expression, str):
            raise ValidationException(
                message="Expression must be a string",
                field_name="expression",
                details=f"Expected string, got {type(expression)}",
                context={'input_type': type(expression).__name__},
                suggested_action="Provide a valid string expression"
            )

    # Extract column names
    with error_boundary("expression parsing", LayerType.UTILITY):
        try:
            import re

            # Remove spaces for easier parsing
            expr = expression.replace(' ', '')

            # Split by mathematical operators and parentheses
            parts = re.split(r'[+\-*/()]', expr)

            # Filter out empty strings and numeric values
            column_names = []
            for part in parts:
                part = part.strip()
                if part and not part.replace('.', '').replace('-', '').isdigit():
                    column_names.append(part)

            # Remove duplicates while preserving order
            unique_columns = []
            for col in column_names:
                if col not in unique_columns:
                    unique_columns.append(col)

            return unique_columns

        except Exception as e:
            raise DataProcessingException(
                message="Failed to extract column names from expression",
                details=f"Expression parsing error: {str(e)}",
                original_exception=e,
                context={'expression': expression},
                suggested_action="Check expression syntax and format"
            )

@log_and_reraise(logger, "mathematical expression evaluation")
def evaluate_mathematical_expression(expression: str, data_dict: Dict[str, float]) -> float:
    """Safely evaluate a mathematical expression with column values with unified error handling."""
    set_operation_context("mathematical_expression_evaluation")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if not expression:
            raise ValidationException(
                message="Empty expression provided",
                field_name="expression",
                details="Expression cannot be empty or None",
                suggested_action="Provide a valid mathematical expression"
            )

        if not isinstance(expression, str):
            raise ValidationException(
                message="Expression must be a string",
                field_name="expression",
                details=f"Expected string, got {type(expression)}",
                context={'input_type': type(expression).__name__},
                suggested_action="Provide a valid string expression"
            )

        if not isinstance(data_dict, dict):
            raise ValidationException(
                message="Data dictionary must be a dictionary",
                field_name="data_dict",
                details=f"Expected dict, got {type(data_dict)}",
                context={'input_type': type(data_dict).__name__},
                suggested_action="Provide a valid dictionary with column values"
            )

    # Replace column names with values
    with error_boundary("expression substitution", LayerType.UTILITY):
        try:
            import re

            # Replace column names in the expression with their values
            expr_with_values = expression
            for col_name, value in data_dict.items():
                # Use word boundaries to avoid partial replacements
                pattern = r'\b' + re.escape(col_name) + r'\b'
                expr_with_values = re.sub(pattern, str(value), expr_with_values)

        except Exception as e:
            raise DataProcessingException(
                message="Failed to substitute column values in expression",
                details=f"Substitution error: {str(e)}",
                original_exception=e,
                context={'expression': expression, 'data_dict': data_dict},
                suggested_action="Check column names and data dictionary format"
            )

    # Evaluate expression
    with error_boundary("expression evaluation", LayerType.UTILITY):
        try:
            # Safely evaluate the mathematical expression
            result = eval(expr_with_values)
            return float(result)
        except Exception as e:
            logger.error(f"Error evaluating expression '{expr_with_values}': {str(e)}")
            raise DataProcessingException(
                message="Failed to evaluate mathematical expression",
                details=f"Evaluation error: {str(e)}",
                original_exception=e,
                context={'original_expression': expression, 'substituted_expression': expr_with_values},
                suggested_action="Check expression syntax and ensure all variables are defined"
            )
    
@log_and_reraise(logger, "output formatting")
def format_output(df: pd.DataFrame, output_format: str) -> str:
    """Format DataFrame according to specified output format with unified error handling.

    Args:
        df: DataFrame to format
        output_format: One of 'table', 'csv', '-csv', or 'json'

    Returns:
        Formatted string in requested format
    """
    set_operation_context("output_formatting")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if df is None or df.empty:
            raise ValidationException(
                message="Empty or None DataFrame provided",
                field_name="df",
                details="DataFrame cannot be empty or None",
                suggested_action="Provide a valid DataFrame with data"
            )

        valid_formats = ['table', 'csv', '-csv', 'json']
        if output_format not in valid_formats:
            raise ValidationException(
                message="Invalid output format",
                field_name="output_format",
                details=f"Format must be one of {valid_formats}, got: {output_format}",
                context={'provided_format': output_format, 'valid_formats': valid_formats},
                suggested_action="Use one of the supported output formats"
            )

    # Format output
    with error_boundary("data formatting", LayerType.UTILITY):
        try:
            if output_format == 'table':
                pd.set_option('display.float_format', lambda x: '%.2f' % x)
                return df.to_markdown(index=False, floatfmt=".2f")
            elif output_format == 'csv':
                return df.to_csv(index=False)
            elif output_format == '-csv':
                return df.to_csv(index=False, header=False)
            elif output_format == 'json':
                # Check if date column exists
                if '日期' in df.columns:
                    dates = df['日期'].tolist()
                    json_data = []
                    for column in df.columns:
                        if column != '日期':  # Skip date column
                            column_data = {
                                'key': column,
                                'data': [],
                                'date': []
                            }
                            for i in range(len(dates)):
                                # Convert numpy numeric types to Python native types
                                value = df[column].iloc[i]
                                if pd.api.types.is_integer_dtype(df[column]):
                                    column_data['data'].append(int(value))
                                elif pd.api.types.is_float_dtype(df[column]):
                                    column_data['data'].append(float(value))
                                else:
                                    column_data['data'].append(value)
                                column_data['date'].append(dates[i])
                            json_data.append(column_data)
                    return json.dumps(json_data, indent=2, ensure_ascii=False)
                else:
                    # Generic JSON output when no date column
                    return df.to_json(orient='records', indent=2, force_ascii=False)

        except Exception as e:
            raise DataProcessingException(
                message="Failed to format output",
                details=f"Formatting error: {str(e)}",
                original_exception=e,
                context={'output_format': output_format, 'dataframe_shape': df.shape},
                suggested_action="Check DataFrame structure and output format compatibility"
            )
