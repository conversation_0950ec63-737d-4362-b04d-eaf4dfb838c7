#!/bin/bash

# LDAP Test Environment Startup Script

echo "🚀 Starting LDAP Test Environment..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Stop and remove existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose -f docker-compose.ldap.yml down -v

# Start the LDAP services
echo "🐳 Starting OpenLDAP and phpLDAPAdmin containers..."
docker-compose -f docker-compose.ldap.yml up -d

# Wait for LDAP server to be ready
echo "⏳ Waiting for LDAP server to be ready..."
sleep 10

# Check if LDAP server is responding
echo "🔍 Checking LDAP server status..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker exec test-openldap ldapsearch -x -H ldap://localhost -b "dc=company,dc=com" -D "cn=admin,dc=company,dc=com" -w admin123 > /dev/null 2>&1; then
        echo "✅ LDAP server is ready!"
        break
    else
        echo "⏳ Attempt $attempt/$max_attempts - LDAP server not ready yet..."
        sleep 2
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ LDAP server failed to start properly"
    echo "📋 Container logs:"
    docker logs test-openldap
    exit 1
fi

# Display connection information
echo ""
echo "🎉 LDAP Test Environment is ready!"
echo ""
echo "📋 Connection Information:"
echo "  LDAP Server: ldap://localhost:389"
echo "  LDAPS Server: ldaps://localhost:636"
echo "  Base DN: dc=company,dc=com"
echo "  Admin DN: cn=admin,dc=company,dc=com"
echo "  Admin Password: admin123"
echo ""
echo "🔧 Service Admin (for application):"
echo "  Admin DN: cn=ldap-admin,ou=people,dc=company,dc=com"
echo "  Admin Password: ldap-admin123"
echo ""
echo "👥 Test Users:"
echo "  john.doe / password123 (Finance Department)"
echo "  jane.smith / password123 (Risk Department)"
echo "  bob.wilson / password123 (Technology Department)"
echo "  alice.brown / password123 (Admin Department)"
echo "  test.user / password123 (No Department)"
echo ""
echo "🌐 phpLDAPAdmin: http://localhost:8080"
echo "  Login DN: cn=admin,dc=company,dc=com"
echo "  Password: admin123"
echo ""
echo "🧪 To test LDAP authentication:"
echo "  python test_ldap_auth.py"
echo ""
echo "🛑 To stop the environment:"
echo "  docker-compose -f docker-compose.ldap.yml down -v"
