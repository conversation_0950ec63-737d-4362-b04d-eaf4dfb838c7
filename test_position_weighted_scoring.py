#!/usr/bin/env python3
"""
Test script for position-weighted scoring enhancements
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_basic_functionality():
    """Test basic functionality without hanging"""
    print("=== Testing Basic Functionality ===")
    
    try:
        from app.utils.reheader import MarkdownTableHeaderAdjuster
        print("✅ Import successful")
        
        adjuster = MarkdownTableHeaderAdjuster()
        print("✅ Object creation successful")
        
        # Test high-priority keywords
        print(f"High-priority keywords: {adjuster.high_priority_header_keywords}")
        
        # Test simple keyword detection
        test_row = ['项目', '金额']
        print(f"Testing row: {test_row}")
        
        # Manual test without calling the method
        row_text = ' '.join(test_row)
        found_keyword = False
        for keyword in adjuster.high_priority_header_keywords:
            if keyword in row_text:
                print(f"✅ Found keyword: {keyword}")
                found_keyword = True
                break
        
        if not found_keyword:
            print("❌ No keywords found")
        
        print("✅ Basic functionality test completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_position_weights():
    """Test position weight calculation logic"""
    print("\n=== Testing Position Weight Logic ===")
    
    # Test the weight calculation logic manually
    test_cases = [
        (1, "Row 1 - should have keyword_weight=1.2, density_weight=0.8"),
        (3, "Row 3 - should have keyword_weight=1.2, density_weight=0.8"),
        (4, "Row 4 - should have keyword_weight=1.0, density_weight=1.0"),
        (6, "Row 6 - should have keyword_weight=1.0, density_weight=1.0"),
        (7, "Row 7 - should have keyword_weight=0.75, density_weight=1.0"),
        (8, "Row 8 - should have keyword_weight=0.5, density_weight=1.0"),
        (9, "Row 9 - should have keyword_weight=0.25, density_weight=1.0"),
        (10, "Row 10 - should have keyword_weight=0.0, density_weight=1.0"),
        (11, "Row 11 - should have keyword_weight=0.0, density_weight=1.0"),
    ]
    
    for row_number, description in test_cases:
        print(f"\n{description}")
        
        # Manual calculation
        if row_number <= 3:
            keyword_weight = 1.2
            density_weight = 0.8
        elif row_number <= 6:
            keyword_weight = 1.0
            density_weight = 1.0
        elif row_number <= 10:
            weight_factor = (10 - row_number) / 4.0
            keyword_weight = weight_factor
            density_weight = 1.0
        else:
            keyword_weight = 0.0
            density_weight = 1.0
        
        print(f"  Calculated: keyword_weight={keyword_weight:.2f}, density_weight={density_weight:.2f}")

def test_combined_scoring():
    """Test the combined scoring logic"""
    print("\n=== Testing Combined Scoring Logic ===")
    
    # Test the scoring combination manually
    test_cases = [
        (1.0, 1.2, 0.8, 0.9, "High keyword score with high keyword weight"),
        (1.0, 1.0, 1.0, 0.9, "High keyword score with equal weights"),
        (1.0, 0.5, 1.0, 0.9, "High keyword score with low keyword weight"),
        (0.0, 1.2, 0.8, 0.9, "No keyword score"),
    ]
    
    for keyword_score, keyword_weight, density_weight, density_score, description in test_cases:
        print(f"\n{description}")
        print(f"  Inputs: keyword_score={keyword_score}, keyword_weight={keyword_weight}, density_weight={density_weight}, density_score={density_score}")
        
        # Manual calculation
        combined_score = (keyword_score * keyword_weight + density_score * density_weight)
        total_weight = keyword_weight + density_weight
        if total_weight > 0:
            combined_score = combined_score / total_weight
        
        print(f"  Combined score: {combined_score:.4f}")

if __name__ == "__main__":
    test_basic_functionality()
    test_position_weights()
    test_combined_scoring()
    print("\n🎉 All manual tests completed!")
