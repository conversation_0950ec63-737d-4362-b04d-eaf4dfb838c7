import re
from typing import List, Tu<PERSON>, Optional

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    DataProcessingException, ConfigurationException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context
import logging

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

class MarkdownSplitter:
    @log_and_reraise(logger, "MarkdownSplitter initialization")
    def __init__(self, size_limit: int = 10000):
        """Initialize with size limit in characters with unified error handling"""
        set_operation_context("markdown_splitter_init")

        with error_boundary("parameter validation", LayerType.UTILITY):
            if size_limit <= 0:
                raise ValidationException(
                    message="Size limit must be positive",
                    field_name="size_limit",
                    details=f"Size limit must be greater than 0, got: {size_limit}",
                    suggested_action="Provide a positive size limit value"
                )

            self.size_limit = size_limit

        with error_boundary("pattern compilation", LayerType.UTILITY):
            try:
                self.header_pattern = re.compile(r'^([#*]{1,6})\s+(.*)$', re.MULTILINE)
                self.table_header_pattern = re.compile(r'^\|(.+?)\|', re.MULTILINE)
                self.table_title_pattern = re.compile(r'^\|.*?表.*?\|', re.MULTILINE)

            except re.error as e:
                raise ConfigurationException(
                    message="Failed to compile regex patterns",
                    details=f"Regex compilation error: {str(e)}",
                    original_exception=e,
                    suggested_action="Check regex pattern syntax"
                )
        
    @log_and_reraise(logger, "markdown header splitting")
    def split_by_headers(self, content: str) -> List[str]:
        """Split markdown content by headers recursively with unified error handling"""
        set_operation_context("markdown_header_splitting")

        # Validate input
        with error_boundary("input validation", LayerType.UTILITY):
            if content is None:
                raise ValidationException(
                    message="Content cannot be None",
                    field_name="content",
                    details="Content parameter is None",
                    suggested_action="Provide valid markdown content string"
                )

            if not isinstance(content, str):
                raise ValidationException(
                    message="Content must be a string",
                    field_name="content",
                    details=f"Expected string, got {type(content)}",
                    context={'input_type': type(content).__name__},
                    suggested_action="Provide a valid string content"
                )

        # Check size limit
        with error_boundary("size check", LayerType.UTILITY):
            if len(content) <= self.size_limit:
                return [content]

        # Find headers
        with error_boundary("header extraction", LayerType.UTILITY):
            try:
                # Find all header positions
                headers = []
                for match in self.header_pattern.finditer(content):
                    headers.append((match.start(), match.group(0)))

                if not headers:
                    return self.fallback_split(content)

            except Exception as e:
                raise DataProcessingException(
                    message="Failed to extract headers from content",
                    details=f"Header extraction error: {str(e)}",
                    original_exception=e,
                    suggested_action="Check content format and header patterns"
                )

        # Split content by headers
        with error_boundary("content splitting", LayerType.UTILITY):
            try:
                # Split at the highest level header that keeps chunks under size limit
                for i, (pos, header) in enumerate(headers):
                    next_pos = headers[i+1][0] if i+1 < len(headers) else len(content)
                    chunk = content[pos:next_pos]

                    if len(chunk) <= self.size_limit:
                        # Try to split this chunk further
                        sub_chunks = self.split_by_headers(chunk)
                        if len(sub_chunks) > 1:
                            return [content[:pos]] + sub_chunks + [content[next_pos:]]

                # If no good split point found, use fallback
                return self.fallback_split(content)

            except Exception as e:
                if isinstance(e, (ValidationException, DataProcessingException)):
                    raise
                raise DataProcessingException(
                    message="Failed to split content by headers",
                    details=f"Content splitting error: {str(e)}",
                    original_exception=e,
                    suggested_action="Check content structure and size limits"
                )
        
    def fallback_split(self, content: str) -> List[str]:
        """Fallback splitting method when header splitting isn't possible"""
        if self._has_table(content):
            return self._split_table(content)
        return self._split_lines(content)
        
    def _has_table(self, content: str) -> bool:
        """Check if content contains markdown tables"""
        return bool(self.table_header_pattern.search(content))
        
    def _split_table(self, content: str) -> List[str]:
        """Split content with tables while preserving all content and structure"""
        lines = content.split('\n')
        chunks = []
        current_chunk = []
        in_table = False
        table_header = None
        context_before_table = []
        markdown_header = None
        table_title_header = None
        
        for line in lines:
            is_table_row = line.strip().startswith('|')
            is_markdown_header = line.strip().startswith('#')
            is_table_title = (is_table_row and 
                            self.table_title_pattern.search(line) and
                            not table_title_header)
            
            if is_table_row:
                if not in_table:
                    # Start of new table - save context before table
                    in_table = True
                    table_header = line if line.strip() else None
                    # Include all context before table in first chunk
                    current_chunk.extend(context_before_table)
                    context_before_table = []
                    # Include table title header if exists, otherwise markdown header
                    if table_title_header:
                        current_chunk.append(table_title_header)
                        table_title_header = None
                    elif markdown_header:
                        current_chunk.append(markdown_header)
                        markdown_header = None
                    current_chunk.append(line)
                else:
                    current_chunk.append(line)
            else:
                if in_table:
                    # End of table - split if needed
                    if len('\n'.join(current_chunk)) > self.size_limit:
                        # Split the table part while preserving context
                        table_chunks = self._split_table_lines(current_chunk, table_header)
                        chunks.extend(table_chunks[:-1])  # Add all but last chunk
                        current_chunk = table_chunks[-1].split('\n')  # Continue with last chunk
                    else:
                        chunks.append('\n'.join(current_chunk))
                        current_chunk = []
                    in_table = False
                    table_header = None
                
                # Track markdown headers
                if is_table_title:
                    table_title_header = line
                elif is_markdown_header and not markdown_header:
                    markdown_header = line
                else:
                    # Accumulate other context for next table
                    context_before_table.append(line)
                
            # Check if current chunk exceeds size limit
            if len('\n'.join(current_chunk)) >= self.size_limit:
                if in_table and table_header:
                    # Split table while preserving context
                    table_chunks = self._split_table_lines(current_chunk, table_header)
                    chunks.extend(table_chunks[:-1])
                    current_chunk = table_chunks[-1].split('\n')
                else:
                    chunks.append('\n'.join(current_chunk))
                    current_chunk = []
                    
        # Add remaining content
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        if context_before_table:
            chunks.append('\n'.join(context_before_table))
            
        return chunks
        
    def _split_table_lines(self, lines: List[str], header: str) -> List[str]:
        """Split table lines into chunks while preserving structure and context"""
        if not lines:
            return []
            
        chunks = []
        current_chunk = []
        prev_chunk_tail = []
        
        # Find where the actual table starts (after context)
        table_start = 0
        for i, line in enumerate(lines):
            if line.strip().startswith('|'):
                table_start = i
                break
                
        # Add all context before table to first chunk (including markdown headers)
        if table_start > 0:
            current_chunk.extend(lines[:table_start])
            
        # Get the table header row (first line of table)
        table_header = lines[table_start] if table_start < len(lines) else ""
        
        # Process table rows - include header in every chunk
        for line in lines[table_start:]:
            # Start new chunk if needed
            if len('\n'.join(current_chunk + [table_header, line])) > self.size_limit and len(current_chunk) > 0:
                # Calculate overlap lines (non-empty only)
                non_empty_lines = [l for l in '\n'.join(current_chunk).split('\n') if l.strip()]
                overlap = max(1, int(len(non_empty_lines) * 0.1))
                prev_chunk_tail = non_empty_lines[-overlap:]
                
                chunks.append('\n'.join(current_chunk))
                # New chunk starts with same context, table header, and overlap
                current_chunk = []
                if table_start > 0:
                    current_chunk.extend(lines[:table_start])
                current_chunk.append(table_header)
                if prev_chunk_tail:
                    current_chunk.extend(prev_chunk_tail)
                
            if not current_chunk and table_start > 0:
                current_chunk.extend(lines[:table_start])
                current_chunk.append(table_header)
                
            current_chunk.append(line)
            
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
            
        return chunks
            
    def _split_lines(self, content: str) -> List[str]:
        """Split non-table content by lines while preserving paragraphs"""
        paragraphs = content.split('\n\n')
        chunks = []
        current_chunk = []
        prev_chunk_tail = []
        
        for para in paragraphs:
            if len('\n\n'.join(current_chunk + [para])) > self.size_limit:
                if current_chunk:
                    # Calculate overlap lines (non-empty only)
                    non_empty_lines = [line for line in '\n\n'.join(current_chunk).split('\n') if line.strip()]
                    overlap = max(1, int(len(non_empty_lines) * 0.1))
                    prev_chunk_tail = non_empty_lines[-overlap:]
                    
                    chunks.append('\n\n'.join(current_chunk))
                    current_chunk = []
                    # Add overlap from previous chunk
                    if prev_chunk_tail:
                        current_chunk.append('\n'.join(prev_chunk_tail))
            
            current_chunk.append(para)
            
        if current_chunk:
            chunks.append('\n\n'.join(current_chunk))
            
        return chunks

    def merge_chunks(self, chunks: List[str]) -> str:
        """Merge split markdown chunks back into original format"""
        cleaned_chunks = []
        in_table = False
        table_header = None
        last_header = None
        
        for i, chunk in enumerate(chunks):
            lines = chunk.split('\n')
            first_content_line = 0
            
            # Skip empty lines at start
            while first_content_line < len(lines) and not lines[first_content_line].strip():
                first_content_line += 1
                
            if i > 0:
                # Check if we're continuing a table
                is_table_row = (first_content_line < len(lines) and 
                               lines[first_content_line].strip().startswith('|'))
                
                if is_table_row and in_table:
                    # Skip table header if it's duplicated
                    if (table_header and first_content_line < len(lines) and
                        lines[first_content_line].strip() == table_header.strip()):
                        first_content_line += 1
                
                # Comprehensive duplicate checking across all lines
                prev_lines = chunks[i-1].split('\n')
                
                # Skip only exact duplicate headers
                if (first_content_line < len(lines) and 
                    last_header and 
                    lines[first_content_line].strip() == last_header.strip()):
                    first_content_line += 1
                
                # For tables, skip only duplicate headers
                if in_table and table_header:
                    if (first_content_line < len(lines) and
                        lines[first_content_line].strip() == table_header.strip()):
                        first_content_line += 1
                
                # Process all lines while removing exact duplicates
                unique_lines = []
                seen_lines = set(prev_lines)  # Track all lines from previous chunk
                
                for j in range(first_content_line, len(lines)):
                    line = lines[j].strip()
                    if line and line not in seen_lines:  # Skip empty lines and duplicates
                        unique_lines.append(lines[j])
                        seen_lines.add(line)
                
                if unique_lines:
                    cleaned_chunks.append('\n'.join(unique_lines))
            else:
                cleaned_chunks.append(chunk)
            
            # Update state tracking
            if first_content_line < len(lines):
                first_line = lines[first_content_line]
                if first_line.strip().startswith('|'):
                    in_table = True
                    table_header = first_line if '|--' not in first_line else None
                else:
                    in_table = False
                    table_header = None
                    # Track last non-table header
                    if (first_line.strip().startswith('#') or 
                        first_line.strip().startswith('*')):
                        last_header = first_line
        
        # Join chunks with proper spacing and clean up
        merged = '\n'.join(cleaned_chunks)
        
        # Fix table formatting
        merged = re.sub(r'\|\s*\n\s*\|', '|\n|', merged)
        
        # Remove duplicate empty lines
        merged = re.sub(r'\n{3,}', '\n\n', merged)
        
        return merged.strip()

def split_markdown(content: str, size_limit: int = 10000) -> List[str]:
    """Convenience function to split markdown content"""
    splitter = MarkdownSplitter(size_limit)
    return splitter.split_by_headers(content)


def merge_markdown(chunks: List[str]) -> str:
    """Convenience function to merge markdown chunks"""
    splitter = MarkdownSplitter()
    return splitter.merge_chunks(chunks)


def main():
    """Command line interface for markdown splitting"""
    import argparse
    import os
    import logging
    
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    parser = argparse.ArgumentParser(description='Split or merge markdown files')
    subparsers = parser.add_subparsers(dest='command', required=True)
    
    # Split command
    split_parser = subparsers.add_parser('split', help='Split markdown files by headers and tables')
    split_parser.add_argument('input_file', help='Path to input markdown file')
    split_parser.add_argument('-o', '--output_dir', default='.', 
                           help='Output directory for split files (default: current directory)')
    split_parser.add_argument('-s', '--size_limit', type=int, default=10000,
                           help='Maximum size per chunk in characters (default: 10000)')
    
    # Merge command
    merge_parser = subparsers.add_parser('merge', help='Merge split markdown files back together')
    merge_parser.add_argument('input_files', nargs='+', help='Path to input markdown files (use wildcards)')
    merge_parser.add_argument('-o', '--output_file', default='merged.md',
                            help='Output file path (default: merged.md)')
    
    args = parser.parse_args()
    
    if args.command == 'split':
        # Read input file
        with open(args.input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Split content
        chunks = split_markdown(content, args.size_limit)
        
        # Write output files
        base_name = os.path.splitext(os.path.basename(args.input_file))[0]
        os.makedirs(args.output_dir, exist_ok=True)
        
        # Filter out empty/whitespace-only chunks
        non_empty_chunks = [chunk for chunk in chunks if chunk.strip()]
        
        for i, chunk in enumerate(non_empty_chunks, 1):
            output_path = os.path.join(args.output_dir, f'{base_name}_part{i}.md')
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(chunk)
            logger.info(f'Wrote {output_path}')
    
    elif args.command == 'merge':
        import glob
        # Expand file patterns and sort files numerically by part number
        file_paths = []
        for pattern in args.input_files:
            file_paths.extend(glob.glob(pattern))
        
        # Sort files by part number (e.g. file_part1.md, file_part2.md)
        file_paths.sort(key=lambda x: int(re.search(r'_part(\d+)\.md$', x).group(1)) if re.search(r'_part(\d+)\.md$', x) else 0)
        
        # Read all input files
        chunks = []
        for file_path in file_paths:
            with open(file_path, 'r', encoding='utf-8') as f:
                chunks.append(f.read())
        
        # Merge chunks
        merged_content = merge_markdown(chunks)
        
        # Determine original filename from first split file
        first_file = file_paths[0]
        base_name = re.sub(r'_part\d+\.md$', '.md', first_file)
        output_file = os.path.join(os.path.dirname(first_file), os.path.basename(base_name))
        
        # Write output file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(merged_content)
        logger.info(f'Merged {len(file_paths)} files into {output_file}')

if __name__ == '__main__':
    main()
