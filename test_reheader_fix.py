#!/usr/bin/env python3
"""
Test script to verify the header detection algorithm improvements
"""

from app.utils.reheader import MarkdownTableHeaderAdjuster

def test_target_file():
    """Test the specific file that was having issues"""
    print("=== Testing Target File ===")
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Read the test file
    with open('test3/财务报表2024.09_利润表 _A1C34_merged_adjusted.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Original content (lines 4-8):")
    lines = content.split('\n')
    for i, line in enumerate(lines[3:8], 4):
        marker = ' <-- CURRENT SEPARATOR' if '---' in line else (' <-- TARGET HEADER' if '项目' in line else '')
        print(f'{i:2d}: {line}{marker}')
    
    # Test high-priority detection
    rows = adjuster.parse_table(content)
    high_priority_positions = adjuster.find_high_priority_header_positions(rows)
    print(f"\nHigh-priority header positions: {high_priority_positions}")
    
    # Test if row 3 (line 6) contains high-priority keywords
    if len(rows) > 3:
        has_high_priority = adjuster.contains_high_priority_header_keywords(rows[3])
        print(f"Row 3 (line 6) has high-priority keywords: {has_high_priority}")
        print(f"Row 3 content: {rows[3]}")
    
    print("\n✅ Target file analysis completed")

def test_regression():
    """Test that simple tables are not affected"""
    print("\n=== Testing Regression Prevention ===")
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Test simple table without high-priority keywords
    simple_content = """| 名称 | 数量 | 金额 |
| --- | --- | --- |
| 产品A | 100 | 1000 |
| 产品B | 200 | 2000 |"""
    
    print("Testing simple table without high-priority keywords...")
    
    # Parse and check detection
    rows = adjuster.parse_table(simple_content)
    high_priority_positions = adjuster.find_high_priority_header_positions(rows)
    
    print(f"High-priority positions found: {high_priority_positions}")
    
    if not high_priority_positions:
        print("✅ PASS: No high-priority keywords detected in simple table")
    else:
        print("⚠️  WARNING: High-priority keywords detected unexpectedly")
    
    print("\n✅ Regression test completed")

def test_keyword_detection():
    """Test the keyword detection logic"""
    print("\n=== Testing Keyword Detection ===")
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    print(f"High-priority keywords: {adjuster.high_priority_header_keywords}")
    
    test_rows = [
        ['项目', '本月数', '本年累计'],  # Should detect
        ['名称', '数量', '金额'],       # Should not detect
        ['科目', '借方', '贷方'],       # Should detect
        ['序号', '内容', '备注'],       # Should detect
    ]
    
    for i, row in enumerate(test_rows):
        has_high_priority = adjuster.contains_high_priority_header_keywords(row)
        print(f"Row {i+1}: {row} -> High-priority: {has_high_priority}")
    
    print("\n✅ Keyword detection test completed")

if __name__ == "__main__":
    try:
        test_keyword_detection()
        test_target_file()
        test_regression()
        print("\n🎉 All tests completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
