"""
App-Specific Logging Configuration

This module provides logging configuration specifically for the app module components:
- Streamlit UI (app.main, app.*)
- Server/API components (app.api.*, uvicorn)
- CLI components (app.cli.*)
- Business logic (app.agents.*, app.workflows.*)
- Utilities (app.utils.*)

This configuration is independent of crawler logging and focuses only on app needs.
"""

import os
import logging
import logging.config
from pathlib import Path

# Import shared logging infrastructure
from .logging_base import (
    UTF8JsonFormatter, SensitiveDataFilter, RequestIdFilter, ExceptionContextFilter,
    set_layer_context, set_operation_context
)

# App-specific filters and classes

class LayerSpecificFilter(logging.Filter):
    """Filter logs based on application layer."""

    def __init__(self, allowed_layers=None):
        super().__init__()
        self.allowed_layers = allowed_layers or []

    def filter(self, record: logging.LogRecord) -> bool:
        if not self.allowed_layers:
            return True

        layer_context = getattr(record, 'layer_context', None)
        if layer_context:
            return layer_context in self.allowed_layers

        # Check module name for layer classification
        module_name = getattr(record, 'name', '')  # Use 'name' instead of 'module'
        if 'api' in module_name or 'main' in module_name or 'cli' in module_name:
            return 'presentation' in self.allowed_layers
        elif 'agents' in module_name or 'workflows' in module_name:
            return 'business_logic' in self.allowed_layers
        elif 'utils' in module_name:
            return 'utility' in self.allowed_layers

        return True


class AppComponentFilter(logging.Filter):
    """Filter logs based on app components (streamlit, server, cli) - no crawler."""

    def __init__(self, allowed_components=None):
        super().__init__()
        self.allowed_components = allowed_components or []

    def filter(self, record: logging.LogRecord) -> bool:
        if not self.allowed_components:
            return True

        # Check layer context first
        layer_context = getattr(record, 'layer_context', None)
        if layer_context:
            # Map layer contexts to components
            if layer_context == 'presentation' and 'streamlit' in self.allowed_components:
                return True
            elif layer_context in ['server', 'api'] and any(comp in self.allowed_components for comp in ['server', 'api']):
                return True
            elif layer_context == 'cli' and 'cli' in self.allowed_components:
                return True

        # Check module name for component classification
        module_name = getattr(record, 'name', '')
        pathname = getattr(record, 'pathname', '')

        # Streamlit component detection
        if 'streamlit' in self.allowed_components:
            if ('main.py' in pathname and 'app' in pathname) or 'streamlit' in module_name.lower():
                return True

        # Server component detection
        if any(comp in self.allowed_components for comp in ['server', 'api']):
            if 'server.py' in pathname or 'api' in module_name or 'uvicorn' in module_name:
                return True

        # CLI component detection
        if 'cli' in self.allowed_components:
            if 'cli' in module_name or 'cli' in pathname or layer_context == 'cli':
                return True

        return False

def setup_logging():
    """Setup app-specific logging configuration (no crawler dependencies)."""
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_to_file = os.getenv('LOG_TO_FILE', 'true').lower() == 'true'

    # Create logs directory if it doesn't exist
    logs_dir = Path(__file__).resolve().parent.parent / 'logs'
    logs_dir.mkdir(exist_ok=True)

    log_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'filters': {
            'sensitive_data_filter': {
                '()': SensitiveDataFilter,
            },
            'request_id_filter': {
                '()': RequestIdFilter,
            },
            'exception_context_filter': {
                '()': ExceptionContextFilter,
            },
            'streamlit_filter': {
                '()': AppComponentFilter,
                'allowed_components': ['streamlit']
            },
            'server_filter': {
                '()': AppComponentFilter,
                'allowed_components': ['server', 'api']
            },
            'cli_filter': {
                '()': AppComponentFilter,
                'allowed_components': ['cli']
            }
        },
        'formatters': {
            'verbose': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s,%(layer_context)s,%(operation_context)s,%(thread)d,%(funcName)s,%(lineno)d] - %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'json': {
                '()': UTF8JsonFormatter,
                'format': '''
                    asctime: %(asctime)s.%(msecs)03d
                    level: %(levelname)s
                    request_id: %(request_id)s
                    process: %(process)d
                    thread: %(thread)d
                    module: %(module)s
                    funcName: %(funcName)s
                    pathname: %(pathname)s
                    lineno: %(lineno)d
                    message: %(message)s
                ''',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'filters': ['sensitive_data_filter', 'request_id_filter'],
                'formatter': 'verbose',
                'stream': 'ext://sys.stdout',
            },
        },
        'loggers': {
            '': {  # root logger
                'handlers': ['console'],
                'level': log_level,
            },
            # Streamlit UI Application Logger
            'app': {
                'handlers': ['console', 'streamlit_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'app.main': {
                'handlers': ['console', 'streamlit_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            # Server/API Component Logger
            'app.api': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'app.api.server': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'uvicorn': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'INFO',
                'propagate': False,
            },
            'uvicorn.access': {
                'handlers': ['server_file'] if log_to_file else [],
                'level': 'INFO',
                'propagate': False,
            },
            'uvicorn.error': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'WARNING',
                'propagate': False,
            },
            # CLI Component Logger
            'app.cli': {
                'handlers': ['console', 'cli_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            '__main__': {
                'handlers': ['console', 'cli_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            # Business Logic and Utility Loggers (route to server)
            'app.agents': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'app.workflows': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            'app.utils': {
                'handlers': ['console', 'server_file'] if log_to_file else ['console'],
                'level': 'DEBUG',
                'propagate': False,
            },
            # External libraries
            'httpx': {
                'level': 'WARNING',
                'propagate': True,
            }
        }
    }

    # Add file handlers if enabled - 3 dedicated log files for app components
    if log_to_file:
        # 1. Streamlit UI Application logs
        log_config['handlers']['streamlit_file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': logs_dir / 'streamlit.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'encoding': 'utf-8',
            'filters': ['sensitive_data_filter', 'request_id_filter', 'exception_context_filter', 'streamlit_filter'],
            'formatter': 'json',
        }

        # 2. Server/API Component logs
        log_config['handlers']['server_file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': logs_dir / 'server.log',
            'maxBytes': 100 * 1024 * 1024,  # 100MB
            'backupCount': 5,
            'encoding': 'utf-8',
            'filters': ['sensitive_data_filter', 'request_id_filter', 'exception_context_filter', 'server_filter'],
            'formatter': 'json',
        }

        # 3. CLI Component logs
        log_config['handlers']['cli_file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': logs_dir / 'cli.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'encoding': 'utf-8',
            'filters': ['sensitive_data_filter', 'request_id_filter', 'exception_context_filter', 'cli_filter'],
            'formatter': 'json',
        }

    logging.config.dictConfig(log_config)
    
    # Set more granular log levels
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    logging.getLogger('openai').setLevel(logging.INFO)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    # Add trace logging for debugging
    logging.getLogger('app.workflows').setLevel('DEBUG')
    logging.getLogger('app.api').setLevel('DEBUG')


# App-specific utility functions for logger management

def get_layer_logger(layer: str, module_name: str = None):
    """Get a logger configured for a specific app layer."""
    if module_name:
        logger_name = f"app.{module_name}"
    else:
        if layer == 'presentation':
            logger_name = 'app.api'
        elif layer == 'business_logic':
            logger_name = 'app.workflows'
        elif layer == 'utility':
            logger_name = 'app.utils'
        else:
            logger_name = 'app'

    logger = logging.getLogger(logger_name)
    set_layer_context(layer)
    return logger


def get_app_component_logger(component: str, module_name: str = None):
    """Get a logger configured for a specific app component (streamlit, server, cli)."""
    if module_name:
        logger_name = module_name
    else:
        if component == 'streamlit':
            logger_name = 'app'
        elif component == 'server':
            logger_name = 'app.api'
        elif component == 'cli':
            logger_name = 'app.cli'
        else:
            logger_name = 'app'

    logger = logging.getLogger(logger_name)
    # Set appropriate layer context based on component
    if component == 'streamlit':
        set_layer_context('presentation')
    elif component == 'server':
        set_layer_context('server')
    elif component == 'cli':
        set_layer_context('cli')

    return logger


if __name__ == '__main__':
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Logging setup complete.")
    logger.debug("This is a debug message.")
    logger.warning("This is a warning.")
    logger.error("This is an error with sensitive data: api_key=12345")
    logger.info("Another message.")
