# Torch.Classes RuntimeError Fix for Streamlit

## Problem Description

When running the Streamlit application, you may encounter a RuntimeError related to `torch.classes.__path__._path` and event loop issues. This happens because:

1. **Streamlit's File Watcher**: Streamlit's file watcher tries to examine all imported modules, including PyTorch
2. **PyTorch Module Structure**: The `torch.classes.__path__._path` attribute doesn't exist or has access issues
3. **Import Order**: PyTorch-dependent modules are imported before Streamlit configuration is set
4. **Event Loop Conflicts**: Asyncio event loop issues when Stream<PERSON> tries to examine module paths

## Error Symptoms

```
RuntimeError: no running event loop in streamlit/web/bootstrap.py
RuntimeError in streamlit/watcher/local_sources_watcher.py when trying to instantiate class '__path__._path' that doesn't exist in torch
```

## Solution Overview

The fix involves three main strategies:

### 1. Early Streamlit Configuration
Configure Streamlit **before** importing any PyTorch-dependent modules:

```python
# Configure Streamlit FIRST - before any torch imports
import streamlit as st
from streamlit import config as st_config

# Disable file watcher to prevent torch.classes examination
st_config.set_option('server.fileWatcherType', 'none')
st_config.set_option('server.runOnSave', False)
st_config.set_option('runner.magicEnabled', False)
st_config.set_option('global.developmentMode', False)
```

### 2. Lazy Module Imports
Import PyTorch-dependent modules only when needed:

```python
# Global variables for lazy imports
ReportAgentClient = None
RAGWorkflow = None
CrawlerClient = None

def lazy_import_modules():
    """Import torch-dependent modules only when needed"""
    global ReportAgentClient, RAGWorkflow, CrawlerClient
    
    if ReportAgentClient is None:
        from app.api.client import ReportAgentClient
        from app.workflows.rag_workflow import RAGWorkflow
        from crawler.client import CrawlerClient
```

### 3. Environment Variables
Set environment variables to prevent torch path inspection:

```python
os.environ['TORCH_LOGS'] = 'off'
os.environ['TORCH_SHOW_CPP_STACKTRACES'] = '0'
os.environ['STREAMLIT_SERVER_FILE_WATCHER_TYPE'] = 'none'
```

## Files Modified

### 1. `app/main.py`
- **Early Streamlit Configuration**: Moved Streamlit config before any torch imports
- **Lazy Imports**: Implemented lazy loading for torch-dependent modules
- **Function Updates**: Added `lazy_import_modules()` calls to all functions using these modules

### 2. `streamlit_config.py` (New)
- **Centralized Configuration**: Provides early Streamlit configuration
- **Environment Setup**: Sets torch-related environment variables
- **Reusable**: Can be imported by other modules

### 3. `run_streamlit.py` (New)
- **Launcher Script**: Properly configures environment before starting Streamlit
- **Command Line Options**: Supports custom port and host settings
- **Dependency Checking**: Verifies required packages are installed

### 4. `run_app.bat` (New)
- **Windows Batch File**: Easy launcher for Windows users
- **Environment Setup**: Sets all necessary environment variables
- **Error Handling**: Provides clear error messages

## Usage Instructions

### Method 1: Use the Launcher Script (Recommended)

```bash
# Basic usage
python run_streamlit.py

# Custom port
python run_streamlit.py --port 8502

# Skip dependency check
python run_streamlit.py --skip-deps-check
```

### Method 2: Use the Batch File (Windows)

```cmd
# Double-click or run from command line
run_app.bat
```

### Method 3: Manual Streamlit Command

```bash
# Set environment variables first
export STREAMLIT_SERVER_FILE_WATCHER_TYPE=none
export TORCH_LOGS=off

# Run with configuration
streamlit run app/main.py \
    --server.fileWatcherType none \
    --server.runOnSave false \
    --runner.magicEnabled false \
    --global.developmentMode false
```

## Technical Details

### Import Order Matters
The fix ensures this import order:

1. **Basic Python modules** (os, sys, pathlib, etc.)
2. **Environment variables** (dotenv, torch environment setup)
3. **Streamlit configuration** (BEFORE any torch imports)
4. **Logging and error handling** (after Streamlit config)
5. **Torch-dependent modules** (lazy imported when needed)

### Key Configuration Options

| Option | Value | Purpose |
|--------|-------|---------|
| `server.fileWatcherType` | `none` | Disable file watching completely |
| `server.runOnSave` | `false` | Prevent auto-reload on file changes |
| `runner.magicEnabled` | `false` | Disable magic command detection |
| `global.developmentMode` | `false` | Disable development features |
| `TORCH_LOGS` | `off` | Disable torch logging |

### Lazy Import Pattern

```python
def function_using_torch_modules():
    # Always call this first
    lazy_import_modules()
    
    # Now safe to use the modules
    client = CrawlerClient()
    workflow = RAGWorkflow(...)
```

## Troubleshooting

### If you still get torch.classes errors:

1. **Check import order**: Ensure no torch imports happen before Streamlit config
2. **Clear Python cache**: Delete `__pycache__` directories
3. **Restart completely**: Kill all Python processes and restart
4. **Check environment**: Verify environment variables are set

### If lazy imports fail:

1. **Check module paths**: Ensure all modules are in Python path
2. **Install dependencies**: Run `pip install -r requirements.txt`
3. **Check for circular imports**: Look for import cycles in your modules

### If Streamlit still watches files:

1. **Use launcher script**: The `run_streamlit.py` script sets everything correctly
2. **Check config**: Verify Streamlit config is applied before imports
3. **Environment variables**: Ensure `STREAMLIT_SERVER_FILE_WATCHER_TYPE=none`

## Verification

To verify the fix is working:

1. **No RuntimeError**: Application starts without torch.classes errors
2. **No file watching**: Changes to files don't trigger auto-reload
3. **Modules load**: All functionality works when actually used
4. **Clean logs**: No torch-related warnings in console

## Performance Impact

- **Startup**: Slightly faster due to lazy imports
- **Memory**: Lower initial memory usage
- **Runtime**: No performance impact once modules are loaded
- **Development**: File watching disabled (use manual refresh)

This fix resolves the torch.classes RuntimeError while maintaining all application functionality.
