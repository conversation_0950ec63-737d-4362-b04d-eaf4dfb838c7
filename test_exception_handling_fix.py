#!/usr/bin/env python3
"""
Test script to verify the exception handling fixes for KeyError: 'suggested_action' issue.

This test specifically targets the SQLite "no such column: 日期" error scenario
and ensures that the exception handling pipeline works correctly without
causing secondary exceptions.
"""

import sys
import os
import tempfile
import sqlite3
import pandas as pd
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.exceptions import DataProcessingException, CLIException, BaseAppException
from app.utils.data_tools import query_data_from_sqlite, load_to_sqlite
from app.logging_config import setup_logging

# Setup logging
setup_logging()

def test_dataprocessing_exception_constructor():
    """Test that DataProcessingException constructor handles different argument patterns"""
    print("🧪 Testing DataProcessingException constructor flexibility...")
    
    # Test 1: Original keyword argument pattern (should work)
    try:
        exc1 = DataProcessingException(
            message="Test error",
            data_type="test_data",
            context={"key": "value"},
            suggested_action="Test action"
        )
        print("   ✅ Keyword argument pattern works")
        assert exc1.message == "Test error"
        assert exc1.context["data_type"] == "test_data"
        assert exc1.context["key"] == "value"
        assert exc1.suggested_action == "Test action"
    except AssertionError as e:
        print(f"   ❌ Keyword argument pattern assertion failed: {e}")
        print(f"      exc1.context: {exc1.context}")
        print(f"      exc1.suggested_action: {exc1.suggested_action}")
        return False
    except Exception as e:
        print(f"   ❌ Keyword argument pattern failed: {e}")
        return False
    
    # Test 2: Positional argument pattern (the problematic one we fixed)
    try:
        exc2 = DataProcessingException(
            "Test error with positional args",
            {"table": "test_table", "error": "test error"},
            "Check the test parameters"
        )
        print("   ✅ Positional argument pattern works")
        assert exc2.message == "Test error with positional args"
        assert exc2.context["table"] == "test_table"
        assert exc2.context["error"] == "test error"
        assert exc2.suggested_action == "Check the test parameters"
    except Exception as e:
        print(f"   ❌ Positional argument pattern failed: {e}")
        return False
    
    # Test 3: Mixed pattern
    try:
        exc3 = DataProcessingException(
            "Test error mixed",
            {"context_key": "context_value"},
            "Mixed action",
            details="Additional details"
        )
        print("   ✅ Mixed argument pattern works")
        assert exc3.message == "Test error mixed"
        assert exc3.context["context_key"] == "context_value"
        assert exc3.suggested_action == "Mixed action"
        assert exc3.details == "Additional details"
    except Exception as e:
        print(f"   ❌ Mixed argument pattern failed: {e}")
        return False
    
    return True

def test_cli_exception_instantiation():
    """Test that CLIException can be instantiated without KeyError"""
    print("\n🧪 Testing CLIException instantiation...")
    
    try:
        # This should not raise KeyError: 'suggested_action'
        cli_exc = CLIException("Test CLI error")
        print("   ✅ CLIException instantiated successfully")
        assert cli_exc.message == "Test CLI error"
        assert cli_exc.error_code == "CLI_001"
        assert cli_exc.suggested_action == "Please check command syntax and arguments"
        return True
    except KeyError as e:
        print(f"   ❌ CLIException raised KeyError: {e}")
        return False
    except Exception as e:
        print(f"   ❌ CLIException raised unexpected error: {e}")
        return False

def test_sqlite_column_error_handling():
    """Test that SQLite 'no such column' errors are properly handled"""
    print("\n🧪 Testing SQLite column error handling...")

    # Create a temporary database with a table that doesn't have '日期' column
    temp_dir = tempfile.mkdtemp()
    original_cwd = os.getcwd()

    try:
        # Change to temp directory to avoid affecting real data
        os.chdir(temp_dir)

        # Create data directory and database
        os.makedirs('data', exist_ok=True)
        conn = sqlite3.connect('data/report.db')
        cursor = conn.cursor()

        # Create a table without the expected '日期' column
        cursor.execute("""
            CREATE TABLE test_table (
                id INTEGER,
                name TEXT,
                value REAL
            )
        """)

        # Insert some test data
        cursor.execute("INSERT INTO test_table VALUES (1, 'test1', 100.0)")
        cursor.execute("INSERT INTO test_table VALUES (2, 'test2', 200.0)")
        conn.commit()
        conn.close()

        # Now try to query with grouping that requires '日期' column
        # This should trigger the SQLite error and test our exception handling
        try:
            df = query_data_from_sqlite(
                table='test_table',
                group_by='month'  # This will try to access '日期' column
            )
            print("   ❌ Expected SQLite error but query succeeded")
            return False

        except (DataProcessingException, BaseAppException) as e:
            # The error boundary decorator might wrap it in UtilityException
            print(f"   ✅ SQLite error properly wrapped in {type(e).__name__}")
            print(f"      Error code: {e.error_code}")
            print(f"      Message: {e.message}")
            if hasattr(e, 'suggested_action') and e.suggested_action:
                print(f"      Suggested action: {e.suggested_action}")

            # Verify exception chaining
            if hasattr(e, 'original_exception') and e.original_exception:
                print(f"      Original exception: {type(e.original_exception).__name__}")
                print("   ✅ Exception chaining preserved")
            else:
                print("   ⚠️ Exception chaining missing (may be wrapped by error boundary)")

            # Verify context information
            if hasattr(e, 'context') and e.context and ('error' in e.context or 'table' in e.context):
                print("   ✅ Error context preserved")
            else:
                print("   ⚠️ Error context missing")

            # Most importantly, verify we didn't get a KeyError: 'suggested_action'
            print("   ✅ No KeyError: 'suggested_action' occurred")
            return True

        except Exception as e:
            print(f"   ❌ Unexpected exception type: {type(e).__name__}: {e}")
            return False

    finally:
        os.chdir(original_cwd)
        # Clean up manually to avoid Windows file locking issues
        try:
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass  # Ignore cleanup errors

def test_exception_logging():
    """Test that exceptions can be logged without errors"""
    print("\n🧪 Testing exception logging...")
    
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        # Create a DataProcessingException with the problematic pattern
        exc = DataProcessingException(
            "Test logging error",
            {"table": "test_table", "operation": "test_op"},
            "Test suggested action"
        )
        
        # Try to log it - this should not raise any errors
        exc.log_error(logger)
        print("   ✅ Exception logged successfully")
        
        # Test logging context
        context = exc.get_full_context()
        assert 'error_code' in context
        assert 'suggested_action' in context
        assert context['suggested_action'] == "Test suggested action"
        print("   ✅ Exception context retrieved successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Exception logging failed: {e}")
        return False

def main():
    """Run all exception handling tests"""
    print("🚀 Testing Exception Handling Fixes")
    print("=" * 50)
    
    tests = [
        ("DataProcessingException Constructor", test_dataprocessing_exception_constructor),
        ("CLIException Instantiation", test_cli_exception_instantiation),
        ("SQLite Column Error Handling", test_sqlite_column_error_handling),
        ("Exception Logging", test_exception_logging),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All exception handling fixes working correctly!")
        print("✅ DataProcessingException constructor handles multiple argument patterns")
        print("✅ CLIException instantiation works without KeyError")
        print("✅ SQLite errors properly wrapped and chained")
        print("✅ Exception logging works correctly")
        return 0
    else:
        print("\n❌ Some exception handling fixes need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())
