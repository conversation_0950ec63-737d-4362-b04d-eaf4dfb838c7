from abc import ABC, abstractmethod
from typing import Optional
import logging
import os
from pathlib import Path

from app.config import AgentConfig


class BaseAgent(ABC):
    """Agent抽象基类，定义统一接口"""

    def __init__(self, config: AgentConfig):
        self.config = config

    @abstractmethod
    def process_message(self, message: str,
                        current_company: Optional[str] = None,
                        current_year: Optional[str] = None,
                        current_quarter: Optional[str] = None,
                        current_template: Optional[str] = None) -> dict:
        """处理消息抽象方法"""
        pass
