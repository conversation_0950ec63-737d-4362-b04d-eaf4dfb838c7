"""
Test Suite for Crawler Unified Error Handling

This test suite validates the unified error handling implementation
across all crawler components, ensuring proper exception handling,
logging, and error recovery mechanisms.
"""

import pytest
import asyncio
import httpx
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path
import json
import tempfile
import os

# Import crawler modules
from crawler.core import Qcc<PERSON>rawler
from crawler.server import <PERSON><PERSON>erManager, CrawlerManager
from crawler.client import CrawlerClient
from crawler.session_manager import <PERSON>Manager
from crawler.utilities import sanitize_filename, ensure_output_dir

# Import exception classes
from crawler.exceptions import (
    BrowserException, PageLoadException, ElementNotFoundException,
    DataExtractionException, SearchException, LoginException,
    AccountRestrictedException, CrawlerSessionException,
    NetworkException, TimeoutException, UserInputException,
    CrawlerUIException
)

# Import error handling framework
from app.exceptions import BaseAppException
from app.error_handling import error_boundary


class TestCrawlerExceptions:
    """Test crawler-specific exception classes"""
    
    def test_browser_exception_creation(self):
        """Test BrowserException with proper context"""
        exc = BrowserException(
            message="Browser failed to launch",
            browser_action="launch",
            details="Chrome process crashed",
            context={'headless': True, 'args': ['--no-sandbox']}
        )
        
        assert exc.message == "Browser failed to launch"
        assert exc.error_code == "CRAWLER_BROWSER_001"
        assert exc.context['browser_action'] == "launch"
        assert exc.context['headless'] is True
    
    def test_page_load_exception_with_url(self):
        """Test PageLoadException with URL context"""
        url = "https://example.com"
        exc = PageLoadException(
            message="Page failed to load",
            url=url,
            timeout=30
        )
        
        assert exc.message == "Page failed to load"
        assert exc.context['url'] == url
        assert exc.context['timeout'] == 30
    
    def test_element_not_found_exception(self):
        """Test ElementNotFoundException with selector context"""
        selector = "#search-input"
        exc = ElementNotFoundException(
            message="Search input not found",
            selector=selector,
            url="https://example.com"
        )
        
        assert exc.context['selector'] == selector
        assert exc.context['url'] == "https://example.com"
    
    def test_login_exception_with_status(self):
        """Test LoginException with login status"""
        exc = LoginException(
            message="Login failed",
            login_status="invalid_credentials"
        )
        
        assert exc.context['login_status'] == "invalid_credentials"
    
    def test_account_restricted_exception(self):
        """Test AccountRestrictedException with restriction type"""
        exc = AccountRestrictedException(
            restriction_type="rate_limited"
        )
        
        assert exc.context['restriction_type'] == "rate_limited"
        assert exc.severity.name == "CRITICAL"


class TestUtilitiesErrorHandling:
    """Test error handling in utility functions"""
    
    def test_sanitize_filename_invalid_input(self):
        """Test sanitize_filename with invalid input types"""
        with pytest.raises(Exception) as exc_info:
            sanitize_filename(None)
        
        # Should be converted to CrawlerException
        assert "Invalid filename input" in str(exc_info.value)
    
    def test_sanitize_filename_empty_string(self):
        """Test sanitize_filename with empty string"""
        with pytest.raises(Exception) as exc_info:
            sanitize_filename("")
        
        assert "Empty filename provided" in str(exc_info.value)
    
    def test_sanitize_filename_valid_input(self):
        """Test sanitize_filename with valid input"""
        result = sanitize_filename("test<>file|name?.txt")
        assert result == "test__file_name_.txt"
    
    def test_ensure_output_dir_invalid_input(self):
        """Test ensure_output_dir with invalid input"""
        with pytest.raises(Exception) as exc_info:
            ensure_output_dir(None)
        
        assert "Invalid output directory input" in str(exc_info.value)
    
    def test_ensure_output_dir_permission_error(self):
        """Test ensure_output_dir with permission error"""
        with patch('pathlib.Path.mkdir', side_effect=PermissionError("Access denied")):
            with pytest.raises(Exception) as exc_info:
                ensure_output_dir("/root/test")
            
            assert "Failed to create output directory" in str(exc_info.value)


class TestSessionManagerErrorHandling:
    """Test error handling in SessionManager"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.session_file = os.path.join(self.temp_dir, "test_session.json")
        self.session_manager = SessionManager(self.session_file)
    
    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_save_session_browser_error(self):
        """Test save_session with browser context error"""
        mock_context = AsyncMock()
        mock_context.cookies.side_effect = Exception("Browser context invalid")
        
        with pytest.raises(BrowserException) as exc_info:
            await self.session_manager.save_session(mock_context)
        
        assert "Failed to extract cookies" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_save_session_file_error(self):
        """Test save_session with file write error"""
        mock_context = AsyncMock()
        mock_context.cookies.return_value = [{"name": "test", "value": "value"}]
        
        # Make directory read-only to cause write error
        os.chmod(self.temp_dir, 0o444)
        
        try:
            with pytest.raises(Exception) as exc_info:
                await self.session_manager.save_session(mock_context)
            
            assert "Failed to save session file" in str(exc_info.value)
        finally:
            # Restore permissions for cleanup
            os.chmod(self.temp_dir, 0o755)
    
    @pytest.mark.asyncio
    async def test_load_session_invalid_json(self):
        """Test load_session with invalid JSON file"""
        # Create invalid JSON file
        with open(self.session_file, 'w') as f:
            f.write("invalid json content")
        
        mock_context = AsyncMock()
        
        with pytest.raises(Exception) as exc_info:
            await self.session_manager.load_session(mock_context)
        
        assert "Invalid session file format" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_load_session_context_error(self):
        """Test load_session with context error"""
        # Create valid JSON file
        with open(self.session_file, 'w') as f:
            json.dump([{"name": "test", "value": "value"}], f)
        
        mock_context = AsyncMock()
        mock_context.add_cookies.side_effect = Exception("Context error")
        
        with pytest.raises(BrowserException) as exc_info:
            await self.session_manager.load_session(mock_context)
        
        assert "Failed to load cookies" in str(exc_info.value)


class TestCrawlerClientErrorHandling:
    """Test error handling in CrawlerClient"""
    
    def setup_method(self):
        """Setup test environment"""
        self.client = CrawlerClient("http://test-server:8000")
    
    @pytest.mark.asyncio
    async def test_get_status_timeout(self):
        """Test get_status with timeout error"""
        with patch.object(self.client.client, 'get', side_effect=httpx.TimeoutException("Request timeout")):
            with pytest.raises(Exception) as exc_info:
                await self.client.get_status()
            
            assert "Status check timeout" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_status_connection_error(self):
        """Test get_status with connection error"""
        with patch.object(self.client.client, 'get', side_effect=httpx.ConnectError("Connection failed")):
            with pytest.raises(NetworkException) as exc_info:
                await self.client.get_status()
            
            assert "Connection failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_login_http_error(self):
        """Test login with HTTP error response"""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal server error"
        
        http_error = httpx.HTTPStatusError("Server error", request=Mock(), response=mock_response)
        
        with patch.object(self.client.client, 'post', side_effect=http_error):
            with pytest.raises(Exception) as exc_info:
                await self.client.login()
            
            assert "Login request failed" in str(exc_info.value)


class TestBrowserManagerErrorHandling:
    """Test error handling in BrowserManager"""
    
    def setup_method(self):
        """Setup test environment"""
        self.browser_manager = BrowserManager()
    
    @pytest.mark.asyncio
    async def test_initialize_playwright_error(self):
        """Test browser initialization with Playwright error"""
        with patch('playwright.async_api.async_playwright') as mock_playwright:
            mock_playwright.return_value.start.side_effect = Exception("Playwright failed")
            
            with pytest.raises(BrowserException) as exc_info:
                await self.browser_manager._initialize()
            
            assert "Failed to start Playwright" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_initialize_browser_launch_error(self):
        """Test browser launch error"""
        with patch('playwright.async_api.async_playwright') as mock_playwright:
            mock_playwright_instance = AsyncMock()
            mock_playwright.return_value.start.return_value = mock_playwright_instance
            mock_playwright_instance.chromium.launch.side_effect = Exception("Browser launch failed")
            
            with pytest.raises(BrowserException) as exc_info:
                await self.browser_manager._initialize()
            
            assert "Failed to launch browser" in str(exc_info.value)


class TestErrorBoundaryIntegration:
    """Test error boundary integration in crawler operations"""
    
    @pytest.mark.asyncio
    async def test_error_boundary_with_crawler_exception(self):
        """Test error boundary with crawler-specific exception"""
        async def failing_operation():
            raise ElementNotFoundException(
                message="Element not found",
                selector="#test",
                url="https://example.com"
            )
        
        with pytest.raises(ElementNotFoundException) as exc_info:
            with error_boundary("test operation", "core"):
                await failing_operation()
        
        # Exception should pass through unchanged
        assert exc_info.value.message == "Element not found"
        assert exc_info.value.context['selector'] == "#test"
    
    @pytest.mark.asyncio
    async def test_error_boundary_with_generic_exception(self):
        """Test error boundary with generic exception"""
        async def failing_operation():
            raise ValueError("Generic error")
        
        # Error boundary should let the exception pass through
        # The calling code should handle conversion if needed
        with pytest.raises(ValueError):
            with error_boundary("test operation", "core"):
                await failing_operation()





class TestErrorLoggingIntegration:
    """Test integration with enhanced logging system"""
    
    def test_exception_logging(self):
        """Test that exceptions are properly logged"""
        exc = BrowserException(
            message="Test browser error",
            browser_action="test_action",
            details="Test details"
        )
        
        with patch('crawler.logging_config.core_logger') as mock_logger:
            exc.log_error(mock_logger)
            
            # Verify logging was called
            mock_logger.error.assert_called_once()
            call_args = mock_logger.error.call_args[0][0]
            assert "Test browser error" in call_args


class TestCrawlerIntegrationWithErrorHandling:
    """Integration tests to ensure crawler functionality works with improved error handling"""

    @pytest.mark.asyncio
    async def test_qcc_crawler_initialization(self):
        """Test QccCrawler initialization with error handling"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test successful initialization
            crawler = QccCrawler(
                output_dir=temp_dir,
                keyword="test",
                session_file_path=os.path.join(temp_dir, "session.json")
            )

            assert crawler.output_dir == temp_dir
            assert crawler.keyword == "test"

    @pytest.mark.asyncio
    async def test_qcc_crawler_initialization_error(self):
        """Test QccCrawler initialization with invalid parameters"""
        # Test with invalid output directory (should handle gracefully)
        with patch('crawler.utilities.ensure_output_dir', side_effect=PermissionError("Access denied")):
            with pytest.raises(BrowserException) as exc_info:
                QccCrawler(output_dir="/invalid/path")

            assert "Failed to initialize crawler" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_read_method_with_mock_page(self):
        """Test QccCrawler.read method with mocked page"""
        crawler = QccCrawler()

        # Mock page object
        mock_page = AsyncMock()
        mock_page.goto = AsyncMock()
        mock_page.wait_for_load_state = AsyncMock()
        mock_page.query_selector_all = AsyncMock(return_value=[])

        # Mock session manager
        crawler.session = AsyncMock()
        crawler.session.safe_fill = AsyncMock(return_value=True)

        # Mock unified_login_check to return logged in status
        crawler.unified_login_check = AsyncMock(return_value={'login': True})

        # Test should raise ElementNotFoundException due to empty suggestions
        with pytest.raises(ElementNotFoundException) as exc_info:
            await crawler.read("https://example.com", "test", mock_page)

        assert "Unable to locate dropdown suggestion elements" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_unified_login_check_with_mock_page(self):
        """Test unified_login_check with mocked page"""
        crawler = QccCrawler()

        # Mock page object
        mock_page = AsyncMock()
        mock_page.goto = AsyncMock()
        mock_page.screenshot = AsyncMock(return_value=b"fake_screenshot")
        mock_page.evaluate = AsyncMock()
        mock_page.wait_for_timeout = AsyncMock()
        mock_page.is_visible = AsyncMock(return_value=False)
        mock_page.is_enabled = AsyncMock(return_value=True)

        # Mock _check_account_limits
        crawler._check_account_limits = AsyncMock(return_value=None)

        result = await crawler.unified_login_check("https://example.com", mock_page)

        # Should return login status
        assert isinstance(result, dict)
        assert 'login' in result
        assert 'status' in result


class TestErrorHandlingPerformance:
    """Test performance impact of error handling"""

    def test_exception_creation_performance(self):
        """Test that exception creation doesn't significantly impact performance"""
        import time

        start_time = time.time()

        # Create many exceptions
        for i in range(1000):
            exc = BrowserException(
                message=f"Test error {i}",
                browser_action="performance_test",
                details=f"Performance test iteration {i}",
                context={'iteration': i}
            )

        end_time = time.time()
        duration = end_time - start_time

        # Should complete in reasonable time (less than 1 second)
        assert duration < 1.0, f"Exception creation took too long: {duration:.3f}s"

    def test_error_boundary_performance(self):
        """Test error boundary performance overhead"""
        import time

        def test_operation():
            return "success"

        # Test without error boundary
        start_time = time.time()
        for i in range(1000):
            result = test_operation()
        no_boundary_time = time.time() - start_time

        # Test with error boundary
        start_time = time.time()
        for i in range(1000):
            with error_boundary("performance test", "core"):
                result = test_operation()
        with_boundary_time = time.time() - start_time

        # Overhead should be minimal (less than 50% increase)
        overhead_ratio = with_boundary_time / no_boundary_time
        assert overhead_ratio < 1.5, f"Error boundary overhead too high: {overhead_ratio:.2f}x"


class TestErrorHandlingEdgeCases:
    """Test edge cases and corner scenarios"""

    def test_nested_error_boundaries(self):
        """Test nested error boundaries"""
        def inner_operation():
            raise ValueError("Inner error")

        def outer_operation():
            with error_boundary("inner boundary", "core"):
                inner_operation()

        with pytest.raises(ValueError):
            with error_boundary("outer boundary", "core"):
                outer_operation()

    def test_exception_with_circular_reference(self):
        """Test exception handling with circular references in context"""
        circular_dict = {}
        circular_dict['self'] = circular_dict

        # Should handle circular references gracefully
        exc = BrowserException(
            message="Test with circular reference",
            browser_action="circular_test",
            context={'circular': circular_dict}
        )

        # Should not raise exception when converting to string
        str_repr = str(exc)
        assert "Test with circular reference" in str_repr

    def test_exception_with_large_context(self):
        """Test exception with very large context data"""
        large_context = {
            'large_data': 'x' * 10000,  # 10KB of data
            'list_data': list(range(1000)),
            'nested_data': {'level1': {'level2': {'level3': 'deep_value'}}}
        }

        exc = DataExtractionException(
            message="Test with large context",
            data_type="large_test",
            context=large_context
        )

        # Should handle large context without issues
        assert exc.context['large_data'] == 'x' * 10000
        assert len(exc.context['list_data']) == 1000

    def test_concurrent_exception_handling(self):
        """Test exception handling under concurrent access"""
        import threading
        import time

        exceptions_created = []

        def create_exceptions():
            for i in range(100):
                exc = NetworkException(
                    message=f"Concurrent test {i}",
                    details=f"Thread {threading.current_thread().ident}"
                )
                exceptions_created.append(exc)
                time.sleep(0.001)  # Small delay to increase chance of race conditions

        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_exceptions)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Should have created all exceptions without issues
        assert len(exceptions_created) == 500

        # All exceptions should be properly formed
        for exc in exceptions_created:
            assert isinstance(exc, NetworkException)
            assert "Concurrent test" in exc.message


class TestErrorHandlingDocumentationExamples:
    """Test examples from the error handling documentation"""

    @pytest.mark.asyncio
    async def test_documentation_example_page_load(self):
        """Test page load example from documentation"""
        async def mock_page_goto(url):
            raise Exception("Network timeout")

        mock_page = AsyncMock()
        mock_page.goto = mock_page_goto

        with pytest.raises(PageLoadException) as exc_info:
            with error_boundary("page navigation", "core"):
                try:
                    await mock_page.goto("https://example.com")
                except Exception as e:
                    raise PageLoadException(
                        message="Failed to navigate to target URL",
                        url="https://example.com",
                        details=str(e),
                        suggested_action="Check URL validity and network connectivity"
                    )

        assert exc_info.value.context['url'] == "https://example.com"
        assert "Check URL validity" in exc_info.value.suggested_action

    def test_documentation_example_element_not_found(self):
        """Test element not found example from documentation"""
        selectors = ["#search", ".search-input", "[name='search']"]

        with pytest.raises(ElementNotFoundException) as exc_info:
            raise ElementNotFoundException(
                message="Unable to locate dropdown suggestion elements",
                selector=", ".join(selectors),
                url="https://example.com",
                suggested_action="Check if page structure has changed"
            )

        assert "#search, .search-input, [name='search']" in exc_info.value.context['selector']


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
