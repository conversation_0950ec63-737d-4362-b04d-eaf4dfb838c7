# Add users to their respective department groups
dn: cn=finance-group,ou=groups,dc=company,dc=com
changetype: modify
add: member
member: cn=john.doe,ou=people,dc=company,dc=com

dn: cn=risk-group,ou=groups,dc=company,dc=com
changetype: modify
add: member
member: cn=jane.smith,ou=people,dc=company,dc=com

dn: cn=technology-group,ou=groups,dc=company,dc=com
changetype: modify
add: member
member: cn=bob.wilson,ou=people,dc=company,dc=com

dn: cn=admin-group,ou=groups,dc=company,dc=com
changetype: modify
add: member
member: cn=alice.brown,ou=people,dc=company,dc=com

dn: cn=admin-group,ou=groups,dc=company,dc=com
changetype: modify
add: member
member: cn=ldap-admin,ou=people,dc=company,dc=com
