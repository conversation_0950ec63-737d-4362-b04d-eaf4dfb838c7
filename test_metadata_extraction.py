#!/usr/bin/env python3
"""
Test script for metadata extraction functionality in reform.py
"""

import os
import json
import tempfile
import shutil
from app.utils.reform import MarkdownTableReformer

def test_metadata_extraction():
    """Test the metadata extraction and separate file saving functionality"""
    
    # Create a temporary directory for testing
    test_dir = tempfile.mkdtemp()
    
    try:
        # Sample markdown content with metadata
        sample_content = '''<!-- METADATA: {"file": "test_table.md", "table_name": "财务报表", "date": "2024年9月", "metadata": ["公司: 测试公司", "编制: 财务部"]} -->

# 财务报表

| 项目 | 金额 | 备注 |
|------|------|------|
| 收入 | 1000 | 主营业务 |
| 支出 | 800 | 运营成本 |
| 利润 | 200 | 净利润 |
'''

        # Create input file
        input_file = os.path.join(test_dir, "test_input.md")
        with open(input_file, 'w', encoding='utf-8') as f:
            f.write(sample_content)
        
        # Initialize reformer
        reformer = MarkdownTableReformer()
        
        # Test process_file method
        output_file = os.path.join(test_dir, "test_output.md")
        reformer.process_file(input_file, output_file)
        
        # Check if markdown file was created without metadata comment
        assert os.path.exists(output_file), "Output markdown file should exist"
        
        with open(output_file, 'r', encoding='utf-8') as f:
            output_content = f.read()
        
        # Verify metadata comment was removed
        assert not output_content.startswith('<!-- METADATA:'), "Metadata comment should be removed from markdown"
        
        # Verify content structure is preserved
        assert '# 财务报表' in output_content, "Title should be preserved"
        assert '| 项目 | 金额 | 备注 |' in output_content, "Table structure should be preserved"
        
        # Check if JSON file was created
        json_file = os.path.join(test_dir, "test_output.json")
        assert os.path.exists(json_file), "JSON metadata file should exist"
        
        # Verify JSON content
        with open(json_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        expected_metadata = {
            "file": "test_table.md",
            "table_name": "财务报表", 
            "date": "2024年9月",
            "metadata": ["公司: 测试公司", "编制: 财务部"]
        }
        
        assert metadata == expected_metadata, f"Metadata should match expected: {metadata}"
        
        print("✓ test_metadata_extraction passed")
        
    finally:
        # Clean up
        shutil.rmtree(test_dir)

def test_no_metadata_content():
    """Test processing content without metadata comment"""
    
    test_dir = tempfile.mkdtemp()
    
    try:
        # Sample content without metadata
        sample_content = '''# 普通表格

| 列1 | 列2 |
|-----|-----|
| 值1 | 值2 |
'''

        input_file = os.path.join(test_dir, "test_no_metadata.md")
        with open(input_file, 'w', encoding='utf-8') as f:
            f.write(sample_content)
        
        reformer = MarkdownTableReformer()
        output_file = os.path.join(test_dir, "test_no_metadata_output.md")
        reformer.process_file(input_file, output_file)
        
        # Check markdown file exists
        assert os.path.exists(output_file), "Output markdown file should exist"
        
        # Check JSON file was NOT created
        json_file = os.path.join(test_dir, "test_no_metadata_output.json")
        assert not os.path.exists(json_file), "JSON file should not be created when no metadata present"
        
        print("✓ test_no_metadata_content passed")
        
    finally:
        shutil.rmtree(test_dir)

def test_malformed_metadata():
    """Test handling of malformed metadata"""
    
    test_dir = tempfile.mkdtemp()
    
    try:
        # Sample content with malformed metadata
        sample_content = '''<!-- METADATA: {invalid json} -->

# 表格

| 列1 | 列2 |
|-----|-----|
| 值1 | 值2 |
'''

        input_file = os.path.join(test_dir, "test_malformed.md")
        with open(input_file, 'w', encoding='utf-8') as f:
            f.write(sample_content)
        
        reformer = MarkdownTableReformer()
        output_file = os.path.join(test_dir, "test_malformed_output.md")
        reformer.process_file(input_file, output_file)
        
        # Check markdown file exists and contains original content
        assert os.path.exists(output_file), "Output markdown file should exist"
        
        with open(output_file, 'r', encoding='utf-8') as f:
            output_content = f.read()
        
        # Should preserve original content when metadata is malformed
        assert '<!-- METADATA:' in output_content, "Original malformed metadata should be preserved"
        
        # Check JSON file was NOT created
        json_file = os.path.join(test_dir, "test_malformed_output.json")
        assert not os.path.exists(json_file), "JSON file should not be created for malformed metadata"
        
        print("✓ test_malformed_metadata passed")
        
    finally:
        shutil.rmtree(test_dir)

def test_process_files_batch():
    """Test batch processing with process_files method"""
    
    test_dir = tempfile.mkdtemp()
    
    try:
        # Create multiple test files
        files_data = [
            ("file1.md", '''<!-- METADATA: {"file": "file1.md", "table_name": "表1"} -->

# 表1

| A | B |
|---|---|
| 1 | 2 |
'''),
            ("file2.md", '''<!-- METADATA: {"file": "file2.md", "table_name": "表2"} -->

# 表2

| X | Y |
|---|---|
| 3 | 4 |
''')
        ]
        
        input_files = []
        for filename, content in files_data:
            input_file = os.path.join(test_dir, filename)
            with open(input_file, 'w', encoding='utf-8') as f:
                f.write(content)
            input_files.append(input_file)
        
        reformer = MarkdownTableReformer()
        output_files = reformer.process_files(input_files, test_dir)
        
        # Check that both files were processed
        assert len(output_files) == 2, "Should process both files"
        
        for i, (filename, _) in enumerate(files_data):
            # Check markdown file
            md_file = os.path.join(test_dir, f"{filename.split('.')[0]}_reformed.md")
            assert os.path.exists(md_file), f"Reformed markdown file should exist: {md_file}"
            
            # Check JSON file
            json_file = os.path.join(test_dir, f"{filename.split('.')[0]}_reformed.json")
            assert os.path.exists(json_file), f"JSON metadata file should exist: {json_file}"
            
            # Verify JSON content
            with open(json_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            assert "table_name" in metadata, "Metadata should contain table_name"
        
        print("✓ test_process_files_batch passed")
        
    finally:
        shutil.rmtree(test_dir)

if __name__ == "__main__":
    print("Testing metadata extraction functionality...")
    
    test_metadata_extraction()
    test_no_metadata_content()
    test_malformed_metadata()
    test_process_files_batch()
    
    print("\n✅ All tests passed! Metadata extraction functionality is working correctly.")
