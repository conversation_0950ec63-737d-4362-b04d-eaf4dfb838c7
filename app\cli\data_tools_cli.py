import logging
import sys
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

# Import unified error handling
from app.exceptions import CLIException
from app.error_handling import log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for CLI operations
set_layer_context("cli")

from app.cli.engine import CLIEngine
from app.utils.data_tools import (
    ProcessCSVCommand,
    LoadToSQLiteCommand,
    QuerySQLiteCommand,
    CalculateYoYCommand,
    CalculateMoMCommand,
    ExtractBracesCommand,
    InterpretCommand,
    ReportCommand,
    StoreCommand
)

@log_and_reraise(logger, "data tools CLI main")
def main():
    """Main function for data tools CLI with unified error handling"""
    set_operation_context("data_tools_cli_main")

    try:
        engine = CLIEngine(prog="data_tools")

        # Register all commands
        engine.register_command("process_csv", ProcessCSVCommand)
        engine.register_command("load_to_sqlite", LoadToSQLiteCommand)
        engine.register_command("query", QuerySQLiteCommand)
        engine.register_command("yoy", CalculateYoYCommand)
        engine.register_command("mom", CalculateMoMCommand)
        engine.register_command("extract_braces", ExtractBracesCommand)
        engine.register_command("interpret", InterpretCommand)
        engine.register_command("report", ReportCommand)
        engine.register_command("store", StoreCommand)

        engine.run()

    except CLIException as e:
        logger.error(f"CLI error: {e.message}")
        if e.details:
            logger.error(f"Details: {e.details}")
        if e.suggested_action:
            logger.info(f"Suggested action: {e.suggested_action}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()