import json
from pathlib import Path
from typing import List, Dict

def parse_con(file_path: Path) -> List[Dict]:
    """
    Parse con.md file and extract structured data.
    
    Returns:
        List of dicts with keys: prompt, think, answer, reference
        Each dict corresponds to one user message and its assistant response
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    result = []
    
    for item in data:
        messages = item.get('messages', [])
        for i in range(len(messages)):
            if messages[i]['role'] == 'user':
                prompt = messages[i]['content']
                # Look for next assistant response
                if i+1 < len(messages):
                    content = messages[i+1]['content']
                    think_start = content.find('<think>')
                    think_end = content.find('</think>')
                    
                    think = content[think_start+7:think_end].strip() if think_start != -1 else ''
                    answer = content[think_end+8:].strip() if think_end != -1 else content
                    
                    # Process references
                    references = set()
                    for ref in messages[i+1].get('reference', []):
                        doc_name = ref.get('document_name', '')
                        if doc_name:
                            references.add(doc_name)
                    
                    result.append({
                        'prompt': prompt,
                        'think': think,
                        'answer': answer,
                        'reference': sorted(list(references)),
                        'created_at': messages[i+1]['created_at']
                    })
    
    return sorted(result, key=lambda x: x['created_at'])
