# Create groups for departments
dn: cn=finance-group,ou=groups,dc=company,dc=com
objectClass: groupOfNames
cn: finance-group
description: Finance Department Group
member: cn=admin,dc=company,dc=com

dn: cn=risk-group,ou=groups,dc=company,dc=com
objectClass: groupOfNames
cn: risk-group
description: Risk Management Department Group
member: cn=admin,dc=company,dc=com

dn: cn=technology-group,ou=groups,dc=company,dc=com
objectClass: groupOfNames
cn: technology-group
description: Technology Department Group
member: cn=admin,dc=company,dc=com

dn: cn=admin-group,ou=groups,dc=company,dc=com
objectClass: groupOfNames
cn: admin-group
description: Administration Department Group
member: cn=admin,dc=company,dc=com
