# Error Handling Quick Reference

## Quick Import Guide

```python
# Core exceptions and types
from app.exceptions import (
    BaseAppException, LayerType, ErrorSeverity,
    PresentationLayerException, BusinessLogicException, UtilityException,
    APIException, AuthenticationException, ValidationException,
    WorkflowException, AgentException, FileProcessingException,
    DataProcessingException, ConfigurationException, ExternalServiceException
)

# Error handling utilities
from app.error_handling import (
    handle_layer_boundary, log_and_reraise, error_boundary, safe_execute
)

# Enhanced logging
from app.logging_config import (
    set_layer_context, set_operation_context, get_layer_logger
)
```

## Common Usage Patterns

### 1. API Endpoint (Presentation Layer)

```python
@handle_layer_boundary(LayerType.PRESENTATION, "user registration")
def register_user(user_data: dict):
    # Validate input
    if not user_data.get('email'):
        raise ValidationException(
            message="Email is required",
            field_name="email",
            context={'provided_fields': list(user_data.keys())}
        )
    
    # Call business logic
    return user_service.create_user(user_data)
```

### 2. Business Logic Function

```python
@handle_layer_boundary(LayerType.BUSINESS_LOGIC, "document processing")
def process_document(file_path: str):
    # Validate business rules
    if not is_valid_document_type(file_path):
        raise WorkflowException(
            message="Unsupported document type",
            workflow_name="DocumentProcessor",
            step="validation",
            context={'file_path': file_path, 'file_type': get_file_type(file_path)}
        )
    
    # Call utility layer
    return document_converter.convert(file_path)
```

### 3. Utility Function

```python
@log_and_reraise(logger, "file conversion")
def convert_pdf_to_markdown(pdf_path: str) -> str:
    if not os.path.exists(pdf_path):
        raise FileProcessingException(
            message="PDF file not found",
            file_path=pdf_path,
            suggested_action="Check file path and permissions"
        )
    
    # Conversion logic here
    return markdown_content
```

### 4. Error Boundary Usage

```python
def batch_process_files(file_list: List[str]):
    results = []
    
    for file_path in file_list:
        with error_boundary("single file processing", LayerType.UTILITY):
            try:
                result = process_single_file(file_path)
                results.append(result)
            except FileProcessingException:
                # Log error but continue with other files
                logger.warning(f"Failed to process {file_path}, skipping")
                continue
    
    return results
```

## Exception Creation Cheat Sheet

### Validation Errors
```python
raise ValidationException(
    message="Invalid input data",
    field_name="email",  # Optional: specific field
    details="Email format is invalid",
    context={'provided_email': email, 'expected_format': '<EMAIL>'}
)
```

### File Processing Errors
```python
raise FileProcessingException(
    message="Failed to process file",
    file_path=file_path,
    details=f"Unsupported format: {file_extension}",
    suggested_action="Use PDF, DOCX, or TXT files"
)
```

### Authentication Errors
```python
raise AuthenticationException(
    message="Login failed",
    details="Invalid username or password",
    context={'username': username, 'attempt_count': attempts}
)
```

### Configuration Errors
```python
raise ConfigurationException(
    message="Missing required configuration",
    config_key="OPENAI_API_KEY",
    details="Environment variable not set",
    suggested_action="Set OPENAI_API_KEY in environment or .env file"
)
```

### External Service Errors
```python
raise ExternalServiceException(
    message="External API call failed",
    service_name="OpenAI API",
    details=f"HTTP {response.status_code}: {response.text}",
    context={'endpoint': url, 'request_id': request_id}
)
```

## Logging Context Setup

```python
# Set context at the beginning of operations
set_layer_context("business_logic")
set_operation_context("report generation")

# Get layer-specific logger
logger = get_layer_logger("utility", "file_processor")

# Your code with enhanced logging
logger.info("Starting file processing")  # Will include layer/operation context
```

## FastAPI Exception Handling

```python
from fastapi import HTTPException

# Global exception handler is already configured
# Just raise your custom exceptions and they'll be handled automatically

@app.post("/api/process")
async def process_data(data: dict):
    # Validation
    if not data:
        raise ValidationException("Request body cannot be empty")
    
    # Business logic call
    result = await business_service.process(data)
    return result
```

## Testing Patterns

```python
import pytest
from app.exceptions import ValidationException

def test_validation_error():
    with pytest.raises(ValidationException) as exc_info:
        validate_user_data({})
    
    assert exc_info.value.error_code == "VAL_001"
    assert exc_info.value.field_name == "email"
    assert "required" in exc_info.value.message.lower()

def test_layer_boundary_wrapping():
    with patch('app.utils.file_processor.process') as mock:
        mock.side_effect = FileNotFoundError("File not found")
        
        with pytest.raises(BusinessLogicException) as exc_info:
            business_function_that_calls_utility()
        
        # Verify original exception is preserved
        assert isinstance(exc_info.value.original_exception, FileNotFoundError)
```

## Error Codes Reference

| Code | Layer | Description |
|------|-------|-------------|
| AUTH_001 | Presentation | Authentication failure |
| VAL_001 | Presentation | Validation error |
| UI_001 | Presentation | UI error |
| CLI_001 | Presentation | CLI error |
| WF_001 | Business Logic | Workflow error |
| AGENT_001 | Business Logic | Agent error |
| RAG_001 | Business Logic | RAG workflow error |
| RPT_001 | Business Logic | Report workflow error |
| MODEL_001 | Business Logic | Model error |
| FILE_001 | Utility | File processing error |
| DATA_001 | Utility | Data processing error |
| CONFIG_001 | Utility | Configuration error |
| EXT_001 | Utility | External service error |

## Migration Checklist

### From Old Exception Handling

- [ ] Replace generic `Exception` with specific exception types
- [ ] Add `@handle_layer_boundary` decorators at layer boundaries
- [ ] Remove defensive try-catch blocks that don't add value
- [ ] Add meaningful context to all custom exceptions
- [ ] Update error messages to be user-friendly
- [ ] Add suggested actions for recoverable errors
- [ ] Update tests to check for specific exception types

### Code Review Checklist

- [ ] Are exceptions crossing layer boundaries properly wrapped?
- [ ] Do all custom exceptions include meaningful context?
- [ ] Are error messages user-friendly and actionable?
- [ ] Is logging context set appropriately?
- [ ] Are try-catch blocks adding value or just defensive?
- [ ] Are exception chains preserved when wrapping?
- [ ] Do tests verify exception handling behavior?

## Performance Tips

1. **Avoid exceptions in hot paths** - Use return values for expected conditions
2. **Lazy load context** - Only compute expensive context when needed
3. **Use appropriate severity levels** - Don't log low-severity errors as critical
4. **Batch operations** - Use error boundaries for batch processing
5. **Cache loggers** - Get layer loggers once and reuse

## Debugging Tips

1. **Check error codes** - Use standardized codes for monitoring
2. **Follow request IDs** - Trace requests through logs
3. **Examine context** - Use `get_full_context()` for debugging
4. **Check original exceptions** - Look at `original_exception` for root cause
5. **Use layer context** - Filter logs by layer for focused debugging
