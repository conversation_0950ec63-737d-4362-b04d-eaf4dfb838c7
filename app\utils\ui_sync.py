from typing import Dict, Optional
from pathlib import Path
import logging

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    DataProcessingException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

@log_and_reraise(logger, "agent state to UI sync")
def sync_state_to_ui(agent_state: Dict, ui_state: Dict) -> Dict:
    """同步agent状态到UI with unified error handling"""
    set_operation_context("agent_state_to_ui_sync")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if not isinstance(agent_state, dict):
            raise ValidationException(
                message="Agent state must be a dictionary",
                field_name="agent_state",
                details=f"Expected dict, got {type(agent_state)}",
                context={'input_type': type(agent_state).__name__},
                suggested_action="Provide a valid agent state dictionary"
            )

        if not isinstance(ui_state, dict):
            raise ValidationException(
                message="UI state must be a dictionary",
                field_name="ui_state",
                details=f"Expected dict, got {type(ui_state)}",
                context={'input_type': type(ui_state).__name__},
                suggested_action="Provide a valid UI state dictionary"
            )

    logger.info("Syncing agent state to UI")

    # Update core state
    with error_boundary("core state update", LayerType.UTILITY):
        try:
            ui_state.update({
                'company': agent_state.get('company'),
                'year': agent_state.get('year'),
                'quarter': agent_state.get('quarter'),
                'template': agent_state.get('template')
            })
        except Exception as e:
            raise DataProcessingException(
                message="Failed to update core UI state",
                details=f"State update error: {str(e)}",
                original_exception=e,
                suggested_action="Check agent state structure and UI state compatibility"
            )

    # Update file count
    with error_boundary("file count update", LayerType.UTILITY):
        try:
            if agent_state.get('company') and agent_state.get('year'):
                workspace_path = Path(f"workspaces/{agent_state.get('workspace_id')}/{agent_state['company']}/{agent_state['year']}")
                if workspace_path.exists():
                    file_count = len([
                        f for f in workspace_path.glob("*.*")
                        if f.suffix.lower() in ('.pdf','.xls','.xlsx')
                    ])
                    ui_state['materials_count'] = file_count
        except Exception as e:
            # Don't fail the sync if file counting fails
            logger.warning(f"Failed to update file count: {str(e)}")
            ui_state['materials_count'] = 0

    return ui_state

@log_and_reraise(logger, "UI state to agent sync")
def sync_state_from_ui(ui_state: Dict, agent_state: Dict) -> Dict:
    """从UI同步状态到agent with unified error handling"""
    set_operation_context("ui_state_to_agent_sync")

    # Validate input
    with error_boundary("input validation", LayerType.UTILITY):
        if not isinstance(ui_state, dict):
            raise ValidationException(
                message="UI state must be a dictionary",
                field_name="ui_state",
                details=f"Expected dict, got {type(ui_state)}",
                context={'input_type': type(ui_state).__name__},
                suggested_action="Provide a valid UI state dictionary"
            )

        if not isinstance(agent_state, dict):
            raise ValidationException(
                message="Agent state must be a dictionary",
                field_name="agent_state",
                details=f"Expected dict, got {type(agent_state)}",
                context={'input_type': type(agent_state).__name__},
                suggested_action="Provide a valid agent state dictionary"
            )

    logger.info("Syncing UI state to agent")

    # Update core state
    with error_boundary("core state update", LayerType.UTILITY):
        try:
            agent_state.update({
                'company': ui_state.get('company'),
                'year': ui_state.get('year'),
                'quarter': ui_state.get('quarter'),
                'template': ui_state.get('template')
            })

            return agent_state

        except Exception as e:
            raise DataProcessingException(
                message="Failed to update agent state from UI",
                details=f"State update error: {str(e)}",
                original_exception=e,
                suggested_action="Check UI state structure and agent state compatibility"
            )
