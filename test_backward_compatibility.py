#!/usr/bin/env python3
"""
Test backward compatibility of the enhanced classification system
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer
import json
import re

def test_backward_compatibility():
    """Test that the enhanced system maintains backward compatibility"""
    reformer = MarkdownTableReformer()
    
    print("=== Testing Backward Compatibility ===")
    
    # Test case 1: Simple table (should work as before)
    simple_content = """| 项目 | 金额 |
| --- | --- |
| 收入 | 1000 |
| 支出 | 800 |"""
    
    print("Test 1: Simple table")
    result1 = reformer.process_table(simple_content, "simple_table.md")
    print("✅ Simple table processed successfully")
    
    # Test case 2: Complex table with metadata (should work as before)
    complex_content = """| 利润表 | | |
| 时间：2024年9月 | | 单位：元 |
| 编制单位：测试公司 | | |
| --- | --- | --- |
| 项目 | 本期金额 | 上期金额 |
| 营业收入 | 1000000 | 900000 |
| 营业成本 | 600000 | 550000 |"""
    
    print("Test 2: Complex table with metadata")
    result2 = reformer.process_table(complex_content, "complex_table.md")
    
    # Check that metadata is extracted
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result2)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        metadata_list = metadata.get('metadata', [])
        if metadata_list:
            print(f"✅ Metadata correctly extracted: {metadata_list}")
        else:
            print("⚠️  No metadata extracted (might be expected)")
    else:
        print("⚠️  No metadata section found")
    
    # Test case 3: The original problematic case (should now be fixed)
    problematic_content = """|  |  |  |  |  |
| --- | --- | --- | --- | --- |
| 1、全量客群-规模、余额 |  |  |  |  |
| 放款时间 | 当月放款金额(元） | 当月放款笔数 | 在贷本金(元） |  |
| 2021-09-01 | 2369184103.11 | 460151 | 14163781481.84 |  |
| 2021-10-01 | 2264739324 | 443158 | 14093419476.16 |  |"""
    
    print("Test 3: Original problematic case")
    result3 = reformer.process_table(problematic_content, "2、业务数据_1-规模_A1E39_Sheet1.md")
    
    # Check that table headers are NOT in metadata
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result3)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        metadata_list = metadata.get('metadata', [])
        
        # Check for problematic table headers in metadata
        problematic_headers = ['放款时间', '当月放款金额', '当月放款笔数', '在贷本金']
        headers_in_metadata = [item for item in metadata_list 
                             if any(header in item for header in problematic_headers)]
        
        if headers_in_metadata:
            print(f"❌ REGRESSION: Table headers found in metadata: {headers_in_metadata}")
            return False
        else:
            print("✅ FIXED: No table headers found in metadata")
    
    print("\n=== All Backward Compatibility Tests Passed ===")
    return True

def test_method_availability():
    """Test that all expected methods are available"""
    reformer = MarkdownTableReformer()
    
    print("=== Testing Method Availability ===")
    
    required_methods = [
        'process_table',
        'process_file', 
        'process_directory',
        'parse_markdown_table',
        'contains_header_keywords',
        'contains_date_or_metadata_keywords'
    ]
    
    for method_name in required_methods:
        if hasattr(reformer, method_name):
            print(f"✅ {method_name} available")
        else:
            print(f"❌ {method_name} missing")
            return False
    
    # Test new enhanced methods
    enhanced_methods = [
        'analyze_row_density',
        'has_colon_separators', 
        'has_date_patterns',
        'analyze_time_keyword_context',
        'calculate_classification_score'
    ]
    
    for method_name in enhanced_methods:
        if hasattr(reformer, method_name):
            print(f"✅ Enhanced method {method_name} available")
        else:
            print(f"❌ Enhanced method {method_name} missing")
            return False
    
    print("✅ All methods available")
    return True

def main():
    """Run all tests"""
    print("Testing Enhanced Classification System - Backward Compatibility")
    print("="*70)
    
    try:
        # Test method availability
        if not test_method_availability():
            print("❌ Method availability test failed")
            return 1
        
        print()
        
        # Test backward compatibility
        if not test_backward_compatibility():
            print("❌ Backward compatibility test failed")
            return 1
        
        print("\n🎉 All backward compatibility tests passed!")
        return 0
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
