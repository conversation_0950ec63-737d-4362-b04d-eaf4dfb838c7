#!/usr/bin/env python3
"""
Test script for RAGFlow metadata update functionality in md_tools.py
"""

import os
import json
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
from app.utils.md_tools import MarkdownTools

def test_update_documents_metadata():
    """Test the metadata update functionality"""
    
    # Create a temporary directory for testing
    test_dir = tempfile.mkdtemp()
    
    try:
        # Create test markdown file
        md_content = '''# Test Table

| Column 1 | Column 2 |
|----------|----------|
| Value 1  | Value 2  |
'''
        md_file = os.path.join(test_dir, "test_document.md")
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        # Create corresponding JSON metadata file
        metadata = {
            "file": "test_document.md",
            "table_name": "Test Table",
            "date": "2024年9月",
            "metadata": ["公司: 测试公司", "编制: 财务部"]
        }
        json_file = os.path.join(test_dir, "test_document.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        # Mock RAGFlow client
        mock_client = Mock()
        mock_client.update_document = Mock(return_value={"status": "success"})
        
        # Mock upload response
        upload_response = [
            {
                "id": "doc_123",
                "name": "test_document.md",
                "status": "uploaded"
            }
        ]
        
        uploaded_files = [md_file]
        
        # Initialize MarkdownTools and call the metadata update method
        tools = MarkdownTools()
        tools._update_documents_metadata(
            client=mock_client,
            dataset_id="dataset_456",
            upload_response=upload_response,
            uploaded_files=uploaded_files
        )
        
        # Verify that update_document was called with correct parameters
        mock_client.update_document.assert_called_once_with(
            dataset_id="dataset_456",
            document_id="doc_123",
            meta_fields=metadata
        )
        
        print("✓ test_update_documents_metadata passed")
        
    finally:
        # Clean up
        shutil.rmtree(test_dir)

def test_no_metadata_file():
    """Test behavior when no metadata file exists"""
    
    test_dir = tempfile.mkdtemp()
    
    try:
        # Create test markdown file without corresponding JSON
        md_content = '''# Test Table

| Column 1 | Column 2 |
|----------|----------|
| Value 1  | Value 2  |
'''
        md_file = os.path.join(test_dir, "test_no_metadata.md")
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        # Mock RAGFlow client
        mock_client = Mock()
        mock_client.update_document = Mock()
        
        # Mock upload response
        upload_response = [
            {
                "id": "doc_789",
                "name": "test_no_metadata.md",
                "status": "uploaded"
            }
        ]
        
        uploaded_files = [md_file]
        
        # Initialize MarkdownTools and call the metadata update method
        tools = MarkdownTools()
        tools._update_documents_metadata(
            client=mock_client,
            dataset_id="dataset_456",
            upload_response=upload_response,
            uploaded_files=uploaded_files
        )
        
        # Verify that update_document was NOT called
        mock_client.update_document.assert_not_called()
        
        print("✓ test_no_metadata_file passed")
        
    finally:
        shutil.rmtree(test_dir)

def test_malformed_json_metadata():
    """Test handling of malformed JSON metadata"""
    
    test_dir = tempfile.mkdtemp()
    
    try:
        # Create test markdown file
        md_content = '''# Test Table

| Column 1 | Column 2 |
|----------|----------|
| Value 1  | Value 2  |
'''
        md_file = os.path.join(test_dir, "test_malformed.md")
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        # Create malformed JSON metadata file
        json_file = os.path.join(test_dir, "test_malformed.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            f.write('{ invalid json content }')
        
        # Mock RAGFlow client
        mock_client = Mock()
        mock_client.update_document = Mock()
        
        # Mock upload response
        upload_response = [
            {
                "id": "doc_999",
                "name": "test_malformed.md",
                "status": "uploaded"
            }
        ]
        
        uploaded_files = [md_file]
        
        # Initialize MarkdownTools and call the metadata update method
        tools = MarkdownTools()
        tools._update_documents_metadata(
            client=mock_client,
            dataset_id="dataset_456",
            upload_response=upload_response,
            uploaded_files=uploaded_files
        )
        
        # Verify that update_document was NOT called due to JSON error
        mock_client.update_document.assert_not_called()
        
        print("✓ test_malformed_json_metadata passed")
        
    finally:
        shutil.rmtree(test_dir)

def test_non_markdown_files():
    """Test that non-markdown files are skipped"""
    
    test_dir = tempfile.mkdtemp()
    
    try:
        # Create test PDF file (non-markdown)
        pdf_file = os.path.join(test_dir, "test_document.pdf")
        with open(pdf_file, 'wb') as f:
            f.write(b'%PDF-1.4 fake pdf content')
        
        # Create corresponding JSON metadata file
        metadata = {"file": "test_document.pdf", "type": "pdf"}
        json_file = os.path.join(test_dir, "test_document.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        # Mock RAGFlow client
        mock_client = Mock()
        mock_client.update_document = Mock()
        
        # Mock upload response
        upload_response = [
            {
                "id": "doc_pdf",
                "name": "test_document.pdf",
                "status": "uploaded"
            }
        ]
        
        uploaded_files = [pdf_file]
        
        # Initialize MarkdownTools and call the metadata update method
        tools = MarkdownTools()
        tools._update_documents_metadata(
            client=mock_client,
            dataset_id="dataset_456",
            upload_response=upload_response,
            uploaded_files=uploaded_files
        )
        
        # Verify that update_document was NOT called for non-markdown files
        mock_client.update_document.assert_not_called()
        
        print("✓ test_non_markdown_files passed")
        
    finally:
        shutil.rmtree(test_dir)

if __name__ == "__main__":
    print("Testing RAGFlow metadata update functionality...")
    
    test_update_documents_metadata()
    test_no_metadata_file()
    test_malformed_json_metadata()
    test_non_markdown_files()
    
    print("\n✅ All tests passed! RAGFlow metadata update functionality is working correctly.")
