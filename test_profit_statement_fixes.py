#!/usr/bin/env python3
"""
Test the fixes for profit statement header detection and date extraction
"""

import sys
import os
from pathlib import Path
import json
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer
from app.utils.reheader import MarkdownTableHeaderAdjuster

def test_header_detection_for_kemurow():
    """Test that the row with '科目' is properly detected as a header"""
    print("="*80)
    print("TESTING HEADER DETECTION FOR '科目' ROW")
    print("="*80)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Read the profit statement file
    with open('test3\\-季度合并财报（2024年Q4）_利润表_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Processing profit statement file...")
    
    # Parse the table
    rows = adjuster.parse_table(content)
    
    print(f"Total rows parsed: {len(rows)}")
    print()
    
    # Show first few rows
    print("First 5 rows:")
    for i, row in enumerate(rows[:5]):
        if adjuster.is_separator_row(row):
            print(f"Row {i+1}: SEPARATOR - {row}")
        else:
            print(f"Row {i+1}: {row}")
    
    print()
    
    # Test header detection specifically for the '科目' row
    kemu_row = ['科目', '2024-12-31', '2024年Q4', '2024年']
    print(f"Testing '科目' row: {kemu_row}")
    
    # Test individual scoring components
    has_keywords = adjuster.contains_header_keywords(kemu_row)
    keyword_count = adjuster.count_header_keywords(kemu_row)
    has_strong = adjuster.has_strong_header_keywords(kemu_row)
    row_density = adjuster.calculate_row_density(kemu_row)
    string_density = adjuster.calculate_string_density(kemu_row)
    
    print(f"  Has header keywords: {has_keywords}")
    print(f"  Keyword count: {keyword_count}")
    print(f"  Has strong keywords: {has_strong}")
    print(f"  Row density: {row_density:.2f}")
    print(f"  String density: {string_density:.2f}")
    
    # Calculate comprehensive score
    score = adjuster.calculate_comprehensive_header_score(kemu_row, 2, len(rows), rows)
    print(f"  Comprehensive score: {score:.2f}")
    
    print()
    
    # Find all header candidates
    candidates = adjuster.find_all_header_candidates(rows)
    print("Top 5 header candidates:")
    for i, (row_idx, candidate_score) in enumerate(candidates[:5]):
        row = rows[row_idx]
        print(f"  {i+1}. Row {row_idx+1}: Score {candidate_score:.2f} - {row}")
    
    print()
    
    # Check if the '科目' row is in the top candidates
    kemu_found = False
    for row_idx, candidate_score in candidates:
        if '科目' in rows[row_idx]:
            print(f"✅ '科目' row found at position {row_idx+1} with score {candidate_score:.2f}")
            kemu_found = True
            break
    
    if not kemu_found:
        print("❌ '科目' row not found in header candidates")
    
    return kemu_found

def test_date_extraction_priority():
    """Test that table content date has priority over filename date"""
    print("\n" + "="*80)
    print("TESTING DATE EXTRACTION PRIORITY")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Read the profit statement file
    with open('test3\\-季度合并财报（2024年Q4）_利润表_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_利润表_adjusted.md"
    
    print(f"Processing file: {filename}")
    
    # Parse the table
    table_data, separator_line, markdown_headers = reformer.parse_markdown_table(content)
    header_rows = table_data[:separator_line] if separator_line > 0 else []
    
    print(f"Separator line: {separator_line}")
    print(f"Header rows: {header_rows}")
    print(f"First few data rows: {table_data[separator_line:separator_line+3] if separator_line < len(table_data) else []}")
    
    print()
    
    # Test date extraction from header lines (should now include first few data rows)
    print("Testing enhanced date extraction:")
    extracted_date = reformer.extract_date_from_header_lines(header_rows, table_data)
    print(f"Date from header lines (including data rows): '{extracted_date}'")
    
    # Test date extraction from filename
    filename_date = reformer.extract_date_from_filename(filename)
    print(f"Date from filename: '{filename_date}'")
    
    # Test comprehensive date extraction
    comprehensive_date = reformer.comprehensive_date_extraction(header_rows, filename, table_data)
    print(f"Comprehensive date: '{comprehensive_date}'")
    
    print()
    
    # Expected: should extract "2024-12-31" from table content
    expected_date = "2024-12-31"
    
    print(f"Expected date (from table content): '{expected_date}'")
    print(f"Actual comprehensive date: '{comprehensive_date}'")
    
    if comprehensive_date == expected_date:
        print("✅ Table content date has priority over filename date")
        priority_correct = True
    else:
        print("❌ Priority not working correctly")
        priority_correct = False
    
    return priority_correct

def test_full_table_processing():
    """Test the full table processing with both fixes"""
    print("\n" + "="*80)
    print("TESTING FULL TABLE PROCESSING")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Read the profit statement file
    with open('test3\\-季度合并财报（2024年Q4）_利润表_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_利润表_adjusted.md"
    
    print(f"Processing file: {filename}")
    
    # Process the table
    result = reformer.process_table(content, filename)
    
    # Extract metadata
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        
        print("Extracted metadata:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
        
        print()
        
        # Check if the correct date is extracted
        metadata_date = metadata.get('date', None)
        expected_date = "2024-12-31"
        
        print(f"Expected date in metadata: '{expected_date}'")
        print(f"Actual date in metadata: '{metadata_date}'")
        
        if metadata_date == expected_date:
            print("✅ Correct date stored in metadata")
            date_correct = True
        else:
            print("❌ Wrong date in metadata")
            date_correct = False
        
        # Check table name
        table_name = metadata.get('table_name', '')
        print(f"Table name: '{table_name}'")
        
        return date_correct
    else:
        print("❌ No metadata found")
        return False

def test_header_structure():
    """Test the header structure in the processed table"""
    print("\n" + "="*80)
    print("TESTING HEADER STRUCTURE")
    print("="*80)
    
    adjuster = MarkdownTableHeaderAdjuster()
    
    # Read the profit statement file
    with open('test3\\-季度合并财报（2024年Q4）_利润表_adjusted_reformed.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Processing with header adjuster...")
    
    # Process with header adjuster
    result = adjuster.adjust_table(content)
    
    # Show first 10 lines of result
    result_lines = result.split('\n')
    print("First 10 lines of processed result:")
    for i, line in enumerate(result_lines[:10]):
        print(f"{i+1:2d}: {line}")
    
    # Check if '科目' appears in the header structure
    header_found = False
    for i, line in enumerate(result_lines[:10]):
        if '科目' in line and ('2024-12-31' in line or '月度' in line):
            print(f"\n✅ Found '科目' in header structure at line {i+1}")
            header_found = True
            break
    
    if not header_found:
        print("\n❌ '科目' not found in proper header structure")
    
    return header_found

def main():
    """Run all tests for the profit statement fixes"""
    print("Testing Profit Statement Header Detection and Date Extraction Fixes")
    print("="*80)
    
    try:
        test1 = test_header_detection_for_kemurow()
        test2 = test_date_extraction_priority()
        test3 = test_full_table_processing()
        test4 = test_header_structure()
        
        print("="*80)
        print("FINAL RESULTS")
        print("="*80)
        print(f"Header Detection for '科目' Row: {'✅ PASS' if test1 else '❌ FAIL'}")
        print(f"Date Extraction Priority: {'✅ PASS' if test2 else '❌ FAIL'}")
        print(f"Full Table Processing: {'✅ PASS' if test3 else '❌ FAIL'}")
        print(f"Header Structure: {'✅ PASS' if test4 else '❌ FAIL'}")
        
        all_passed = test1 and test2 and test3 and test4
        
        if all_passed:
            print("\n🎉 All profit statement fixes working correctly!")
            print("✅ '科目' row properly detected as header")
            print("✅ Table content date prioritized over filename date")
            print("✅ Date '2024-12-31' preserved in original format")
            print("✅ Proper header structure maintained")
            return 0
        else:
            print("\n❌ Some fixes need attention")
            return 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
