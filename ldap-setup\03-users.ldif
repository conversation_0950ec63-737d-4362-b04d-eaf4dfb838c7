# Service admin user for LDAP operations
dn: cn=ldap-admin,ou=people,dc=company,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
cn: ldap-admin
sn: Administrator
givenName: LDAP
displayName: LDAP Administrator
uid: ldap-admin
uidNumber: 10000
gidNumber: 10000
homeDirectory: /home/<USER>
loginShell: /bin/bash
userPassword: ldap-admin123
mail: <EMAIL>
departmentNumber: admin
title: LDAP Administrator

# Test user 1 - Finance Department
dn: cn=john.doe,ou=people,dc=company,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
cn: john.doe
sn: Doe
givenName: John
displayName: John Doe
uid: john.doe
uidNumber: 10001
gidNumber: 10001
homeDirectory: /home/<USER>
loginShell: /bin/bash
userPassword: password123
mail: <EMAIL>
departmentNumber: finance
title: Financial Analyst

# Test user 2 - Risk Department
dn: cn=jane.smith,ou=people,dc=company,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
cn: jane.smith
sn: Smith
givenName: Jane
displayName: Jane Smith
uid: jane.smith
uidNumber: 10002
gidNumber: 10002
homeDirectory: /home/<USER>
loginShell: /bin/bash
userPassword: password123
mail: <EMAIL>
departmentNumber: risk
title: Risk Analyst

# Test user 3 - Technology Department
dn: cn=bob.wilson,ou=people,dc=company,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
cn: bob.wilson
sn: Wilson
givenName: Bob
displayName: Bob Wilson
uid: bob.wilson
uidNumber: 10003
gidNumber: 10003
homeDirectory: /home/<USER>
loginShell: /bin/bash
userPassword: password123
mail: <EMAIL>
departmentNumber: technology
title: Software Engineer

# Test user 4 - Admin Department
dn: cn=alice.brown,ou=people,dc=company,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
cn: alice.brown
sn: Brown
givenName: Alice
displayName: Alice Brown
uid: alice.brown
uidNumber: 10004
gidNumber: 10004
homeDirectory: /home/<USER>
loginShell: /bin/bash
userPassword: password123
mail: <EMAIL>
departmentNumber: admin
title: System Administrator

# Test user 5 - User without department (for testing fallbacks)
dn: cn=test.user,ou=people,dc=company,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
cn: test.user
sn: User
givenName: Test
displayName: Test User
uid: test.user
uidNumber: 10005
gidNumber: 10005
homeDirectory: /home/<USER>
loginShell: /bin/bash
userPassword: password123
mail: <EMAIL>
title: Test User
