#!/usr/bin/env python3
"""
Test the enhanced date extraction system with header line priority
"""

import sys
import os
from pathlib import Path
import json
import re

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reform import MarkdownTableReformer

def test_header_line_date_priority():
    """Test that header line dates have highest priority"""
    print("="*80)
    print("TESTING HEADER LINE DATE PRIORITY")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # (header_rows, filename, expected_date, description)
        (
            [['财务报表', '2024-09-30', ''], ['科目', '金额', '备注']],
            "财务数据_2024年Q1.md",
            "2024-09-30",
            "Full date in header should override filename quarter"
        ),
        (
            [['报表标题', '2024年9月30日', ''], ['项目', '数量', '']],
            "data_2024-01-01.md",
            "2024年9月30日",
            "Chinese full date in header should override filename"
        ),
        (
            [['财务数据', '2024年Q3', ''], ['科目', '金额', '']],
            "report_2024年Q1.md",
            "2024年Q3",
            "Quarter in header should override filename quarter"
        ),
        (
            [['科目', '金额', '备注'], ['收入', '1000', '']],
            "财务数据_2024-12-31.md",
            "2024-12-31",
            "No date in header, should use filename"
        ),
    ]
    
    print("Header Line Priority Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (header_rows, filename, expected_date, description) in enumerate(test_cases):
        extracted_date = reformer.comprehensive_date_extraction(header_rows, filename)
        
        print(f"Test {i+1}: {description}")
        print(f"  Header rows: {header_rows}")
        print(f"  Filename: {filename}")
        print(f"  Extracted: '{extracted_date}'")
        print(f"  Expected: '{expected_date}'")
        
        passed = extracted_date == expected_date
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_split_date_extraction():
    """Test extraction of dates split across multiple cells"""
    print("="*80)
    print("TESTING SPLIT DATE EXTRACTION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # (row, expected_date, description)
        (
            ['2024', '年', '9', '月', '30', '日'],
            "2024年9月30日",
            "Chinese date split across cells"
        ),
        (
            ['2024', '-', '09', '-', '30'],
            "2024-09-30",
            "International date with separators"
        ),
        (
            ['2024', '09', '30'],
            "2024-09-30",
            "International date without separators"
        ),
        (
            ['2024', '年', '12', '月'],
            "2024年12月",
            "Year-month split across cells"
        ),
        (
            ['财务报表', '2024', '年', '3', '月', '数据'],
            "2024年3月",
            "Date components mixed with other content"
        ),
        (
            ['项目', '金额', '备注'],
            None,
            "No date components"
        ),
    ]
    
    print("Split Date Extraction Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (row, expected_date, description) in enumerate(test_cases):
        extracted_date = reformer.extract_split_date_from_row(row)
        
        print(f"Test {i+1}: {description}")
        print(f"  Row: {row}")
        print(f"  Extracted: '{extracted_date}'")
        print(f"  Expected: '{expected_date}'")
        
        passed = extracted_date == expected_date
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_date_validation():
    """Test enhanced date validation including calendar validation"""
    print("="*80)
    print("TESTING ENHANCED DATE VALIDATION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # (date_string, should_be_valid, description)
        ("2024-09-30", True, "Valid full date"),
        ("2024年9月30日", True, "Valid Chinese full date"),
        ("2024-02-29", True, "Valid leap year date"),
        ("2023-02-29", False, "Invalid non-leap year date"),
        ("2024-13-01", False, "Invalid month"),
        ("2024-09-32", False, "Invalid day"),
        ("2024年2月30日", False, "Invalid February date"),
        ("2024-04-31", False, "Invalid April date (only 30 days)"),
        ("2024年Q3", True, "Valid quarter"),
        ("2024年9月", True, "Valid year-month"),
        ("2019-09-30", False, "Year too early"),
        ("2031-09-30", False, "Year too late"),
    ]
    
    print("Enhanced Date Validation Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (date_string, should_be_valid, description) in enumerate(test_cases):
        is_valid = reformer.validate_date_format(date_string)
        
        print(f"Test {i+1}: {description}")
        print(f"  Date: '{date_string}'")
        print(f"  Expected valid: {should_be_valid}")
        print(f"  Actually valid: {is_valid}")
        
        passed = is_valid == should_be_valid
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_date_prioritization():
    """Test that full dates get highest priority"""
    print("="*80)
    print("TESTING DATE PRIORITIZATION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    test_cases = [
        # (dates_list, expected_priority, description)
        (
            ["2024年Q3", "2024-09-30", "2024年9月"],
            "2024-09-30",
            "Full date should have highest priority"
        ),
        (
            ["2024年", "2024年9月30日", "Q3"],
            "2024年9月30日",
            "Chinese full date should have high priority"
        ),
        (
            ["Q3", "2024年9月", "2024年"],
            "2024年9月",
            "Year-month should beat quarter and year"
        ),
        (
            ["2024年", "2024年Q3", "Q1"],
            "2024年Q3",
            "Year-quarter should beat standalone formats"
        ),
        (
            ["2024", "2024年"],
            "2024年",
            "Chinese year should beat international year"
        ),
    ]
    
    print("Date Prioritization Tests:")
    print("-" * 50)
    
    all_passed = True
    for i, (dates_list, expected_priority, description) in enumerate(test_cases):
        prioritized_date = reformer.validate_and_prioritize_dates(dates_list)
        
        print(f"Test {i+1}: {description}")
        print(f"  Input dates: {dates_list}")
        print(f"  Prioritized: '{prioritized_date}'")
        print(f"  Expected: '{expected_priority}'")
        
        passed = prioritized_date == expected_priority
        print(f"  Result: {'✅ PASS' if passed else '❌ FAIL'}")
        
        if not passed:
            all_passed = False
        
        print()
    
    return all_passed

def test_real_file_integration():
    """Test with real balance sheet file to ensure proper date extraction"""
    print("="*80)
    print("TESTING REAL FILE INTEGRATION")
    print("="*80)
    
    reformer = MarkdownTableReformer()
    
    # Test with the balance sheet file
    with open('test\\-季度合并财报（2024年Q4）_资产负债表.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    filename = "-季度合并财报（2024年Q4）_资产负债表.md"
    
    print(f"Processing real file: {filename}")
    
    # Parse the table to see what's in the header
    table_data, separator_line, markdown_headers = reformer.parse_markdown_table(content)
    header_rows = table_data[:separator_line] if separator_line > 0 else []
    
    print(f"Header rows found: {header_rows[:3]}")  # Show first 3 rows
    
    # Test date extraction
    extracted_date = reformer.comprehensive_date_extraction(header_rows, filename)
    print(f"Extracted date: '{extracted_date}'")
    
    # Process the full table
    result = reformer.process_table(content, filename)
    
    # Extract metadata to check date extraction
    metadata_match = re.search(r'<!-- METADATA: ({.*?}) -->', result)
    if metadata_match:
        metadata = json.loads(metadata_match.group(1))
        metadata_date = metadata.get('date', None)
        
        print(f"Metadata date: '{metadata_date}'")
        
        # Check if we got a reasonable date
        # Should prioritize header content if it has a specific date, otherwise filename
        if metadata_date:
            print(f"✅ Date successfully extracted and stored in metadata")
            
            # Validate the format
            if (re.match(r'\d{4}-\d{2}-\d{2}', metadata_date) or  # Full date
                re.match(r'\d{4}年\d{1,2}月\d{1,2}日', metadata_date) or  # Chinese full date
                re.match(r'\d{4}年Q[1-4]', metadata_date) or  # Year-quarter
                re.match(r'\d{4}年\d{1,2}月', metadata_date)):  # Year-month
                print(f"✅ Date format is valid and preserved")
                return True
            else:
                print(f"❌ Date format not recognized: {metadata_date}")
                return False
        else:
            print(f"❌ No date found in metadata")
            return False
    else:
        print("❌ No metadata found in result")
        return False

def main():
    """Run all enhanced date extraction tests"""
    print("Testing Enhanced Date Extraction System")
    print("="*80)
    
    try:
        test1 = test_header_line_date_priority()
        test2 = test_split_date_extraction()
        test3 = test_date_validation()
        test4 = test_date_prioritization()
        test5 = test_real_file_integration()
        
        print("="*80)
        print("FINAL RESULTS")
        print("="*80)
        print(f"Header Line Date Priority: {'✅ PASS' if test1 else '❌ FAIL'}")
        print(f"Split Date Extraction: {'✅ PASS' if test2 else '❌ FAIL'}")
        print(f"Enhanced Date Validation: {'✅ PASS' if test3 else '❌ FAIL'}")
        print(f"Date Prioritization: {'✅ PASS' if test4 else '❌ FAIL'}")
        print(f"Real File Integration: {'✅ PASS' if test5 else '❌ FAIL'}")
        
        all_passed = test1 and test2 and test3 and test4 and test5
        
        if all_passed:
            print("\n🎉 All enhanced date extraction tests passed!")
            return 0
        else:
            print("\n❌ Some enhanced date extraction tests failed")
            return 1
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
