import logging
import re
from pathlib import Path

# Import unified error handling framework
from app.exceptions import LayerType, ErrorSeverity
from app.error_handling import log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context
from .exceptions import (
    CrawlerException, CrawlerLayerType
)

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utilities
set_layer_context("utility")


@log_and_reraise(logger, "filename sanitization")
def sanitize_filename(name: str) -> str:
    """清理文件名中的非法字符 with unified error handling"""
    set_operation_context("filename_sanitization")

    if not isinstance(name, str):
        raise CrawlerException(
            message="Invalid filename input",
            error_code="CRAWLER_UTIL_001",
            layer=CrawlerLayerType.CORE,
            details=f"Expected string, got {type(name).__name__}",
            context={'input_type': type(name).__name__, 'input_value': str(name)},
            suggested_action="Provide a valid string for filename"
        )

    if not name.strip():
        raise CrawlerException(
            message="Empty filename provided",
            error_code="CRAWLER_UTIL_002",
            layer=CrawlerLayerType.CORE,
            details="Filename cannot be empty or whitespace only",
            suggested_action="Provide a non-empty filename"
        )

    with error_boundary("filename character replacement", CrawlerLayerType.CORE):
        try:
            # 替换Windows文件名非法字符为下划线
            name = re.sub(r'[\\/:*?"<>|]', '_', name)
            # 去除首尾空白和特殊字符
            sanitized = name.strip(' _')

            if not sanitized:
                raise CrawlerException(
                    message="Filename became empty after sanitization",
                    error_code="CRAWLER_UTIL_003",
                    layer=CrawlerLayerType.CORE,
                    details=f"Original filename '{name}' resulted in empty string after sanitization",
                    context={'original_name': name},
                    suggested_action="Provide a filename with valid characters"
                )

            return sanitized
        except Exception as e:
            if isinstance(e, CrawlerException):
                raise
            raise CrawlerException(
                message="Filename sanitization failed",
                error_code="CRAWLER_UTIL_004",
                layer=CrawlerLayerType.CORE,
                details=f"Unexpected error during sanitization: {str(e)}",
                context={'original_name': name},
                original_exception=e,
                suggested_action="Check filename format and try again"
            )


@log_and_reraise(logger, "output directory creation")
def ensure_output_dir(output_dir: str):
    """确保输出目录存在 with unified error handling"""
    set_operation_context("output_directory_creation")

    if not isinstance(output_dir, str):
        raise CrawlerException(
            message="Invalid output directory input",
            error_code="CRAWLER_UTIL_005",
            layer=CrawlerLayerType.CORE,
            details=f"Expected string, got {type(output_dir).__name__}",
            context={'input_type': type(output_dir).__name__, 'input_value': str(output_dir)},
            suggested_action="Provide a valid string for output directory path"
        )

    with error_boundary("directory creation", CrawlerLayerType.CORE):
        try:
            path = Path(output_dir)
            path.mkdir(parents=True, exist_ok=True)
            logger.debug(f"确保输出目录: {output_dir}")
        except (OSError, IOError) as e:
            raise CrawlerException(
                message="Failed to create output directory",
                error_code="CRAWLER_UTIL_006",
                layer=CrawlerLayerType.CORE,
                details=f"Could not create directory '{output_dir}': {str(e)}",
                context={'output_dir': output_dir, 'error_type': type(e).__name__},
                original_exception=e,
                suggested_action="Check directory permissions and disk space"
            )
        except Exception as e:
            raise CrawlerException(
                message="Unexpected error creating output directory",
                error_code="CRAWLER_UTIL_007",
                layer=CrawlerLayerType.CORE,
                details=f"Unexpected error: {str(e)}",
                context={'output_dir': output_dir},
                original_exception=e,
                suggested_action="Check system permissions and try again"
            )
