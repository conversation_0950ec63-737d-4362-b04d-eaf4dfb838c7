# Hierarchical Table Name Extraction System

## Overview
Successfully implemented a comprehensive hierarchical table name extraction system for markdown financial tables with sophisticated priority-based processing and intelligent Chinese filename handling.

## Problem Solved
**Original Issue**: The balance sheet file `test3\-季度合并财报（2024年Q4）_资产负债表_adjusted_reformed.md` had multiple critical problems:
- **Table Name**: Truncated and malformed "-季度合并财报（ - 资产负债表"
- **Header Structure**: Wrong header row selected (应收股利 instead of 科目)
- **Metadata**: Table structure elements incorrectly included in metadata

## Solution Implemented

### 🎯 **Hierarchical Priority System**

#### **Priority 1: Table Header Lines (Highest Priority)**
```python
def extract_table_name_from_header_lines(self, header_rows: List[List[str]]) -> Optional[str]:
```
- **Scope**: Scans rows 1-5 before separator line
- **Criteria**: 
  - Exactly one non-empty cell with length > 3 characters
  - Any cell content ends with '表' (table)
  - Matches patterns: `.*表$`, `.*报表$`
- **Validation**: Must contain table-related keywords (资产负债, 利润, 现金流, 财务, 合并)

#### **Priority 2: Markdown Headers (Medium Priority)**
```python
def extract_table_name_from_markdown_headers(self, markdown_headers: List[str]) -> Optional[str]:
```
- **Scope**: First 10 lines of content
- **Filtering**: Excludes pure metadata (公司:, 单位:, 编制:, 时间:, 日期:, 币别:)
- **Criteria**: 
  - Ends with '表' character
  - Contains table type keywords
  - Matches pattern: `/^[^#]*表[^#]*$/`

#### **Priority 3: Filename (Fallback Priority)**
```python
def extract_table_name_from_filename(self, filename: str) -> Optional[str]:
```
- **Preprocessing**: Removes extensions (.md, .xlsx, .xls, .csv) and suffixes (_adjusted, _reformed, _processed, _output)
- **Excel Cell Filtering**: Strict regex `/_[A-Z]{1,3}\d{1,6}[A-Z]{0,3}\d{0,6}_/` with underscore boundaries
- **Comprehensive Extraction**: Table types + date/period information

### 🚀 **Advanced Features**

#### **Chinese Filename Prioritization**
```python
# For filenames containing Chinese characters [\u4e00-\u9fff]
if (filename_length > table_name_length or 
    has_date_info or
    any(keyword in filename_name for keyword in ['合并', '季度', '年度'])):
    return filename_name  # Override content-extracted title
```

#### **Comprehensive Date/Period Extraction**
- **Years**: 2024年, 2024, 20年, 21年
- **Quarters**: 2024年一季度, 2024年Q1, Q1-Q4, 一季度-四季度
- **Months**: 2024年09月, 2024年9月, 2024.09, 2024-09, 09月, 9月
- **Specific Dates**: 2024-09-30, 2024.09.30, 2024/09/30
- **Parenthetical**: (2024年Q4数据), (2024-09汇总), (Q3), (三季度)
- **Delimited**: _2024年_, _Q3_, _三季度_, -2024-, -Q2-, -二季度-

#### **Excel Cell Position Filtering**
- **Strict Pattern**: Must be surrounded by underscores to avoid false matches
- **Examples Excluded**: _A1E34_, _B2C25_, _AA123BB456_
- **Examples Preserved**: Q4, 一季度, 2024年Q1 (date/period indicators)

#### **Table Type Recognition**
- **Supported Types**: 资产负债表, 利润表, 现金流量表, 财务报表, 合并报表, 报表
- **Priority Order**: Most specific types first

## Test Results

### ✅ **Priority System Validation**
```
Priority 1 - Table header lines: ✅ PASS (利润表 extracted)
Priority 2 - Markdown headers: ✅ PASS (现金流量表 extracted)  
Priority 3 - Chinese filename: ✅ PASS (季度合并财报（2024年Q4）_资产负债表)
```

### ✅ **Date/Period Extraction**
```
财务报表_2024年Q3_利润表.md → ['2024年', '2024', '24年', '2024年Q3', 'Q3']
资产负债表（2024年9月）.md → ['2024年', '2024', '24年', '2024年9月', '9月']
现金流量表-2024-三季度-.md → ['2024', '三季度']
合并报表_Q4_2024年.md → ['2024年', '2024', '24年', 'Q4']
```

### ✅ **Excel Cell Position Filtering**
```
财务数据_A1E34_Sheet1.md → Cell positions: ['A1E34'] (correctly identified)
季度报表（Q4）_B2C25_数据.md → Cell positions: ['B2C25'] (correctly identified)
2024年Q4财报.md → Cell positions: [] (Q4 preserved as date indicator)
```

### ✅ **Real File Processing**
```
Input: -季度合并财报（2024年Q4）_资产负债表.md
Output: 季度合并财报（2024年Q4）_资产负债表

Quality Checks:
✅ Contains '季度合并财报'
✅ Contains '2024年Q4'  
✅ Contains '资产负债表'
✅ No truncation (length > 20)
✅ No malformed characters
```

## Before/After Comparison

### **Table Name**
- **Before**: "-季度合并财报（ - 资产负债表" (truncated, malformed)
- **After**: "季度合并财报（2024年Q4）_资产负债表" (complete, accurate)

### **Header Structure**
- **Before**: `| 应收股利 | - | 效 |` (wrong row selected)
- **After**: `| 科目 | 2024-12-31 | |` (correct header with "科目")

### **Metadata Quality**
- **Before**: `["流动资产:"]` (table structure in metadata)
- **After**: `[]` (clean metadata, no table structure)

## Integration Points

### **Enhanced Header Detection**
- **"科目" Recognition**: Strong signal for header lines (25 bonus points)
- **Multi-pass Scanning**: Evaluates ALL potential headers before selection
- **Optimal Separator Positioning**: Preserves correct position when already optimal

### **Context-Aware Classification**
- **Header vs Metadata**: Intelligent classification prevents table headers in metadata
- **Time Keyword Handling**: Context-aware analysis for "时间" classification
- **Density Analysis**: High string density rows favored as headers

## Files Modified
- ✅ `app/utils/reform.py` - Hierarchical extraction system implementation
- ✅ `test_hierarchical_extraction.py` - Comprehensive test suite
- ✅ `fix_balance_sheet.py` - Integration test and validation
- ✅ `HIERARCHICAL_TABLE_NAME_EXTRACTION_SUMMARY.md` - This documentation

## Usage
The system works transparently with existing code:

```python
from app.utils.reform import MarkdownTableReformer

reformer = MarkdownTableReformer()
result = reformer.process_table(content, filename)
# Now with intelligent hierarchical table name extraction
```

## Expected Outcomes Achieved
✅ **Priority-based extraction** - Table header lines > Markdown headers > Filename
✅ **Chinese filename prioritization** - Comprehensive info preferred over content extraction
✅ **Validation and quality checks** - Meaningful names, complete information preserved
✅ **Error handling** - Graceful fallbacks, malformed filename handling
✅ **Natural format output** - Readable table names combining type and period information

The hierarchical system now provides intelligent, comprehensive table name extraction that correctly identifies and preserves all relevant information while maintaining backward compatibility with existing workflows.
