#!/usr/bin/env python3
"""
Test script for the enhanced header detection algorithm in reheader.py
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.reheader import MarkdownTableHeaderAdjuster

def test_premature_termination_fix():
    """Test that the algorithm no longer terminates prematurely and finds the best header"""
    adjuster = MarkdownTableHeaderAdjuster()
    adjuster.debug_output = True
    
    print("=== Test Case 1: Premature Termination Fix ===")
    
    # Test case where a strong header appears after a weaker one
    test_content = """| 财务报表 | | | |
| --- | --- | --- | --- |
| 2024年第三季度 | | | |
| 编制单位：测试公司 | | 单位：万元 | |
| 项目 | 行次 | 本期金额 | 上期金额 |
| 一、营业收入 | 1 | 1000 | 900 |
| 二、营业成本 | 2 | 600 | 550 |"""
    
    print("Input table:")
    print(test_content)
    print("\n" + "="*60 + "\n")
    
    # Parse the table to test header detection
    rows = adjuster.parse_table(test_content)
    
    # Test the enhanced header detection
    candidates = adjuster.find_all_header_candidates(rows)
    print("Header candidates found:")
    for i, (row_idx, score) in enumerate(candidates):
        row_preview = ' | '.join(rows[row_idx][:4])
        print(f"  {i+1}. Row {row_idx+1}: {score:.2f} - [{row_preview}]")
    
    # Test individual scoring components
    print("\nDetailed scoring analysis:")
    for row_idx, row in enumerate(rows):
        if adjuster.is_separator_row(row):
            continue
            
        keyword_count = adjuster.count_header_keywords(row)
        has_strong = adjuster.has_strong_header_keywords(row)
        row_density = adjuster.calculate_row_density(row)
        string_density = adjuster.calculate_string_density(row)
        
        print(f"Row {row_idx+1}: keywords={keyword_count}, strong={has_strong}, "
              f"density={row_density:.2f}, string_density={string_density:.2f}")
    
    # The row with "项目" and "行次" should score highest
    if candidates:
        best_row_idx = candidates[0][0]
        best_row = rows[best_row_idx]
        print(f"\nBest header row selected: Row {best_row_idx+1}")
        print(f"Content: {best_row}")
        
        # Verify it contains strong keywords
        if '项目' in ' '.join(best_row) and '行次' in ' '.join(best_row):
            print("✅ PASS: Correctly identified row with strong header keywords")
        else:
            print("❌ FAIL: Did not identify the row with strongest header keywords")
    else:
        print("❌ FAIL: No header candidates found")
    
    print("\n" + "="*60 + "\n")

def test_row_density_analysis():
    """Test that rows with high string density are favored"""
    adjuster = MarkdownTableHeaderAdjuster()
    
    print("=== Test Case 2: Row Density Analysis ===")
    
    # Test case with varying row densities
    test_content = """| 报表名称 | | | |
| --- | --- | --- | --- |
| | | 单位：元 | |
| 项目 | 科目 | 本月金额 | 累计金额 |
| 收入 | 4001 | 50000 | 150000 |
| 支出 | 5001 | 30000 | 90000 |"""
    
    rows = adjuster.parse_table(test_content)
    
    print("Row density analysis:")
    for row_idx, row in enumerate(rows):
        if adjuster.is_separator_row(row):
            continue
            
        row_density = adjuster.calculate_row_density(row)
        string_density = adjuster.calculate_string_density(row)
        score = adjuster.calculate_comprehensive_header_score(row, row_idx, len(rows), rows)
        
        row_preview = ' | '.join(row[:4])
        print(f"Row {row_idx+1}: density={row_density:.2f}, string_density={string_density:.2f}, "
              f"score={score:.2f} - [{row_preview}]")
    
    # The row with "项目 | 科目 | 本月金额 | 累计金额" should score highest
    candidates = adjuster.find_all_header_candidates(rows)
    if candidates:
        best_row_idx = candidates[0][0]
        best_row = rows[best_row_idx]
        
        if '项目' in ' '.join(best_row) and '科目' in ' '.join(best_row):
            print("✅ PASS: High-density row with header keywords selected")
        else:
            print("❌ FAIL: Did not select the high-density header row")
    else:
        print("❌ FAIL: No candidates found")
    
    print("\n" + "="*60 + "\n")

def test_full_algorithm():
    """Test the complete enhanced algorithm"""
    adjuster = MarkdownTableHeaderAdjuster()
    adjuster.debug_output = False  # Reduce noise for this test
    
    print("=== Test Case 3: Full Algorithm Test ===")
    
    # Complex test case that should challenge the algorithm
    test_content = """# 财务数据表
| 利润表 | | | | |
| --- | --- | --- | --- | --- |
| 2024年9月 | | | 单位：万元 | |
| 编制：财务部 | | | | |
| 项目 | 行次 | 本期金额 | 上期金额 | 备注 |
| 一、营业收入 | 1 | 1000.5 | 900.2 | |
| 减：营业成本 | 2 | 600.3 | 550.1 | |
| 二、营业利润 | 3 | 400.2 | 350.1 | |"""
    
    print("Processing complex table...")
    result = adjuster.adjust_table(test_content)
    
    print("Result:")
    print(result)
    
    # Check if the separator is positioned correctly after the strong header row
    result_lines = result.split('\n')
    header_row_found = False
    separator_after_header = False
    
    for i, line in enumerate(result_lines):
        if '项目' in line and '行次' in line:
            header_row_found = True
            # Check if next line is separator
            if i + 1 < len(result_lines) and '---' in result_lines[i + 1]:
                separator_after_header = True
            break
    
    if header_row_found and separator_after_header:
        print("✅ PASS: Separator correctly positioned after strong header row")
    else:
        print("❌ FAIL: Separator not positioned correctly")
        print(f"Header found: {header_row_found}, Separator after: {separator_after_header}")
    
    print("\n" + "="*60 + "\n")

def test_keyword_scoring():
    """Test that header keywords are properly weighted"""
    adjuster = MarkdownTableHeaderAdjuster()
    
    print("=== Test Case 4: Header Keyword Scoring ===")
    
    # Test different keyword combinations
    test_rows = [
        ['标题', '', '', ''],  # Weak
        ['项目', '金额', '', ''],  # Strong keyword
        ['序号', '项目', '行次', '金额'],  # Multiple strong keywords
        ['数据', '信息', '内容', '备注'],  # No keywords
    ]
    
    print("Keyword scoring test:")
    for i, row in enumerate(test_rows):
        keyword_count = adjuster.count_header_keywords(row)
        has_strong = adjuster.has_strong_header_keywords(row)
        score = adjuster.calculate_comprehensive_header_score(row, i, len(test_rows), test_rows)
        
        row_text = ' | '.join(row)
        print(f"Row {i+1}: keywords={keyword_count}, strong={has_strong}, "
              f"score={score:.2f} - [{row_text}]")
    
    # Row with multiple strong keywords should score highest
    scores = []
    for i, row in enumerate(test_rows):
        score = adjuster.calculate_comprehensive_header_score(row, i, len(test_rows), test_rows)
        scores.append(score)
    
    max_score_idx = scores.index(max(scores))
    if max_score_idx == 2:  # Row with '序号', '项目', '行次', '金额'
        print("✅ PASS: Row with multiple strong keywords scored highest")
    else:
        print(f"❌ FAIL: Expected row 3 to score highest, but row {max_score_idx + 1} did")
    
    print("\n" + "="*60 + "\n")

def main():
    """Run all tests"""
    print("Testing Enhanced Header Detection Algorithm")
    print("="*60)
    
    try:
        test_premature_termination_fix()
        test_row_density_analysis()
        test_keyword_scoring()
        test_full_algorithm()
        
        print("🎉 All tests completed!")
        return 0
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
