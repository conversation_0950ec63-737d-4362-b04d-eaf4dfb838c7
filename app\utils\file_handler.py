import logging
import os
from pathlib import Path
from typing import List

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity, ValidationException,
    FileProcessingException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary
from app.logging_config import setup_logging, set_layer_context, set_operation_context

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

# Set layer context for utility operations
set_layer_context("utility")

@log_and_reraise(logger, "directory creation")
def ensure_directory_exists(path: str) -> str:
    """确保目录存在，如果不存在则创建 with unified error handling"""
    set_operation_context("directory_creation")

    with error_boundary("directory validation and creation", LayerType.UTILITY):
        if not path:
            raise ValidationException(
                message="Empty path provided",
                field_name="path",
                details="Path cannot be empty or None",
                suggested_action="Provide a valid directory path"
            )

        try:
            os.makedirs(path, exist_ok=True)
            logger.info(f"Ensure工作区输出目录存在: {path}")
            return path
        except (OSError, PermissionError) as e:
            raise FileProcessingException(
                message="Failed to create directory",
                file_path=path,
                details=f"Directory creation error: {str(e)}",
                original_exception=e,
                suggested_action="Check directory permissions and disk space"
            )

@log_and_reraise(logger, "file validation")
def get_valid_files(directory: str, extensions: List[str]) -> List[str]:
    """获取目录中指定扩展名的文件 with unified error handling"""
    set_operation_context("file_validation")

    with error_boundary("input validation", LayerType.UTILITY):
        if not directory:
            raise ValidationException(
                message="Empty directory path provided",
                field_name="directory",
                details="Directory path cannot be empty or None",
                suggested_action="Provide a valid directory path"
            )

        if not extensions:
            raise ValidationException(
                message="Empty extensions list provided",
                field_name="extensions",
                details="Extensions list cannot be empty or None",
                suggested_action="Provide a list of valid file extensions"
            )

    with error_boundary("file listing", LayerType.UTILITY):
        if not os.path.exists(directory):
            return []

        try:
            return [
                f for f in os.listdir(directory)
                if any(f.lower().endswith(ext.lower()) for ext in extensions)
            ]
        except (OSError, PermissionError) as e:
            raise FileProcessingException(
                message="Failed to list directory contents",
                file_path=directory,
                details=f"Directory listing error: {str(e)}",
                original_exception=e,
                suggested_action="Check directory permissions and accessibility"
            )

@log_and_reraise(logger, "file upload")
def save_uploaded_file(uploaded_file, save_path: str) -> str:
    """保存上传的文件 with unified error handling"""
    set_operation_context("file_upload")

    with error_boundary("input validation", LayerType.UTILITY):
        if not uploaded_file:
            raise ValidationException(
                message="No uploaded file provided",
                field_name="uploaded_file",
                details="Uploaded file cannot be None",
                suggested_action="Provide a valid uploaded file object"
            )

        if not save_path:
            raise ValidationException(
                message="Empty save path provided",
                field_name="save_path",
                details="Save path cannot be empty or None",
                suggested_action="Provide a valid file save path"
            )

    with error_boundary("file saving", LayerType.UTILITY):
        try:
            ensure_directory_exists(os.path.dirname(save_path))

            with open(save_path, "wb") as f:
                f.write(uploaded_file.getbuffer())

            return save_path

        except (IOError, OSError) as e:
            raise FileProcessingException(
                message="Failed to save uploaded file",
                file_path=save_path,
                details=f"File saving error: {str(e)}",
                original_exception=e,
                suggested_action="Check file path and disk space"
            )
