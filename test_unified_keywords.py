#!/usr/bin/env python3
"""
Test the unified keyword system
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_manual_keyword_system():
    """Test the keyword system manually without importing the class"""
    print("=== Manual Keyword System Test ===")
    
    # Test the keyword priorities manually
    keyword_priorities = {
        # High-priority keywords (1.5x weight)
        '项目': 1.5,
        '科目': 1.5, 
        '行次': 1.5,
        '序号': 1.5,
        
        # Medium-priority keywords (1.2x weight)
        '日期': 1.2,
        '时间': 1.2,
        '月度': 1.2,
        '期间': 1.2,
        
        # Standard keywords (1.0x weight)
        '本月金额': 1.0,
        '本季金额': 1.0,
        '本年金额': 1.0,
        '本月发生': 1.0,
        '本季发生': 1.0,
        '本年发生': 1.0,
        '本月累计': 1.0,
        '本季累计': 1.0,
        '本年累计': 1.0,
    }
    
    print(f"Keyword priorities: {len(keyword_priorities)} keywords")
    print("High-priority keywords:", [k for k, v in keyword_priorities.items() if v >= 1.5])
    print("Medium-priority keywords:", [k for k, v in keyword_priorities.items() if v == 1.2])
    print("Standard keywords:", [k for k, v in keyword_priorities.items() if v == 1.0])
    
    # Test keyword scoring manually
    def calculate_keyword_score_manual(row):
        if not row:
            return 0.0
        
        row_text = ' '.join(row)
        total_score = 0.0
        max_possible_score = sum(keyword_priorities.values())
        
        for keyword, priority_weight in keyword_priorities.items():
            if keyword in row_text:
                total_score += priority_weight
        
        return total_score / max_possible_score if max_possible_score > 0 else 0.0
    
    # Test cases
    test_rows = [
        ['项目', '本月数', '本年累计'],  # High-priority keyword
        ['日期', '时间', '备注'],       # Medium-priority keywords
        ['本月金额', '本年金额'],       # Standard keywords
        ['名称', '数量', '金额'],       # No keywords
        ['科目', '日期', '本月累计'],   # Mixed priority keywords
    ]
    
    print("\n=== Manual Keyword Score Testing ===")
    for i, row in enumerate(test_rows):
        score = calculate_keyword_score_manual(row)
        print(f'Row {i+1}: {row} -> Score: {score:.4f}')
    
    print("\n✅ Manual keyword system test completed")

def test_import_only():
    """Test just the import without creating objects"""
    print("\n=== Testing Import Only ===")
    
    try:
        print("Step 1: Testing import...")
        import app.utils.reheader
        print("✅ Module import successful")
        
        print("Step 2: Testing class access...")
        cls = app.utils.reheader.MarkdownTableHeaderAdjuster
        print("✅ Class access successful")
        
        print("Step 3: Testing object creation...")
        # This is where it might hang
        obj = cls()
        print("✅ Object creation successful")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_manual_keyword_system()
    test_import_only()
    print("\n🎉 All tests completed!")
