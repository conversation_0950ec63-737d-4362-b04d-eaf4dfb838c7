import typer
from md_splitter import process_file, process_directory

app = typer.Typer(help="Markdown表格分割工具")

@app.command()
def file(
    input_path: str = typer.Argument(..., help="输入文件路径"),
    output_dir: str = typer.Option(None, "--output-dir", "-o", help="输出目录"),
):
    """处理单个Markdown文件"""
    count = process_file(input_path, output_dir)
    typer.echo(f"成功处理 {input_path}，生成 {count} 个文件")

@app.command()
def directory(
    input_dir: str = typer.Argument(..., help="输入目录路径"),
    output_dir: str = typer.Option(None, "--output-dir", "-o", help="输出目录"),
):
    """批量处理目录中的Markdown文件"""
    total = process_directory(input_dir, output_dir)
    typer.echo(f"成功处理 {input_dir} 中的 {total} 个文件")

if __name__ == "__main__":
    app()
