import logging
import re
from pathlib import Path
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class MarkdownProcessor:
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {"output_dir": "split_results"}
        self.saved_files = 0

    @staticmethod
    def is_valid_table(lines: List[str]) -> bool:
        if len(lines) < 2:
            return False
        header_separator = re.compile(
            r'^\|?(\s*:?-+:?\s*\|)+(\s*:?-+:?\s*)\|?$'
        )
        return any(header_separator.match(line.strip()) for line in lines[1:])

    @staticmethod
    def is_valid_text(content: str) -> bool:
        line_separator = re.compile(r'^(?![#*]).*')
        return any(line_separator.match(line.strip()) 
                 for line in content.split('\n') if line.strip())

    @staticmethod
    def sanitize_filename(title: str) -> str:
        return re.sub(r'[#<>:"/\\|?*]', '', title).replace(' ', '_').strip('_')

    def process_file(self, input_path: str, output_dir: str = None) -> List[str]:
        output_dir = output_dir or self.config['output_dir']
        buffer_lines = []
        table_stack = []
        current_table = None
        base_name = Path(input_path).stem
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        created_files = []
        logger.debug(f"创建处理目录: {output_dir}")
        
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read().split('\n')
        
        tname = None
        
        for line in content:
            # 处理标题（## 级）
            if heading_match := re.match(r'^([#*]+)(.+)$', line):
                # 遇到新标题时处理栈中表格
                if len(table_stack) > 1:
                    table_data = table_stack.pop()
                    if self.is_valid_table(table_data['content']):
                        self.saved_files += 1
                        safe_title = f"{self.sanitize_filename(table_data['title'])}_{self.saved_files}"
                        out_path = output_path / f'{base_name}_{safe_title}.md'
                        out_path.write_text('\n'.join([table_data['title'], '\n'.join(table_data['content'])]), encoding='utf-8')
                        created_files.append(str(out_path))

                # 新标题进入缓冲区
                buffer_lines.append(line)

                if re.match(r'(.+表).*', line):
                    tname = line
                    current_table = None
                continue
            
            # 处理表格行
            if line.strip().startswith('|'):
                if not current_table:  # 新表格开始
                    if tname:
                        table_stack.append({
                            'type': 'heading',
                            'title': tname,
                            'content': []
                        })
                        tname = None
                    else:
                        table_stack.append({
                            'type': 'heading',
                            'title': buffer_lines[-1],
                            'content': []
                        })
                    current_table = table_stack.pop()

                current_table['content'].append(line)
                continue

            # 表格结束处理
            if current_table:
                if self.is_valid_table(current_table['content']):
                    table_stack.append(current_table)
                current_table = None

            # 保留非空内容
            if line.strip():
                buffer_lines.append(line)

        if current_table:
            # 验证并保存表格
            if self.is_valid_table(current_table['content']):
                table_stack.append(current_table)
            current_table = None

        # 处理剩余表格
        while len(table_stack) > 1:
            table_data = table_stack.pop()
            if self.is_valid_table(table_data['content']):
                self.saved_files += 1
                safe_title = f"{self.sanitize_filename(table_data['title'])}_{self.saved_files}"
                out_path = output_path / f'{base_name}_{safe_title}.md'
                out_path.write_text('\n'.join([table_data['title'], '\n'.join(table_data['content'])]), encoding='utf-8')
                created_files.append(str(out_path))

        # 处理最终内容
        if self.is_valid_text('\n'.join(buffer_lines)):
            Path(input_path).write_text('\n'.join(buffer_lines), encoding='utf-8')
            if table_stack:
                table_data = table_stack.pop()
                if self.is_valid_table(table_data['content']):
                    self.saved_files += 1
                    safe_title = f"{self.sanitize_filename(table_data['title'])}_{self.saved_files}"
                    out_path = output_path / f'{base_name}_{safe_title}.md'
                    out_path.write_text('\n'.join([table_data['title'], '\n'.join(table_data['content'])]), encoding='utf-8')
                    created_files.append(str(out_path))
        else:
            if table_stack:
                table_data = table_stack.pop()
                if self.is_valid_table(table_data['content']):
                    self.saved_files += 1
                    Path(input_path).write_text('\n'.join([table_data['title'], '\n'.join(table_data['content'])]), encoding='utf-8')

        
        return created_files

def process_file(input_path: str, output_dir: str = None) -> List[str]:
    return MarkdownProcessor().process_file(input_path, output_dir)

def process_directory(input_dir: str, output_dir: str = None) -> List[str]:
    processor = MarkdownProcessor()
    created_files = []
    for path in Path(input_dir).glob('*.md'):
        created_files.extend(processor.process_file(str(path), output_dir))
    return created_files
